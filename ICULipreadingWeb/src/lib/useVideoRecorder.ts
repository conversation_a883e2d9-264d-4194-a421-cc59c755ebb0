import { useState, useEffect, useRef, useCallback } from 'react';

interface UseVideoRecorderProps {
  stream: MediaStream | null;
  videoRef: React.RefObject<HTMLVideoElement>;
}

interface UseVideoRecorderReturn {
  isRecording: boolean;
  recordedVideoUrl: string | null;
  startRecording: () => void;
  stopRecording: () => void;
  error: Error | null;
}

const useVideoRecorder = ({ stream, videoRef }: UseVideoRecorderProps): UseVideoRecorderReturn => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordedVideoUrl, setRecordedVideoUrl] = useState<string | null>(null);
  const [error, setError] = useState<Error | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  const startRecording = useCallback(() => {
    if (!stream) {
      setError(new Error('No camera stream available'));
      return;
    }

    try {
      chunksRef.current = [];
      const options = { mimeType: 'video/webm;codecs=vp9,opus' };
      mediaRecorderRef.current = new MediaRecorder(stream, options);
      
      // Capture data more frequently to make video smoother
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };
      
      // Request data every 200ms instead of waiting for segments
      mediaRecorderRef.current.start(200);

      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        setRecordedVideoUrl(url);
        setIsRecording(false);
      };

      // Start already called with timeslice parameter above
      setIsRecording(true);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Error starting recording'));
    }
  }, [stream]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
    }
  }, [isRecording]);

  // Clean up URLs to prevent memory leaks
  useEffect(() => {
    return () => {
      if (recordedVideoUrl) {
        URL.revokeObjectURL(recordedVideoUrl);
      }
    };
  }, [recordedVideoUrl]);

  return {
    isRecording,
    recordedVideoUrl,
    startRecording,
    stopRecording,
    error
  };
};

export default useVideoRecorder;
