{"version": 3, "sources": ["../../src/mutationOptions.ts"], "sourcesContent": ["import type { DefaultError, WithRequired } from '@tanstack/query-core'\nimport type { UseMutationOptions } from './types'\n\nexport function mutationOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TOnMutateResult = unknown,\n>(\n  options: WithRequired<\n    UseMutationOptions<TData, TError, TVariables, TOnMutateResult>,\n    'mutationKey'\n  >,\n): WithRequired<\n  UseMutationOptions<TData, TError, TVariables, TOnMutateResult>,\n  'mutationKey'\n>\nexport function mutationOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TOnMutateResult = unknown,\n>(\n  options: Omit<\n    UseMutationOptions<TData, TError, TVariables, TOnMutateResult>,\n    'mutationKey'\n  >,\n): Omit<\n  UseMutationOptions<TData, TError, TVariables, TOnMutateResult>,\n  'mutationKey'\n>\nexport function mutationOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TOnMutateResult = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TOnMutateResult>,\n): UseMutationOptions<TData, TError, TVariables, TOnMutateResult> {\n  return options\n}\n"], "mappings": ";AA+BO,SAAS,gBAMd,SACgE;AAChE,SAAO;AACT;", "names": []}