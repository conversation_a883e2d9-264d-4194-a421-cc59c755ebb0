{"version": 3, "sources": ["../../src/types.ts"], "sourcesContent": ["/* istanbul ignore file */\n\nimport type {\n  DefaultError,\n  DefinedInfiniteQueryObserverResult,\n  DefinedQueryObserverResult,\n  DistributiveOmit,\n  FetchQueryOptions,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  MutateFunction,\n  MutationObserverOptions,\n  MutationObserverResult,\n  OmitKeyof,\n  Override,\n  QueryKey,\n  QueryObserverOptions,\n  QueryObserverResult,\n  SkipToken,\n} from '@tanstack/query-core'\n\nexport type AnyUseBaseQueryOptions = UseBaseQueryOptions<\n  any,\n  any,\n  any,\n  any,\n  any\n>\nexport interface UseBaseQueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQuery<PERSON><PERSON>\n  > {\n  /**\n   * Set this to `false` to unsubscribe this observer from updates to the query cache.\n   * Defaults to `true`.\n   */\n  subscribed?: boolean\n}\n\nexport interface UsePrefetchQueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends OmitKeyof<\n    FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryFn'\n  > {\n  queryFn?: Exclude<\n    FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>['queryFn'],\n    SkipToken\n  >\n}\n\nexport type AnyUseQueryOptions = UseQueryOptions<any, any, any, any>\nexport interface UseQueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends OmitKeyof<\n    UseBaseQueryOptions<TQueryFnData, TError, TData, TQueryFnData, TQueryKey>,\n    'suspense'\n  > {}\n\nexport type AnyUseSuspenseQueryOptions = UseSuspenseQueryOptions<\n  any,\n  any,\n  any,\n  any\n>\nexport interface UseSuspenseQueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryFn' | 'enabled' | 'throwOnError' | 'placeholderData'\n  > {\n  queryFn?: Exclude<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>['queryFn'],\n    SkipToken\n  >\n}\n\nexport type AnyUseInfiniteQueryOptions = UseInfiniteQueryOptions<\n  any,\n  any,\n  any,\n  any,\n  any\n>\nexport interface UseInfiniteQueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> extends OmitKeyof<\n    InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n    'suspense'\n  > {\n  /**\n   * Set this to `false` to unsubscribe this observer from updates to the query cache.\n   * Defaults to `true`.\n   */\n  subscribed?: boolean\n}\n\nexport type AnyUseSuspenseInfiniteQueryOptions =\n  UseSuspenseInfiniteQueryOptions<any, any, any, any, any>\nexport interface UseSuspenseInfiniteQueryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n> extends OmitKeyof<\n    UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>,\n    'queryFn' | 'enabled' | 'throwOnError' | 'placeholderData'\n  > {\n  queryFn?: Exclude<\n    UseInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >['queryFn'],\n    SkipToken\n  >\n}\n\nexport type UseBaseQueryResult<\n  TData = unknown,\n  TError = DefaultError,\n> = QueryObserverResult<TData, TError>\n\nexport type UseQueryResult<\n  TData = unknown,\n  TError = DefaultError,\n> = UseBaseQueryResult<TData, TError>\n\nexport type UseSuspenseQueryResult<\n  TData = unknown,\n  TError = DefaultError,\n> = DistributiveOmit<\n  DefinedQueryObserverResult<TData, TError>,\n  'isPlaceholderData' | 'promise'\n>\n\nexport type DefinedUseQueryResult<\n  TData = unknown,\n  TError = DefaultError,\n> = DefinedQueryObserverResult<TData, TError>\n\nexport type UseInfiniteQueryResult<\n  TData = unknown,\n  TError = DefaultError,\n> = InfiniteQueryObserverResult<TData, TError>\n\nexport type DefinedUseInfiniteQueryResult<\n  TData = unknown,\n  TError = DefaultError,\n> = DefinedInfiniteQueryObserverResult<TData, TError>\n\nexport type UseSuspenseInfiniteQueryResult<\n  TData = unknown,\n  TError = DefaultError,\n> = OmitKeyof<\n  DefinedInfiniteQueryObserverResult<TData, TError>,\n  'isPlaceholderData' | 'promise'\n>\n\nexport type AnyUseMutationOptions = UseMutationOptions<any, any, any, any>\nexport interface UseMutationOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TOnMutateResult = unknown,\n> extends OmitKeyof<\n    MutationObserverOptions<TData, TError, TVariables, TOnMutateResult>,\n    '_defaulted'\n  > {}\n\nexport type UseMutateFunction<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TOnMutateResult = unknown,\n> = (\n  ...args: Parameters<\n    MutateFunction<TData, TError, TVariables, TOnMutateResult>\n  >\n) => void\n\nexport type UseMutateAsyncFunction<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TOnMutateResult = unknown,\n> = MutateFunction<TData, TError, TVariables, TOnMutateResult>\n\nexport type UseBaseMutationResult<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TOnMutateResult = unknown,\n> = Override<\n  MutationObserverResult<TData, TError, TVariables, TOnMutateResult>,\n  { mutate: UseMutateFunction<TData, TError, TVariables, TOnMutateResult> }\n> & {\n  mutateAsync: UseMutateAsyncFunction<\n    TData,\n    TError,\n    TVariables,\n    TOnMutateResult\n  >\n}\n\nexport type UseMutationResult<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TOnMutateResult = unknown,\n> = UseBaseMutationResult<TData, TError, TVariables, TOnMutateResult>\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}