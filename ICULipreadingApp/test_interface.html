<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Lipreading App - Test Interface</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f7;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section h2 {
            color: #1d1d1f;
            margin-top: 0;
            border-bottom: 2px solid #007AFF;
            padding-bottom: 8px;
        }
        
        .button-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 8px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .medical-phrases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }
        
        .phrase-item {
            background: #e3f2fd;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
            font-size: 14px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007AFF;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏥 ICU Lipreading App - Test Interface</h1>
        <p>Test your lipreading backend and model integration</p>
        <div id="connection-status">
            <span id="status-indicator" class="status loading">Checking Connection...</span>
        </div>
    </div>

    <!-- Backend Health Tests -->
    <div class="test-section">
        <h2>🔍 Backend Health Tests</h2>
        <div class="button-group">
            <button onclick="testHealth()">Health Check</button>
            <button onclick="testModelInfo()">Model Information</button>
            <button onclick="testConnection()">Test Connection</button>
        </div>
        <div id="health-results" class="results">Click a button above to test backend health...</div>
    </div>

    <!-- Lipreading Simulation Tests -->
    <div class="test-section">
        <h2>🤖 Lipreading Simulation Tests</h2>
        <div class="button-group">
            <button onclick="testSimulation()">Single Simulation</button>
            <button onclick="testMultipleSimulations()">Multiple Tests (5x)</button>
            <button onclick="testMedicalPhrases()">Get Medical Phrases</button>
        </div>
        <div id="simulation-results" class="results">Click a button above to test lipreading simulation...</div>
    </div>

    <!-- Performance Tests -->
    <div class="test-section">
        <h2>⚡ Performance Tests</h2>
        <div class="button-group">
            <button onclick="testResponseTime()">Response Time Test</button>
            <button onclick="testLoadTest()">Load Test (10 concurrent)</button>
            <button onclick="testErrorHandling()">Error Handling</button>
        </div>
        <div id="performance-results" class="results">Click a button above to test performance...</div>
    </div>

    <!-- System Statistics -->
    <div class="test-section">
        <h2>📊 System Statistics</h2>
        <div class="stats" id="system-stats">
            <div class="stat-card">
                <div class="stat-value" id="model-status">-</div>
                <div class="stat-label">Model Status</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="vocab-size">-</div>
                <div class="stat-label">Vocabulary Size</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="phrase-count">-</div>
                <div class="stat-label">Medical Phrases</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="response-time">-</div>
                <div class="stat-label">Avg Response (ms)</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001';
        let responseTimeSum = 0;
        let responseTimeCount = 0;

        // Utility functions
        function updateStatus(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `status ${type}`;
        }

        function displayResults(elementId, data, title = '') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const content = title ? `[${timestamp}] ${title}\n${JSON.stringify(data, null, 2)}\n\n` : 
                                   `[${timestamp}]\n${JSON.stringify(data, null, 2)}\n\n`;
            element.textContent = content + element.textContent;
        }

        function measureResponseTime(startTime) {
            const responseTime = Date.now() - startTime;
            responseTimeSum += responseTime;
            responseTimeCount++;
            document.getElementById('response-time').textContent = 
                Math.round(responseTimeSum / responseTimeCount) + 'ms';
            return responseTime;
        }

        // Test functions
        async function testHealth() {
            const startTime = Date.now();
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                const responseTime = measureResponseTime(startTime);
                
                displayResults('health-results', {
                    ...data,
                    response_time_ms: responseTime
                }, 'Health Check');
                
                updateStatus('status-indicator', 
                    data.model_loaded ? 'Connected & Model Loaded' : 'Connected (No Model)', 
                    data.model_loaded ? 'success' : 'error');
                    
                document.getElementById('model-status').textContent = 
                    data.model_loaded ? '✅ Loaded' : '❌ Not Loaded';
                    
            } catch (error) {
                displayResults('health-results', { error: error.message }, 'Health Check Error');
                updateStatus('status-indicator', 'Connection Failed', 'error');
            }
        }

        async function testModelInfo() {
            const startTime = Date.now();
            try {
                const response = await fetch(`${API_BASE}/model_info`);
                const data = await response.json();
                measureResponseTime(startTime);
                
                displayResults('health-results', data, 'Model Information');
                
                document.getElementById('vocab-size').textContent = 
                    data.vocabulary_size ? data.vocabulary_size.toLocaleString() : '-';
                    
            } catch (error) {
                displayResults('health-results', { error: error.message }, 'Model Info Error');
            }
        }

        async function testConnection() {
            updateStatus('status-indicator', 'Testing Connection...', 'loading');
            await testHealth();
        }

        async function testSimulation() {
            const startTime = Date.now();
            try {
                const response = await fetch(`${API_BASE}/simulate`, { method: 'POST' });
                const data = await response.json();
                const responseTime = measureResponseTime(startTime);
                
                displayResults('simulation-results', {
                    ...data,
                    response_time_ms: responseTime
                }, 'Simulation Test');
                
            } catch (error) {
                displayResults('simulation-results', { error: error.message }, 'Simulation Error');
            }
        }

        async function testMultipleSimulations() {
            const results = [];
            const startTime = Date.now();
            
            for (let i = 1; i <= 5; i++) {
                try {
                    const response = await fetch(`${API_BASE}/simulate`, { method: 'POST' });
                    const data = await response.json();
                    results.push({
                        test: i,
                        prediction: data.prediction,
                        confidence: data.confidence,
                        method: data.method
                    });
                } catch (error) {
                    results.push({ test: i, error: error.message });
                }
            }
            
            const totalTime = Date.now() - startTime;
            displayResults('simulation-results', {
                total_tests: 5,
                total_time_ms: totalTime,
                avg_time_ms: Math.round(totalTime / 5),
                results: results
            }, 'Multiple Simulations');
        }

        async function testMedicalPhrases() {
            const startTime = Date.now();
            try {
                const response = await fetch(`${API_BASE}/medical_phrases`);
                const data = await response.json();
                measureResponseTime(startTime);
                
                displayResults('simulation-results', data, 'Medical Phrases');
                document.getElementById('phrase-count').textContent = data.count || '-';
                
                // Display phrases in a nice format
                const phrasesContainer = document.createElement('div');
                phrasesContainer.className = 'medical-phrases';
                phrasesContainer.innerHTML = data.phrases.map(phrase => 
                    `<div class="phrase-item">"${phrase}"</div>`
                ).join('');
                
                const resultsElement = document.getElementById('simulation-results');
                resultsElement.appendChild(phrasesContainer);
                
            } catch (error) {
                displayResults('simulation-results', { error: error.message }, 'Medical Phrases Error');
            }
        }

        async function testResponseTime() {
            const tests = 10;
            const times = [];
            
            for (let i = 0; i < tests; i++) {
                const startTime = Date.now();
                try {
                    await fetch(`${API_BASE}/health`);
                    times.push(Date.now() - startTime);
                } catch (error) {
                    times.push(-1);
                }
            }
            
            const validTimes = times.filter(t => t > 0);
            const avgTime = validTimes.reduce((a, b) => a + b, 0) / validTimes.length;
            const minTime = Math.min(...validTimes);
            const maxTime = Math.max(...validTimes);
            
            displayResults('performance-results', {
                tests_run: tests,
                successful_tests: validTimes.length,
                average_response_ms: Math.round(avgTime),
                min_response_ms: minTime,
                max_response_ms: maxTime,
                individual_times: times
            }, 'Response Time Test');
        }

        async function testLoadTest() {
            const concurrent = 10;
            const promises = [];
            const startTime = Date.now();
            
            for (let i = 0; i < concurrent; i++) {
                promises.push(
                    fetch(`${API_BASE}/simulate`, { method: 'POST' })
                        .then(r => r.json())
                        .catch(e => ({ error: e.message }))
                );
            }
            
            const results = await Promise.all(promises);
            const totalTime = Date.now() - startTime;
            const successful = results.filter(r => !r.error).length;
            
            displayResults('performance-results', {
                concurrent_requests: concurrent,
                successful_requests: successful,
                failed_requests: concurrent - successful,
                total_time_ms: totalTime,
                avg_time_per_request: Math.round(totalTime / concurrent),
                results: results
            }, 'Load Test');
        }

        async function testErrorHandling() {
            const tests = [
                { name: 'Invalid Endpoint', url: `${API_BASE}/invalid_endpoint` },
                { name: 'Invalid Method', url: `${API_BASE}/health`, method: 'DELETE' },
                { name: 'Malformed Request', url: `${API_BASE}/process_video`, method: 'POST' }
            ];
            
            const results = [];
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, { 
                        method: test.method || 'GET',
                        headers: test.headers || {}
                    });
                    const data = await response.text();
                    results.push({
                        test: test.name,
                        status: response.status,
                        response: data.substring(0, 200)
                    });
                } catch (error) {
                    results.push({
                        test: test.name,
                        error: error.message
                    });
                }
            }
            
            displayResults('performance-results', {
                error_handling_tests: results
            }, 'Error Handling Test');
        }

        // Initialize on page load
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
