{"expo": {"name": "SMHS Lipreader", "slug": "smhs-lipreader", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "sdkVersion": "54.0.0", "splash": {"backgroundColor": "#007AFF", "resizeMode": "contain"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.smhs.lipreader", "infoPlist": {"NSCameraUsageDescription": "SMHS Lipreader needs camera access to record videos for TRUE lipreading analysis.", "NSMicrophoneUsageDescription": "SMHS Lipreader may need microphone access for video recording."}}, "android": {"package": "com.smhs.lipreader", "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"]}, "plugins": [["expo-camera", {"cameraPermission": "Allow SMHS Lipreader to access your camera for TRUE lipreading analysis."}]]}}