{"expo": {"name": "ICU Lipreading App", "slug": "icu-lipreading-app", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "splash": {"backgroundColor": "#007AFF", "resizeMode": "contain"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.hospital.iculipreading", "infoPlist": {"NSCameraUsageDescription": "This app needs camera access to record videos for lipreading to help ICU patients communicate.", "NSMicrophoneUsageDescription": "This app may need microphone access for video recording to help ICU patients communicate."}}, "android": {"package": "com.hospital.iculipreading", "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"]}, "plugins": [["expo-camera", {"cameraPermission": "Allow ICU Lipreading App to access your camera to record videos for lipreading communication."}]]}}