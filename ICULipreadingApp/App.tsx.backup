import React, { useState } from 'react';
import {
  Safe<PERSON>reaView,
  StatusBar,
  StyleSheet,
  useColorScheme,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Colors } from 'react-native/Libraries/NewAppScreen';

// Import screens
import WelcomeScreenMinimal from './src/screens/WelcomeScreenMinimal';
import MainInterfaceMinimal from './src/screens/MainInterfaceMinimal';
import ResultsModalMinimal from './src/components/ResultsModalMinimal';
import ErrorModalMinimal from './src/components/ErrorModalMinimal';

const Stack = createStackNavigator();

function App(): JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [prediction, setPrediction] = useState('');
  const [recordedVideoUrl, setRecordedVideoUrl] = useState<string | null>(null);

  const backgroundStyle = {
    backgroundColor: isDarkMode ? Colors.darker : Colors.lighter,
    flex: 1,
  };

  const handleShowResults = (prediction: string, videoUrl: string) => {
    setPrediction(prediction);
    setRecordedVideoUrl(videoUrl);
    setShowResultsModal(true);
  };

  const handleShowError = (message: string) => {
    setErrorMessage(message);
    setShowErrorModal(true);
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Welcome"
          screenOptions={{
            headerShown: false,
          }}>
          <Stack.Screen name="Welcome" component={WelcomeScreenMinimal} />
          <Stack.Screen name="MainInterface">
            {(props) => (
              <MainInterfaceMinimal
                {...props}
                onShowResults={handleShowResults}
                onError={handleShowError}
              />
            )}
          </Stack.Screen>
        </Stack.Navigator>
      </NavigationContainer>

      {showResultsModal && (
        <ResultsModalMinimal
          prediction={prediction}
          videoUrl={recordedVideoUrl}
          onClose={() => setShowResultsModal(false)}
          onRetry={() => {
            setShowResultsModal(false);
          }}
          onRecordAgain={() => {
            setShowResultsModal(false);
          }}
        />
      )}

      {showErrorModal && (
        <ErrorModalMinimal
          message={errorMessage}
          onClose={() => setShowErrorModal(false)}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
