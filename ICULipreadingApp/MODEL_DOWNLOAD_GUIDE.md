# Model Download Guide

## 🎯 Required Model Files

The ICU Lipreading App requires two main model files to function:

1. **LRS3_V_WER19.1** - Main lipreading model (~2-3 GB)
2. **lm_en_subword** - Language model for text correction (~500 MB)

## 📥 Download Methods

### Method 1: Direct Google Drive Download (Recommended)

#### Step 1: Download LRS3_V_WER19.1
1. Go to: https://drive.google.com/file/d/1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6/view
2. Click "Download" button (you may need to click "Download anyway" if warned about file size)
3. Save as `LRS3_V_WER19.1.zip`

#### Step 2: Download Language Model
1. Go to: https://drive.google.com/file/d/1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv/view
2. Click "Download" button
3. Save as `lm_en_subword.zip`

#### Step 3: Extract and Place Files
```bash
# Navigate to your project
cd ICULipreadingApp/backend

# Create models directory
mkdir -p models

# Extract the main model
unzip ~/Downloads/LRS3_V_WER19.1.zip -d models/

# Extract the language model
unzip ~/Downloads/lm_en_subword.zip -d models/

# Verify structure
ls -la models/
# Should show:
# models/LRS3_V_WER19.1/
# models/lm_en_subword/
```

### Method 2: Using gdown (Python tool)

If direct download doesn't work, use `gdown`:

```bash
# Install gdown
pip install gdown

# Download LRS3 model
gdown https://drive.google.com/uc?id=1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6

# Download language model
gdown https://drive.google.com/uc?id=1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv

# Extract files
unzip LRS3_V_WER19.1.zip -d models/
unzip lm_en_subword.zip -d models/
```

### Method 3: Alternative Sources

If Google Drive links don't work, you can:

1. **Check the original repositories**:
   - Chaplin: https://github.com/amanvirparhar/chaplin
   - Auto-AVSR: https://github.com/mpc001/auto_avsr

2. **Look for alternative hosting**:
   - Hugging Face Model Hub
   - Academic institution mirrors
   - Research paper supplementary materials

## 🔧 Automated Download Script

I'll create a script to help with this process:

```bash
#!/bin/bash
# download_models.sh

echo "🔄 Downloading ICU Lipreading Models..."

# Create models directory
mkdir -p models
cd models

# Function to download with retry
download_with_retry() {
    local url=$1
    local filename=$2
    local max_attempts=3
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        echo "📥 Attempt $attempt: Downloading $filename..."
        
        if command -v gdown &> /dev/null; then
            gdown "$url" -O "$filename"
        else
            echo "⚠️  gdown not found. Please install with: pip install gdown"
            echo "🌐 Manual download required from: $url"
            return 1
        fi
        
        if [ $? -eq 0 ]; then
            echo "✅ Successfully downloaded $filename"
            return 0
        else
            echo "❌ Download failed. Retrying..."
            ((attempt++))
            sleep 5
        fi
    done
    
    echo "❌ Failed to download $filename after $max_attempts attempts"
    return 1
}

# Download models
download_with_retry "https://drive.google.com/uc?id=1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6" "LRS3_V_WER19.1.zip"
download_with_retry "https://drive.google.com/uc?id=1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv" "lm_en_subword.zip"

# Extract files
echo "📦 Extracting model files..."
if [ -f "LRS3_V_WER19.1.zip" ]; then
    unzip -q LRS3_V_WER19.1.zip
    rm LRS3_V_WER19.1.zip
    echo "✅ Extracted LRS3_V_WER19.1"
fi

if [ -f "lm_en_subword.zip" ]; then
    unzip -q lm_en_subword.zip
    rm lm_en_subword.zip
    echo "✅ Extracted lm_en_subword"
fi

echo "🎉 Model download complete!"
echo "📁 Models are now available in: $(pwd)"
ls -la
```

## 🚨 Troubleshooting

### Problem: "File too large" error on Google Drive
**Solution**: 
- Use gdown instead of browser download
- Or download in parts if available

### Problem: "Access denied" or "Quota exceeded"
**Solution**:
- Try downloading at a different time
- Use a different Google account
- Check if files are still available

### Problem: Models not loading in the app
**Solution**:
1. Verify file structure:
   ```
   backend/models/
   ├── LRS3_V_WER19.1/
   │   ├── model.pth
   │   ├── config.yaml
   │   └── ...
   └── lm_en_subword/
       ├── lm.binary
       ├── vocab.txt
       └── ...
   ```

2. Check file permissions:
   ```bash
   chmod -R 755 models/
   ```

3. Verify file integrity (if checksums are available)

## 🔄 Alternative: Development Mode

If you can't download the models immediately, you can still test the app:

1. **Simulation Mode**: The app includes a simulation mode that works without models
2. **Mock Responses**: Uses pre-defined medical phrases for testing
3. **UI Testing**: All interface features work without the AI models

To enable simulation mode, set in your backend config:
```ini
[development]
simulation_mode = true
mock_responses = true
```

## 📞 Getting Help

If you're still having trouble:

1. **Check the original Chaplin repository**: https://github.com/amanvirparhar/chaplin
2. **Look for updated download links** in recent issues/releases
3. **Contact the model authors** if links are broken
4. **Use the simulation mode** for development and testing

## ✅ Verification

After downloading, verify your setup:

```bash
# Check if models exist
ls -la backend/models/LRS3_V_WER19.1/
ls -la backend/models/lm_en_subword/

# Test the backend
cd backend
python app.py

# Should see: "Model loaded successfully!"
```

The models are essential for the AI functionality, but the app can run in simulation mode for development and testing purposes.
