{"version": 3, "file": "Asset.server.js", "sourceRoot": "", "sources": ["../src/Asset.server.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,SAAS,MAAM,aAAa,CAAC;AACzC,OAAO,KAAK,WAAW,MAAM,eAAe,CAAC;AAE7C,MAAM,OAAO,KAAK;IACR,MAAM,CAAC,MAAM,GAAsC,EAAE,CAAC;IACtD,MAAM,CAAC,KAAK,GAAsC,EAAE,CAAC;IAEtD,IAAI,CAAS;IACJ,IAAI,CAAS;IACb,IAAI,GAAkB,IAAI,CAAC;IAC3B,GAAG,CAAS;IACrB,QAAQ,GAAkB,IAAI,CAAC;IAC/B,KAAK,GAAkB,IAAI,CAAC;IAC5B,MAAM,GAAkB,IAAI,CAAC;IAC7B,UAAU,GAAY,IAAI,CAAC;IAElC,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAmB;QAC1E,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACrB,CAAC;QACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAE9C,sDAAsD;QACtD,IAAI,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAChB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,QAA+C;QAC9D,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClE,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED,MAAM,CAAC,UAAU,CACf,kBAAoF;QAEpF,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC;aAAM,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CACb,2FAA2F,CAC5F,CAAC;QACJ,CAAC;QACD,IACE,OAAO,kBAAkB,KAAK,QAAQ;YACtC,KAAK,IAAI,kBAAkB;YAC3B,OAAO,kBAAkB,CAAC,GAAG,KAAK,QAAQ,EAC1C,CAAC;YACD,MAAM,SAAS,GAAG,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YACrE,OAAO,IAAI,KAAK,CAAC;gBACf,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBACpE,IAAI,EAAE,IAAI;gBACV,GAAG,EAAE,kBAAkB,CAAC,GAAG;gBAC3B,KAAK,EAAE,kBAAkB,CAAC,KAAK;gBAC/B,MAAM,EAAE,kBAAkB,CAAC,MAAM;aAClC,CAAC,CAAC;QACL,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,OAAO,kBAAkB,CAAC,CAAC;IACnF,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,IAAmB;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;YACtB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI;YACJ,GAAG;YACH,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC;QACH,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;QAC/B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,GAAW;QACxB,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,MAAM,SAAS,GAAG,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAClD,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACxE,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;YACtB,IAAI,EAAE,EAAE;YACR,IAAI;YACJ,IAAI,EAAE,IAAI;YACV,GAAG;SACJ,CAAC,CAAC;QAEH,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAEzB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;;AAGH,SAAS,SAAS,CAAC,MAAgB,EAAE,WAAmB;IACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;YAC7B,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC;AAED;;;;;GAKG;AACH,SAAS,iBAAiB,CAAC,IAAmB;IAC5C,kGAAkG;IAClG,2BAA2B;IAC3B,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACxC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;IACxD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;IAE1F,6DAA6D;IAC7D,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;IAClF,IAAI,GAAG,EAAE,CAAC;QACR,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,MAAM,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC;IAClD,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3E,MAAM,MAAM,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,aAAa,EAAE,CAAC;IAC/E,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;QACjC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAQ;QAC9B,IAAI,EAAE,IAAI,CAAC,IAAI;KAChB,CAAC,CAAC;IAEH,iGAAiG;IACjG,kDAAkD;IAClD,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACjD,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;QAC5D,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,gGAAgG;IAChG,+FAA+F;IAC/F,kGAAkG;IAClG,4BAA4B;IAC5B,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;AAC3B,CAAC", "sourcesContent": ["import type { AssetDescriptor } from './Asset';\nimport type { AssetMetadata, AssetSource } from './AssetSources';\nimport * as AssetUris from './AssetUris';\nimport * as ImageAssets from './ImageAssets';\n\nexport class Asset {\n  private static byHash: Record<string, Asset | undefined> = {};\n  private static byUri: Record<string, Asset | undefined> = {};\n\n  public name: string;\n  public readonly type: string;\n  public readonly hash: string | null = null;\n  public readonly uri: string;\n  public localUri: string | null = null;\n  public width: number | null = null;\n  public height: number | null = null;\n  public downloaded: boolean = true;\n\n  constructor({ name, type, hash = null, uri, width, height }: AssetDescriptor) {\n    this.name = name;\n    this.type = type;\n    this.hash = hash;\n    this.uri = uri;\n\n    if (typeof width === 'number') {\n      this.width = width;\n    }\n    if (typeof height === 'number') {\n      this.height = height;\n    }\n\n    this.name ??= AssetUris.getFilename(uri);\n    this.type ??= AssetUris.getFileExtension(uri);\n\n    // Essentially run the contents of downloadAsync here.\n    if (ImageAssets.isImageType(this.type)) {\n      this.width = 0;\n      this.height = 0;\n      this.name = AssetUris.getFilename(this.uri);\n    } else {\n      this.name = AssetUris.getFilename(this.uri);\n    }\n    this.localUri = this.uri;\n  }\n\n  static loadAsync(moduleId: number | number[] | string | string[]): Promise<Asset[]> {\n    const moduleIds = Array.isArray(moduleId) ? moduleId : [moduleId];\n    return Promise.all(moduleIds.map((moduleId) => Asset.fromModule(moduleId).downloadAsync()));\n  }\n\n  static fromModule(\n    virtualAssetModule: number | string | { uri: string; width: number; height: number }\n  ): Asset {\n    if (typeof virtualAssetModule === 'string') {\n      return Asset.fromURI(virtualAssetModule);\n    } else if (typeof virtualAssetModule === 'number') {\n      throw new Error(\n        'Cannot resolve numeric asset IDs on the server as they are non-deterministic identifiers.'\n      );\n    }\n    if (\n      typeof virtualAssetModule === 'object' &&\n      'uri' in virtualAssetModule &&\n      typeof virtualAssetModule.uri === 'string'\n    ) {\n      const extension = AssetUris.getFileExtension(virtualAssetModule.uri);\n      return new Asset({\n        name: '',\n        type: extension.startsWith('.') ? extension.substring(1) : extension,\n        hash: null,\n        uri: virtualAssetModule.uri,\n        width: virtualAssetModule.width,\n        height: virtualAssetModule.height,\n      });\n    }\n    throw new Error('Unexpected asset module ID type: ' + typeof virtualAssetModule);\n  }\n\n  static fromMetadata(meta: AssetMetadata): Asset {\n    const metaHash = meta.hash;\n    const maybeHash = Asset.byHash[metaHash];\n    if (maybeHash) {\n      return maybeHash;\n    }\n\n    const { uri, hash } = selectAssetSource(meta);\n    const asset = new Asset({\n      name: meta.name,\n      type: meta.type,\n      hash,\n      uri,\n      width: meta.width,\n      height: meta.height,\n    });\n    Asset.byHash[metaHash] = asset;\n    return asset;\n  }\n\n  static fromURI(uri: string): Asset {\n    if (Asset.byUri[uri]) {\n      return Asset.byUri[uri];\n    }\n\n    // Possibly a Base64-encoded URI\n    let type = '';\n    if (uri.indexOf(';base64') > -1) {\n      type = uri.split(';')[0].split('/')[1];\n    } else {\n      const extension = AssetUris.getFileExtension(uri);\n      type = extension.startsWith('.') ? extension.substring(1) : extension;\n    }\n\n    const asset = new Asset({\n      name: '',\n      type,\n      hash: null,\n      uri,\n    });\n\n    Asset.byUri[uri] = asset;\n\n    return asset;\n  }\n\n  async downloadAsync(): Promise<this> {\n    return this;\n  }\n}\n\nfunction pickScale(scales: number[], deviceScale: number): number {\n  for (let i = 0; i < scales.length; i++) {\n    if (scales[i] >= deviceScale) {\n      return scales[i];\n    }\n  }\n  return scales[scales.length - 1] || 1;\n}\n\n/**\n * Selects the best file for the given asset (ex: choosing the best scale for images) and returns\n * a { uri, hash } pair for the specific asset file.\n *\n * If the asset isn't an image with multiple scales, the first file is selected.\n */\nfunction selectAssetSource(meta: AssetMetadata): AssetSource {\n  // This logic is based on that of AssetSourceResolver, with additional support for file hashes and\n  // explicitly provided URIs\n  const scale = pickScale(meta.scales, 1);\n  const index = meta.scales.findIndex((s) => s === scale);\n  const hash = meta.fileHashes ? (meta.fileHashes[index] ?? meta.fileHashes[0]) : meta.hash;\n\n  // Allow asset processors to directly provide the URL to load\n  const uri = meta.fileUris ? (meta.fileUris[index] ?? meta.fileUris[0]) : meta.uri;\n  if (uri) {\n    return { uri, hash };\n  }\n\n  const fileScale = scale === 1 ? '' : `@${scale}x`;\n  const fileExtension = meta.type ? `.${encodeURIComponent(meta.type)}` : '';\n  const suffix = `/${encodeURIComponent(meta.name)}${fileScale}${fileExtension}`;\n  const params = new URLSearchParams({\n    platform: process.env.EXPO_OS!,\n    hash: meta.hash,\n  });\n\n  // For assets with a specified absolute URL, we use the existing origin instead of prepending the\n  // development server or production CDN URL origin\n  if (/^https?:\\/\\//.test(meta.httpServerLocation)) {\n    const uri = meta.httpServerLocation + suffix + '?' + params;\n    return { uri, hash };\n  }\n\n  // In correctly configured apps, we arrive here if the asset is locally available on disk due to\n  // being managed by expo-updates, and `getLocalAssetUri(hash)` must return a local URI for this\n  // hash. Since the asset is local, we don't have a remote URL and specify an invalid URL (an empty\n  // string) as a placeholder.\n  return { uri: '', hash };\n}\n"]}