{"version": 3, "file": "Asset.js", "sourceRoot": "", "sources": ["../src/Asset.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,wCAAwC,CAAC;AACtE,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,OAAO,EAAiB,iBAAiB,EAAE,MAAM,gBAAgB,CAAC;AAClE,OAAO,KAAK,SAAS,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAC5C,OAAO,KAAK,WAAW,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AACjD,OAAO,EAAE,wBAAwB,EAAE,MAAM,iBAAiB,CAAC;AAC3D,OAAO,kBAAkB,MAAM,sBAAsB,CAAC;AAmBtD;;;GAGG;AACH,MAAM,CAAC,MAAM,kCAAkC,GAAG,sBAAsB,CAAC;AAEzE;;;GAGG;AACH,MAAM,OAAO,KAAK;IACR,MAAM,CAAC,MAAM,GAAsC,EAAE,CAAC;IACtD,MAAM,CAAC,KAAK,GAAsC,EAAE,CAAC;IAE7D;;;OAGG;IACI,IAAI,CAAS;IACpB;;OAEG;IACa,IAAI,CAAS;IAC7B;;OAEG;IACa,IAAI,GAAkB,IAAI,CAAC;IAC3C;;;;;;;OAOG;IACa,GAAG,CAAS;IAC5B;;;OAGG;IACI,QAAQ,GAAkB,IAAI,CAAC;IACtC;;;OAGG;IACI,KAAK,GAAkB,IAAI,CAAC;IACnC;;OAEG;IACI,MAAM,GAAkB,IAAI,CAAC;IAE5B,WAAW,GAAY,KAAK,CAAC;IAErC;;OAEG;IACI,UAAU,GAAY,KAAK,CAAC;IAE3B,kBAAkB,GAA+B,EAAE,CAAC;IAE5D,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAmB;QAC1E,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACrB,CAAC;QACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,kCAAkC,CAAC,EAAE,CAAC;gBAClE,sGAAsG;gBACtG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;gBACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACvB,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACzB,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;IAED,cAAc;IACd;;;;;;;;;;OAUG;IACH,MAAM,CAAC,SAAS,CAAC,QAA+C;QAC9D,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClE,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED,cAAc;IACd;;;;;OAKG;IACH,MAAM,CAAC,UAAU,CACf,kBAAoF;QAEpF,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC;QACD,IACE,OAAO,kBAAkB,KAAK,QAAQ;YACtC,KAAK,IAAI,kBAAkB;YAC3B,OAAO,kBAAkB,CAAC,GAAG,KAAK,QAAQ,EAC1C,CAAC;YACD,MAAM,SAAS,GAAG,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YACrE,OAAO,IAAI,KAAK,CAAC;gBACf,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBACpE,IAAI,EAAE,IAAI;gBACV,GAAG,EAAE,kBAAkB,CAAC,GAAG;gBAC3B,KAAK,EAAE,kBAAkB,CAAC,KAAK;gBAC/B,MAAM,EAAE,kBAAkB,CAAC,MAAM;aAClC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,IAAI,GAAG,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,WAAW,kBAAkB,sCAAsC,CAAC,CAAC;QACvF,CAAC;QAED,0EAA0E;QAC1E,2CAA2C;QAC3C,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAC9B,qDAAqD;YACrD,MAAM,EAAE,GAAG,EAAE,GAAG,kBAAkB,CAAC,kBAAkB,CAAE,CAAC;YAExD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;gBACtB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,GAAG;gBACH,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;YAEH,qCAAqC;YACrC,+DAA+D;YAC/D,2CAA2C;YAC3C,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnF,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;gBAC3B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;YAC1B,CAAC;YAED,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YAChC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,eAAe;IACf,MAAM,CAAC,YAAY,CAAC,IAAmB;QACrC,4FAA4F;QAC5F,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;YACtB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI;YACJ,GAAG;YACH,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC;QACH,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;QAC/B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,eAAe;IACf,MAAM,CAAC,OAAO,CAAC,GAAW;QACxB,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAChC,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,MAAM,SAAS,GAAG,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAClD,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACxE,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC;YACtB,IAAI,EAAE,EAAE;YACR,IAAI;YACJ,IAAI,EAAE,IAAI;YACV,GAAG;SACJ,CAAC,CAAC;QAEH,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAEzB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,cAAc;IACd;;;;;;;;;;;;;;;OAeG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC1C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI,CAAC;YACH,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;gBAC1B,IAAI,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC9E,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;oBACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACnB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9C,CAAC;YACH,CAAC;YACD,IAAI,CAAC,QAAQ,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,CAAC;QACV,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC/B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC", "sourcesContent": ["import { getAssetByID } from '@react-native/assets-registry/registry';\nimport { Platform } from 'expo-modules-core';\n\nimport { AssetMetadata, selectAssetSource } from './AssetSources';\nimport * as AssetUris from './AssetUris';\nimport { downloadAsync } from './ExpoAsset';\nimport * as ImageAssets from './ImageAssets';\nimport { getLocalAssetUri } from './LocalAssets';\nimport { IS_ENV_WITH_LOCAL_ASSETS } from './PlatformUtils';\nimport resolveAssetSource from './resolveAssetSource';\n\n// @docsMissing\nexport type AssetDescriptor = {\n  name: string;\n  type: string;\n  hash?: string | null;\n  uri: string;\n  width?: number | null;\n  height?: number | null;\n};\n\ntype DownloadPromiseCallbacks = {\n  resolve: () => void;\n  reject: (error: Error) => void;\n};\n\nexport { AssetMetadata };\n\n/**\n * Android resource URL prefix.\n * @hidden\n */\nexport const ANDROID_EMBEDDED_URL_BASE_RESOURCE = 'file:///android_res/';\n\n/**\n * The `Asset` class represents an asset in your app. It gives metadata about the asset (such as its\n * name and type) and provides facilities to load the asset data.\n */\nexport class Asset {\n  private static byHash: Record<string, Asset | undefined> = {};\n  private static byUri: Record<string, Asset | undefined> = {};\n\n  /**\n   * The name of the asset file without the extension. Also without the part from `@` onward in the\n   * filename (used to specify scale factor for images).\n   */\n  public name: string;\n  /**\n   * The extension of the asset filename.\n   */\n  public readonly type: string;\n  /**\n   * The MD5 hash of the asset's data.\n   */\n  public readonly hash: string | null = null;\n  /**\n   * A URI that points to the asset's data on the remote server. When running the published version\n   * of your app, this refers to the location on Expo's asset server where Expo has stored your\n   * asset. When running the app from Expo CLI during development, this URI points to Expo CLI's\n   * server running on your computer and the asset is served directly from your computer. If you\n   * are not using Classic Updates (legacy), this field should be ignored as we ensure your assets\n   * are on device before running your application logic.\n   */\n  public readonly uri: string;\n  /**\n   * If the asset has been downloaded (by calling [`downloadAsync()`](#downloadasync)), the\n   * `file://` URI pointing to the local file on the device that contains the asset data.\n   */\n  public localUri: string | null = null;\n  /**\n   * If the asset is an image, the width of the image data divided by the scale factor. The scale\n   * factor is the number after `@` in the filename, or `1` if not present.\n   */\n  public width: number | null = null;\n  /**\n   * If the asset is an image, the height of the image data divided by the scale factor. The scale factor is the number after `@` in the filename, or `1` if not present.\n   */\n  public height: number | null = null;\n\n  private downloading: boolean = false;\n\n  /**\n   * Whether the asset has finished downloading from a call to [`downloadAsync()`](#downloadasync).\n   */\n  public downloaded: boolean = false;\n\n  private _downloadCallbacks: DownloadPromiseCallbacks[] = [];\n\n  constructor({ name, type, hash = null, uri, width, height }: AssetDescriptor) {\n    this.name = name;\n    this.type = type;\n    this.hash = hash;\n    this.uri = uri;\n\n    if (typeof width === 'number') {\n      this.width = width;\n    }\n    if (typeof height === 'number') {\n      this.height = height;\n    }\n\n    if (hash) {\n      this.localUri = getLocalAssetUri(hash, type);\n      if (this.localUri?.startsWith(ANDROID_EMBEDDED_URL_BASE_RESOURCE)) {\n        // Treat Android embedded resources as not downloaded state, because the uri is not direct accessible.\n        this.uri = this.localUri;\n        this.localUri = null;\n      } else if (this.localUri) {\n        this.downloaded = true;\n      }\n    }\n\n    if (Platform.OS === 'web') {\n      if (!name) {\n        this.name = AssetUris.getFilename(uri);\n      }\n      if (!type) {\n        this.type = AssetUris.getFileExtension(uri);\n      }\n    }\n  }\n\n  // @needsAudit\n  /**\n   * A helper that wraps `Asset.fromModule(module).downloadAsync` for convenience.\n   * @param moduleId An array of `require('path/to/file')` or external network URLs. Can also be\n   * just one module or URL without an Array.\n   * @return Returns a Promise that fulfills with an array of `Asset`s when the asset(s) has been\n   * saved to disk.\n   * @example\n   * ```ts\n   * const [{ localUri }] = await Asset.loadAsync(require('./assets/snack-icon.png'));\n   * ```\n   */\n  static loadAsync(moduleId: number | number[] | string | string[]): Promise<Asset[]> {\n    const moduleIds = Array.isArray(moduleId) ? moduleId : [moduleId];\n    return Promise.all(moduleIds.map((moduleId) => Asset.fromModule(moduleId).downloadAsync()));\n  }\n\n  // @needsAudit\n  /**\n   * Returns the [`Asset`](#asset) instance representing an asset given its module or URL.\n   * @param virtualAssetModule The value of `require('path/to/file')` for the asset or external\n   * network URL\n   * @return The [`Asset`](#asset) instance for the asset.\n   */\n  static fromModule(\n    virtualAssetModule: number | string | { uri: string; width: number; height: number }\n  ): Asset {\n    if (typeof virtualAssetModule === 'string') {\n      return Asset.fromURI(virtualAssetModule);\n    }\n    if (\n      typeof virtualAssetModule === 'object' &&\n      'uri' in virtualAssetModule &&\n      typeof virtualAssetModule.uri === 'string'\n    ) {\n      const extension = AssetUris.getFileExtension(virtualAssetModule.uri);\n      return new Asset({\n        name: '',\n        type: extension.startsWith('.') ? extension.substring(1) : extension,\n        hash: null,\n        uri: virtualAssetModule.uri,\n        width: virtualAssetModule.width,\n        height: virtualAssetModule.height,\n      });\n    }\n\n    const meta = getAssetByID(virtualAssetModule);\n    if (!meta) {\n      throw new Error(`Module \"${virtualAssetModule}\" is missing from the asset registry`);\n    }\n\n    // Outside of the managed env we need the moduleId to initialize the asset\n    // because resolveAssetSource depends on it\n    if (!IS_ENV_WITH_LOCAL_ASSETS) {\n      // null-check is performed above with `getAssetByID`.\n      const { uri } = resolveAssetSource(virtualAssetModule)!;\n\n      const asset = new Asset({\n        name: meta.name,\n        type: meta.type,\n        hash: meta.hash,\n        uri,\n        width: meta.width,\n        height: meta.height,\n      });\n\n      // For images backward compatibility,\n      // keeps localUri the same as uri for React Native's Image that\n      // works fine with drawable resource names.\n      if (Platform.OS === 'android' && !uri.includes(':') && (meta.width || meta.height)) {\n        asset.localUri = asset.uri;\n        asset.downloaded = true;\n      }\n\n      Asset.byHash[meta.hash] = asset;\n      return asset;\n    }\n\n    return Asset.fromMetadata(meta);\n  }\n\n  // @docsMissing\n  static fromMetadata(meta: AssetMetadata): Asset {\n    // The hash of the whole asset, not to be confused with the hash of a specific file returned\n    // from `selectAssetSource`\n    const metaHash = meta.hash;\n    const assetByHash = Asset.byHash[metaHash];\n    if (assetByHash) {\n      return assetByHash;\n    }\n\n    const { uri, hash } = selectAssetSource(meta);\n    const asset = new Asset({\n      name: meta.name,\n      type: meta.type,\n      hash,\n      uri,\n      width: meta.width,\n      height: meta.height,\n    });\n    Asset.byHash[metaHash] = asset;\n    return asset;\n  }\n\n  // @docsMissing\n  static fromURI(uri: string): Asset {\n    if (Asset.byUri[uri]) {\n      return Asset.byUri[uri];\n    }\n\n    // Possibly a Base64-encoded URI\n    let type = '';\n    if (uri.indexOf(';base64') > -1) {\n      type = uri.split(';')[0].split('/')[1];\n    } else {\n      const extension = AssetUris.getFileExtension(uri);\n      type = extension.startsWith('.') ? extension.substring(1) : extension;\n    }\n\n    const asset = new Asset({\n      name: '',\n      type,\n      hash: null,\n      uri,\n    });\n\n    Asset.byUri[uri] = asset;\n\n    return asset;\n  }\n\n  // @needsAudit\n  /**\n   * Downloads the asset data to a local file in the device's cache directory. Once the returned\n   * promise is fulfilled without error, the [`localUri`](#localuri) field of this asset points\n   * to a local file containing the asset data. The asset is only downloaded if an up-to-date local\n   * file for the asset isn't already present due to an earlier download. The downloaded `Asset`\n   * will be returned when the promise is resolved.\n   *\n   * > **Note:** There is no guarantee that files downloaded via `downloadAsync` persist between app sessions.\n   * `downloadAsync` stores files in the caches directory, so it's up to the OS to clear this folder at its\n   * own discretion or when the user manually purges the caches directory. Downloaded assets are stored as\n   * `ExponentAsset-{cacheFileId}.{extension}` within the cache directory.\n   * > To manually clear cached assets, you can use [`expo-file-system`](./filesystem/) to\n   * delete the cache directory: `Paths.cache.delete()` or use the legacy API `deleteAsync(cacheDirectory)`.\n   *\n   * @return Returns a Promise which fulfills with an `Asset` instance.\n   */\n  async downloadAsync(): Promise<this> {\n    if (this.downloaded) {\n      return this;\n    }\n    if (this.downloading) {\n      await new Promise<void>((resolve, reject) => {\n        this._downloadCallbacks.push({ resolve, reject });\n      });\n      return this;\n    }\n    this.downloading = true;\n\n    try {\n      if (Platform.OS === 'web') {\n        if (ImageAssets.isImageType(this.type)) {\n          const { width, height, name } = await ImageAssets.getImageInfoAsync(this.uri);\n          this.width = width;\n          this.height = height;\n          this.name = name;\n        } else {\n          this.name = AssetUris.getFilename(this.uri);\n        }\n      }\n      this.localUri = await downloadAsync(this.uri, this.hash, this.type);\n\n      this.downloaded = true;\n      this._downloadCallbacks.forEach(({ resolve }) => resolve());\n    } catch (e: any) {\n      this._downloadCallbacks.forEach(({ reject }) => reject(e));\n      throw e;\n    } finally {\n      this.downloading = false;\n      this._downloadCallbacks = [];\n    }\n    return this;\n  }\n}\n"]}