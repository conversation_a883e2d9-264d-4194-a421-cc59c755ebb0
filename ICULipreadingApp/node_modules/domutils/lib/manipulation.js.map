{"version": 3, "file": "manipulation.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["manipulation.ts"], "names": [], "mappings": ";;AAQA,sCAcC;AASD,wCAiBC;AASD,kCAaC;AASD,wBAoBC;AASD,oCAaC;AASD,0BAiBC;AAjJD;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,IAAe;IACzC,IAAI,IAAI,CAAC,IAAI;QAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,IAAI,CAAC,IAAI;QAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAE1C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpC,IAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;YACnB,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAAC,IAAe,EAAE,WAAsB;IAClE,IAAM,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;IAC5B,CAAC;IAED,IAAM,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;IAC5B,CAAC;IAED,IAAM,MAAM,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAClD,IAAI,MAAM,EAAE,CAAC;QACT,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;QAC/B,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACvB,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,WAAW,CAAC,MAAkB,EAAE,KAAgB;IAC5D,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAClB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IAEtB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;QAClC,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5D,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;QACrB,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;IACzB,CAAC;SAAM,CAAC;QACJ,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IACtB,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,MAAM,CAAC,IAAe,EAAE,IAAe;IACnD,aAAa,CAAC,IAAI,CAAC,CAAC;IAEZ,IAAA,MAAM,GAAK,IAAI,OAAT,CAAU;IACxB,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;IAE3B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;IACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IAErB,IAAI,QAAQ,EAAE,CAAC;QACX,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;QACrB,IAAI,MAAM,EAAE,CAAC;YACT,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;SAAM,IAAI,MAAM,EAAE,CAAC;QAChB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,YAAY,CAAC,MAAkB,EAAE,KAAgB;IAC7D,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IACtB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAElB,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;QACvC,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;QACrB,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;IACzB,CAAC;SAAM,CAAC;QACJ,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IACtB,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,OAAO,CAAC,IAAe,EAAE,IAAe;IACpD,aAAa,CAAC,IAAI,CAAC,CAAC;IAEZ,IAAA,MAAM,GAAK,IAAI,OAAT,CAAU;IACxB,IAAI,MAAM,EAAE,CAAC;QACT,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;QAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,CAAC"}