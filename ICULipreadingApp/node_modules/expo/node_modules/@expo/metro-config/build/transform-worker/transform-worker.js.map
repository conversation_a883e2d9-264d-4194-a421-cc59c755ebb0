{"version": 3, "file": "transform-worker.js", "sourceRoot": "", "sources": ["../../src/transform-worker/transform-worker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;GAMG;AACH,0EAAkD;AAOlD,+BAA2C;AAC3C,+CAAsE;AACtE,iEAAmD;AACnD,uCAAmD;AACnD,iCAAgD;AAGzC,KAAK,UAAU,SAAS,CAC7B,MAA2B,EAC3B,WAAmB,EACnB,QAAgB,EAChB,IAAY,EACZ,OAA2B;IAE3B,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3E,yDAAyD;IACzD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,WAAW,GAAG,OAAO,CAAC,sBAAsB,EAAE,WAAW,CAAC;QAEhE,IACE,WAAW,KAAK,MAAM;YACtB,wCAAwC;YACxC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,OAAO,CAAC,QAAQ,yBAAyB,CAAC,CAAC;gBACvF,oBAAoB;gBACpB,QAAQ,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC,EACjE;YACA,2GAA2G;YAC3G,OAAO,MAAM,CAAC,SAAS,CACrB,MAAM,EACN,WAAW,EACX,QAAQ,EACR,CAAC,OAAO,CAAC,MAAM;gBACb,CAAC,CAAC,MAAM,CAAC,IAAI;gBACT,sEAAsE;gBACtE,kBAAkB;gBAClB,6EAA6E,CAC9E;gBACH,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EACnB,OAAO,CACR,CAAC;SACH;QAED,IACE,WAAW,KAAK,MAAM;YACtB,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC;YACnC,QAAQ,CAAC,KAAK,CAAC,8CAA8C,CAAC,EAC9D;YACA,iEAAiE;YACjE,oFAAoF;YACpF,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;SAClF;QAED,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;KACvE;IAED,2DAA2D;IAC3D,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;QAC9B,MAAM,IAAI,GAAG,IAAA,4BAAc,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvF,OAAO,MAAM,CAAC,SAAS,CACrB,MAAM,EACN,WAAW,EACX,QAAQ;QACR,2BAA2B;QAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EACjB,OAAO,CACR,CAAC;KACH;IAED,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAEjC,2BAA2B;IAC3B,MAAM,cAAc,GAAG,MAAM,IAAA,gCAAsB,EAAC,WAAW,EAAE;QAC/D,GAAG,EAAE,IAAI;QACT,QAAQ;KACT,CAAC,CAAC;IAEH,IAAI,cAAc,CAAC,UAAU,EAAE;QAC7B,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC;KAC3B;IAED,uEAAuE;IACvE,MAAM,MAAM,GAAG,IAAA,gBAAS,EAAC,QAAQ,CAAC,CAAC;IACnC,IAAI,MAAM,EAAE;QACV,IAAI,GAAG,IAAA,kBAAW,EAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC;KAC1E;IAED,gEAAgE;IAChE,sDAAsD;IACtD,IAAI,IAAA,4BAAc,EAAC,QAAQ,CAAC,EAAE;QAC5B,MAAM,OAAO,GAAG,MAAM,IAAA,mCAAqB,EAAC;YAC1C,QAAQ;YACR,GAAG,EAAE,IAAI;YACT,OAAO,EAAE;gBACP,WAAW;gBACX,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,SAAS,EAAE,KAAK;aACjB;SACF,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,SAAS,CAC5C,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAC3B,OAAO,CACR,CAAC;QAEF,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QACvC,MAAM,MAAM,GAAe;YACzB;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE;oBACJ,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI;oBAElC,wDAAwD;oBACxD,GAAG,EAAE;wBACH,IAAI,EAAE,OAAO;wBACb,SAAS,EAAE,IAAA,oBAAU,EAAC,OAAO,CAAC;wBAC9B,GAAG,EAAE,EAAE;wBACP,WAAW,EAAE,IAAI;qBAClB;iBACF;aACF;SACF,CAAC;QAEF,OAAO;YACL,YAAY,EAAE,eAAe,CAAC,YAAY;YAC1C,MAAM;SACP,CAAC;KACH;IAED,cAAc;IAEd,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,cAAc,CAAkC,CAAC;IAE/E,wCAAwC;IACxC,wDAAwD;IAExD,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,QAAQ;QACR,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QACvB,SAAS,EAAE,KAAK;QAChB,UAAU,EAAE,KAAK;QACjB,WAAW;QACX,MAAM,EAAE,OAAO,CAAC,MAAM;KACvB,CAAC,CAAC;IAEH,kBAAkB;IAClB,6CAA6C;IAC7C,MAAM;IAEN,wDAAwD;IACxD,kDAAkD;IAClD,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,SAAS,CAC5C,MAAM,EACN,WAAW,EACX,QAAQ,EACR,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,wBAAkB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EACxF,OAAO,CACR,CAAC;IAEF,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAE3C,iFAAiF;IACjF,qFAAqF;IACrF,oCAAoC;IACpC,MAAM,MAAM,GAAmB;QAC7B;YACE,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE;gBACJ,GAAI,eAAe,CAAC,MAAM,CAAC,CAAC,CAAkB,CAAC,IAAI;gBAEnD,wDAAwD;gBACxD,GAAG,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,SAAS,EAAE,IAAA,oBAAU,EAAC,OAAO,CAAC;oBAC9B,GAAG,EAAE,EAAE;oBACP,WAAW,EAAE,IAAI;oBACjB,sFAAsF;oBACtF,qEAAqE;oBACrE,SAAS,EAAE,cAAc,CAAC,UAAU;iBACrC;aACF;SACF;KACF,CAAC;IAEF,OAAO;QACL,YAAY,EAAE,eAAe,CAAC,YAAY;QAC1C,MAAM;KACP,CAAC;AACJ,CAAC;AAxLD,8BAwLC;AAED;;;;;GAKG;AACH,MAAM,CAAC,OAAO,GAAG;IACf,iDAAiD;IACjD,GAAG,MAAM;IACT,SAAS;CACV,CAAC"}