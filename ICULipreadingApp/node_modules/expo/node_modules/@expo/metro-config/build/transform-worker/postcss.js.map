{"version": 3, "file": "postcss.js", "sourceRoot": "", "sources": ["../../src/transform-worker/postcss.ts"], "names": [], "mappings": ";;;;;;AAAA;;;;;GAKG;AACH,gEAAuC;AACvC,4CAAoB;AACpB,gDAAwB;AAExB,gEAAuC;AAEvC,6CAA4E;AAY5E,MAAM,gBAAgB,GAAG,gBAAgB,CAAC;AAE1C,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC,CAAC;AAE1D,KAAK,UAAU,sBAAsB,CAC1C,WAAmB,EACnB,EAAE,GAAG,EAAE,QAAQ,EAAqC;IAEpD,MAAM,WAAW,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;IAEtD,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;KACnC;IAED,OAAO;QACL,GAAG,EAAE,MAAM,kCAAkC,CAAC,WAAW,EAAE;YACzD,WAAW;YACX,GAAG;YACH,QAAQ;SACT,CAAC;QACF,UAAU,EAAE,IAAI;KACjB,CAAC;AACJ,CAAC;AAlBD,wDAkBC;AAED,KAAK,UAAU,kCAAkC,CAC/C,WAAmB,EACnB,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAsE;IAElG,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,MAAM,uBAAuB,CAAC,WAAW,EAAE;QAC7E,MAAM,EAAE,WAAW;QACnB,YAAY,EAAE,QAAQ;KACvB,CAAC,CAAC;IAEH,KAAK,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IAClC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAE3B,qCAAqC;IACrC,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAA6B,CAAC;IAE/D,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IAEjE,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,KAAK,UAAU,uBAAuB,CACpC,WAAmB,EACnB,EACE,YAAY,EAAE,IAAI,EAClB,MAAM,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,GAIpF;IAED,MAAM,OAAO,GAAG,aAAa,EAAE,CAAC;IAEhC,OAAO,CAAC,YAAY,CAAC,CAAC;IACtB,yBAAyB;IAEzB,MAAM,OAAO,GAAG,CAAC,GAAG,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAC1C,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;QAE/B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,OAAO,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;SACvD;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CAAC;IAEH,IAAI,MAAM,CAAC,IAAI,EAAE;QACf,MAAM,CAAC,IAAI,GAAG,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;KACtD;IAED,IAAI,MAAM,CAAC,EAAE,EAAE;QACb,MAAM,CAAC,EAAE,GAAG,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;KAClD;IAED,MAAM,cAAc,GAA4B;QAC9C,IAAI,EAAE,IAAI;QACV,EAAE,EAAE,IAAI;QACR,GAAG,EAAE,KAAK;KACX,CAAC;IAEF,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI;YACF,cAAc,CAAC,MAAM,GAAG,MAAM,IAAA,8BAAoB,EAChD,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,MAAM,CAClD,CAAC;SACH;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,MAAM,IAAI,KAAK,CACb,oBAAoB,MAAM,oBAAoB,KAAK,CAAC,OAAO,SAAS,IAAI,GAAG,CAC5E,CAAC;aACH;YACD,MAAM,KAAK,CAAC;SACb;KACF;IAED,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;QACnC,IAAI;YACF,cAAc,CAAC,WAAW,GAAG,MAAM,IAAA,8BAAoB,EACrD,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,WAAW,CAC5D,CAAC;SACH;QAAC,OAAO,KAAc,EAAE;YACvB,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,MAAM,IAAI,KAAK,CACb,oBAAoB,WAAW,yBAAyB,KAAK,CAAC,OAAO,SAAS,IAAI,GAAG,CACtF,CAAC;aACH;YACD,MAAM,KAAK,CAAC;SACb;KACF;IAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI;YACF,cAAc,CAAC,MAAM,GAAG,MAAM,IAAA,8BAAoB,EAChD,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,MAAM,CAClD,CAAC;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,oBAAoB,MAAM,oBAAoB,KAAK,CAAC,OAAO,SAAS,IAAI,GAAG,CAAC,CAAC;SAC9F;KACF;IAED,IAAI,GAAG,KAAK,IAAI,EAAE;QAChB,qEAAqE;QACrE,cAAc,CAAC,GAAG,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;KACvC;IAED,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;AACrC,CAAC;AAED,SAAS,UAAU,CAAC,WAAmB,EAAE,MAAc,EAAE,OAAgB,EAAE,IAAY;IACrF,IAAI;QACF,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;QAE9B,qBAAqB;QACrB,IAAI,YAAY,GAAG,OAAO,CAAC,IAAA,sBAAW,EAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;QAE7D,IAAI,YAAY,CAAC,OAAO,EAAE;YACxB,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC;SACrC;QAED,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;YAC5C,OAAO,YAAY,CAAC;SACrB;QAED,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC;KAC9B;IAAC,OAAO,KAAc,EAAE;QACvB,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,oBAAoB,MAAM,oBAAoB,KAAK,CAAC,OAAO,SAAS,IAAI,GAAG,CAAC,CAAC;SAC9F;QACD,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAED,SAAgB,aAAa;IAC3B,MAAM,aAAa,GAAG,IAAI,GAAG,EAAe,CAAC;IAE7C,OAAO,CAAC,OAAa,EAAE,EAAE;QACvB,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YAClC,OAAO,aAAa,CAAC;SACtB;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC1B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;gBAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACzB,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC;oBAE/B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;wBAC5B,MAAM,IAAI,KAAK,CACb,yCAAyC,IAAI,+CAA+C,CAC7F,CAAC;qBACH;oBAED,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBAClC;qBAAM,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;oBACjD,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;iBACtC;qBAAM,IACL,MAAM;oBACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;oBAChC,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ;wBACjD,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC;oBACtD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EACvC;oBACA,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACnC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;oBAE7B,IAAI,OAAO,KAAK,KAAK,EAAE;wBACrB,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;qBAC5B;yBAAM;wBACL,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;qBAClC;iBACF;qBAAM,IAAI,MAAM,EAAE;oBACjB,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;iBACtC;aACF;SACF;aAAM;YACL,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAE9C,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,aAAa,EAAE;gBAC3C,IAAI,OAAO,KAAK,KAAK,EAAE;oBACrB,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBAC5B;qBAAM;oBACL,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBAClC;aACF;SACF;QAED,OAAO,aAAa,CAAC;IACvB,CAAC,CAAC;AACJ,CAAC;AAvDD,sCAuDC;AAED,SAAgB,oBAAoB,CAAC,WAAmB;IACtD,4EAA4E;IAC5E,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,GAAG,KAAK,CAAC,CAAC;IAEtE,IAAI,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;QAC/B,KAAK,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QAClC,OAAO,IAAA,6BAAmB,EAAC,YAAY,CAAC,CAAC;KAC1C;IAED,MAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAE1E,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;QACjC,KAAK,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACpC,OAAO,mBAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;KACvD;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAjBD,oDAiBC;AAED,SAAgB,oBAAoB,CAAC,WAAmB;IACtD,0EAA0E;IAC1E,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;IAE9C,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,GAAG,KAAK,CAAC,CAAC;IACtE,IAAI,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;QAC/B,OAAO,UAAU,CAAC,YAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC1E;IAED,MAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC1E,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;QACjC,OAAO,UAAU,CAAC,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC5E;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAdD,oDAcC"}