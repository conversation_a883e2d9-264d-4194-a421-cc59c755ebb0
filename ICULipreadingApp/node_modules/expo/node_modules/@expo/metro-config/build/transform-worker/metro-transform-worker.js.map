{"version": 3, "file": "metro-transform-worker.js", "sourceRoot": "", "sources": ["../../src/transform-worker/metro-transform-worker.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;GASG;AACH,sCAAmD;AAEnD,iEAAwC;AACxC,uDAAyC;AACzC,oDAAsC;AAEtC,iGAAyE;AACzE,wGAG0D;AAK1D,2GAAmF;AACnF,0EAAkD;AAElD,6CAAyC;AACzC,sEAA+C;AAC/C,uDAK0B;AAE1B,sFAA4D;AAE5D,+FAAuE;AACvE,8DAAiC;AAEjC,sEAAwD;AACxD,qDAAgD;AAqChD,mBAAmB;AACnB,SAAS,UAAU,CAAmB,CAAW,EAAE,OAAgB;IACjE,IAAA,qBAAM,EAAC,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;IAC3B,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,sBAAsB,CAC7B,UAAmC,EACnC,QAAgB;IAEhB,QAAQ,UAAU,EAAE;QAClB,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC;QAClB,KAAK,gBAAgB;YACnB,OAAO,8BAA8B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC/E;YACE,MAAM,IAAI,KAAK,CAAC,8CAA8C,UAAU,IAAI,CAAC,CAAC;KACjF;AACH,CAAC;AAED,MAAM,UAAU,GAAG,KAAK,EACtB,MAA2B,EAC3B,WAAmB,EACnB,QAAgB,EAChB,IAAY,EACZ,MAAc,EACd,GAAiC,EACjC,WAAqB,EAAE,EAItB,EAAE;IACH,MAAM,SAAS,GAAG,IAAA,kCAAe,EAAC;QAChC;YACE,IAAI;YACJ,MAAM;YACN,GAAG;YACH,8CAA8C;YAC9C,WAAW,EAAE,IAAI;YACjB,IAAI,EAAE,QAAQ;YACd,2CAA2C;YAC3C,SAAS,EAAE,KAAK;SACjB;KACF,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IAExB,MAAM,MAAM,GAAG,IAAA,qBAAW,EAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAEhD,IAAI;QACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC;YAC5B,IAAI;YACJ,GAAG,EAAE,SAAS;YACd,QAAQ;YACR,QAAQ;YACR,MAAM,EAAE,MAAM,CAAC,cAAc;SAC9B,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAA,kCAAe,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,iCAAc,CAAC,CAAC,CAAC,CAAC,EAAE;SAC3E,CAAC;KACH;IAAC,OAAO,KAAU,EAAE;QACnB,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,gBAAgB,EAAE;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,OAAO,YAAY,QAAQ,OAAO,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;SACvF;QAED,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CAAC;AAEF,MAAM,6BAA6B,GAA0B;IAC3D,oBAAoB,EAAE,GAAG,EAAE,GAAE,CAAC;IAC9B,mBAAmB,EAAE,GAAG,EAAE,GAAE,CAAC;IAC7B,iBAAiB,EAAE,GAAG,EAAE,GAAE,CAAC;IAC3B,8BAA8B,EAAE,GAAG,EAAE,GAAE,CAAC;CACzC,CAAC;AAEF,MAAM,uBAAwB,SAAQ,KAAK;IACzC,UAAU,CAAkC;IAC5C,QAAQ,CAAS;IAEjB,YAAY,UAA2C,EAAE,QAAgB;QACvE,KAAK,CAAC,GAAG,QAAQ,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;CACF;AAED,KAAK,UAAU,WAAW,CACxB,IAAY,EACZ,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAyB;IAEvD,4EAA4E;IAC5E,4DAA4D;IAC5D,IAAI,GAAG,GACL,IAAI,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC;IAEtE,iJAAiJ;IACjJ,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,IAAA,6BAAmB,EAAC,GAAG,CAAC,CAAC;IAE9D,6EAA6E;IAC7E,iBAAiB;IACjB,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;IAEnC,IACE,GAAG,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;QACnC,UAAU,IAAI,IAAI;QAClB,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC,KAAK,CAAC,CAAC,EAClE;QACA,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;KACxE;IAED,wEAAwE;IACxE,0DAA0D;IAC1D,MAAM,OAAO,GAAiB,EAAE,CAAC;IACjC,MAAM,eAAe,GAAG;QACtB,GAAG,OAAO;QACV,eAAe,EAAE,CAAC,aAAa,EAAE,SAAS,CAAC;QAC3C,aAAa;QACb,SAAS;KACV,CAAC;IAEF,wGAAwG;IACxG,uGAAuG;IACvG,IAAI,OAAO,CAAC,yBAAyB,KAAK,IAAI,EAAE;QAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,iCAAqB,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC,CAAC;KAC3E;IAED,0GAA0G;IAC1G,wGAAwG;IACxG,IAAI,OAAO,CAAC,cAAc,EAAE;QAC1B,OAAO,CAAC,IAAI,CAAC;YACX,iCAAqB,CAAC,oBAAoB;YAC1C;gBACE,GAAG,eAAe;gBAClB,eAAe,EAAE,OAAO,CAAC,kBAAkB;aAC5C;SACF,CAAC,CAAC;KACJ;IAED,iGAAiG;IACjG,uEAAuE;IAEvE,8GAA8G;IAC9G,wBAAwB;IACxB,GAAG,GAAG,UAAU;IACd,mBAAmB;IACnB,IAAA,2BAAoB,EAAC,GAAG,EAAE,EAAE,EAAE;QAC5B,GAAG,EAAE,IAAI;QACT,OAAO,EAAE,KAAK;QACd,IAAI,EAAE,KAAK;QACX,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,OAAO;QACP,UAAU,EAAE,KAAK;QACjB,yFAAyF;QACzF,6EAA6E;QAC7E,4JAA4J;QAC5J,6FAA6F;QAC7F,qFAAqF;QACrF,aAAa,EAAE,IAAI;KACpB,CAAC,CAAC,GAAI,CACR,CAAC;IACF,IAAI;IAEJ,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;QAChB,4EAA4E;QAC5E,wEAAwE;QACxE,cAAc;QACd,GAAG,GAAG,UAAU;QACd,mBAAmB;QACnB,IAAA,2BAAoB,EAAC,GAAG,EAAE,EAAE,EAAE;YAC5B,GAAG,EAAE,IAAI;YACT,OAAO,EAAE,KAAK;YACd,IAAI,EAAE,KAAK;YACX,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,CAAC,CAAC,iCAAqB,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;YACzE,UAAU,EAAE,KAAK;YACjB,aAAa,EAAE,KAAK;SACrB,CAAC,CAAC,GAAG,CACP,CAAC;KACH;IAED,IAAI,iBAAiB,GAAW,EAAE,CAAC;IACnC,IAAI,YAAmC,CAAC;IACxC,IAAI,UAAkC,CAAC;IAEvC,0EAA0E;IAC1E,8EAA8E;IAC9E,0EAA0E;IAC1E,0BAA0B;IAC1B,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;QAC7B,YAAY,GAAG,EAAE,CAAC;QAClB,UAAU,GAAG,wBAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;KAC/C;SAAM;QACL,IAAI;YACF,MAAM,IAAI,GAAG;gBACX,sBAAsB,EAAE,MAAM,CAAC,sBAAsB;gBACrD,qBAAqB,EACnB,MAAM,CAAC,8BAA8B,KAAK,IAAI;oBAC5C,CAAC,CAAC,6BAA6B;oBAC/B,CAAC,CAAC,SAAS;gBACf,eAAe,EAAE,sBAAsB,CAAC,MAAM,CAAC,qBAAqB,EAAE,IAAI,CAAC,QAAQ,CAAC;gBACpF,eAAe,EAAE,CAAC,aAAa,EAAE,SAAS,CAAC;gBAC3C,gBAAgB,EAAE,OAAO,CAAC,GAAG;gBAC7B,yBAAyB,EAAE,MAAM,CAAC,yBAAyB;gBAC3D,iBAAiB,EAAE,MAAM,CAAC,kCAAkC;gBAC5D,4BAA4B,EAAE,MAAM,CAAC,4BAA4B;aAClE,CAAC;YAEF,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG,IAAA,6BAAmB,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;SAC7E;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,6CAA+B,EAAE;gBACpD,MAAM,IAAI,uBAAuB,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACzD;YACD,MAAM,KAAK,CAAC;SACb;QAED,IAAI,MAAM,CAAC,8BAA8B,KAAK,IAAI,EAAE;YAClD,UAAU,GAAG,GAAG,CAAC;SAClB;aAAM;YACL,wEAAwE;YACxE,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,wBAAc,CAAC,UAAU,CAC9C,GAAG,EACH,aAAa,EACb,SAAS,EACT,iBAAiB,EACjB,MAAM,CAAC,YAAY,CACpB,CAAC,CAAC;SACJ;KACF;IACD,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,IAAI,MAAM,CAAC,kCAAkC,IAAI,IAAI,EAAE;QACrD,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC;KAC1D;IAED,MAAM,MAAM,GAAG,IAAA,6BAAY,EAAC,OAAO,CAAC,CAAC;IAErC,IACE,MAAM;QACN,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,qBAAqB;QAClD,CAAC,MAAM,CAAC,sCAAsC,EAC9C;QACA,iHAAiH;QACjH,QAAQ,CAAC,IAAI,CACX,GAAG,iCAAqB,CAAC,sBAAsB,CAAC,UAAU,EAAE;YAC1D,aAAa,EAAE,QAAQ;SACxB,CAAC,CACH,CAAC;KACH;IAED,MAAM,MAAM,GAAG,IAAA,mBAAQ,EACrB,UAAU,EACV;QACE,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,MAAM,CAAC,sBAAsB;QACtC,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,WAAW,EAAE,KAAK;QAClB,cAAc,EAAE,IAAI,CAAC,QAAQ;QAC7B,UAAU,EAAE,IAAI;KACjB,EACD,IAAI,CAAC,IAAI,CACV,CAAC;IAEF,+CAA+C;IAC/C,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,iCAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3E,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;IAEvB,IAAI,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,MAAM,UAAU,CAC/B,MAAM,EACN,WAAW,EACX,IAAI,CAAC,QAAQ,EACb,MAAM,CAAC,IAAI,EACX,IAAI,CAAC,IAAI,EACT,GAAG,EACH,QAAQ,CACT,CAAC,CAAC;KACJ;IAED,MAAM,MAAM,GAAe;QACzB;YACE,IAAI,EAAE;gBACJ,IAAI;gBACJ,SAAS,EAAE,IAAA,oBAAU,EAAC,IAAI,CAAC;gBAC3B,GAAG;gBACH,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B;YACD,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB;KACF,CAAC;IAEF,OAAO;QACL,YAAY;QACZ,MAAM;KACP,CAAC;AACJ,CAAC;AAED,gCAAgC;AAChC,KAAK,UAAU,cAAc,CAC3B,IAAe,EACf,OAA8B;IAE9B,MAAM,EAAE,iBAAiB,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;IAE3D,6CAA6C;IAC7C,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,SAAS,CAC7C,qBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,EACpC,iBAAiB,EACjB,YAAY,CACb,CAAC;IAEF,MAAM,MAAM,GAAW;QACrB,GAAG,IAAI;QACP,IAAI,EAAE,iBAAiB;QACvB,GAAG,EAAE,MAAM,CAAC,GAAG;QACf,WAAW,EAAE,IAAI;KAClB,CAAC;IAEF,OAAO,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACtC,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,oBAAoB,CACjC,IAAY,EACZ,OAA8B;IAE9B,MAAM,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;IAChD,MAAM,WAAW,GAAqB,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAEpE,MAAM,eAAe,GAAG,MAAM,WAAW,CAAC,SAAS;IACjD,8DAA8D;IAC9D,qBAAqB,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,yCAAsB,CAAC,CAAC,CAC/D,CAAC;IAEF,MAAM,MAAM,GAAW;QACrB,GAAG,IAAI;QACP,GAAG,EAAE,eAAe,CAAC,GAAG;QACxB,WAAW,EACT,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW;YAC5C,4DAA4D;YAC5D,eAAe,CAAC,WAAW;YAC3B,IAAI;KACP,CAAC;IAEF,OAAO,MAAM,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED,KAAK,UAAU,aAAa,CAC1B,IAAc,EACd,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAyB;IAEvD,IAAI,IAAI,GACN,MAAM,CAAC,8BAA8B,KAAK,IAAI;QAC5C,CAAC,CAAC,wBAAc,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,CAAC,CAAC,wBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;IAC9D,IAAI,GAAG,GAAiC,EAAE,CAAC;IAE3C,MAAM,MAAM,GAAG,IAAA,6BAAY,EAAC,OAAO,CAAC,CAAC;IAErC,IAAI,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;KAC9F;IAED,IAAI,MAAkB,CAAC;IAEvB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;QACzB,MAAM,GAAG,iBAAiB,CAAC;KAC5B;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;QACjC,MAAM,GAAG,WAAW,CAAC;KACtB;SAAM;QACL,MAAM,GAAG,WAAW,CAAC;KACtB;IAED,MAAM,MAAM,GAAe;QACzB;YACE,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAA,oBAAU,EAAC,IAAI,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,IAAI,EAAE;YACnE,IAAI,EAAE,MAAM;SACb;KACF,CAAC;IAEF,OAAO;QACL,YAAY,EAAE,EAAE;QAChB,MAAM;KACP,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAC5B,IAAwC,EACxC,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAyB,EACvD,UAAwB,EAAE;IAE1B,MAAM,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,uBAAuB,EAAE,GAAG,OAAO,CAAC;IAClE,OAAO;QACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,OAAO,EAAE;YACP,GAAG,uBAAuB;YAC1B,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;YAC/C,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;YAC7C,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,WAAW;YACX,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,QAAQ,EAAE,uBAAuB,CAAC,QAAQ,IAAI,IAAI;SACnD;QACD,OAAO;QACP,GAAG,EAAE,IAAI,CAAC,IAAI;KACf,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,SAAS,CAC7B,MAA2B,EAC3B,WAAmB,EACnB,QAAgB,EAChB,IAAY,EACZ,OAA2B;IAE3B,MAAM,OAAO,GAA0B;QACrC,MAAM;QACN,WAAW;QACX,OAAO;KACR,CAAC;IACF,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAEzC,MAAM,EAAE,kCAAkC,EAAE,GAAG,MAAM,CAAC;IACtD,IAAI,kCAAkC,IAAI,IAAI,EAAE;QAC9C,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACxE,IAAI,QAAQ,GAAG,CAAC,CAAC,EAAE;YACjB,MAAM,IAAI,WAAW,CACnB,4CAA4C;gBAC1C,kCAAkC;gBAClC,wBAAwB;gBACxB,QAAQ,CACX,CAAC;SACH;KACF;IAED,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC9B,MAAM,QAAQ,GAAa;YACzB,QAAQ;YACR,aAAa,EAAE,IAAI,CAAC,MAAM;YAC1B,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,CAAC;QAEF,OAAO,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;KACzC;IAED,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE;QAC5B,MAAM,IAAI,GAAc;YACtB,QAAQ;YACR,aAAa,EAAE,IAAI,CAAC,MAAM;YAC1B,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,CAAC;QAEF,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACtC;IAED,MAAM,IAAI,GAAW;QACnB,QAAQ;QACR,aAAa,EAAE,IAAI,CAAC,MAAM;QAC1B,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW;QAC3D,WAAW,EAAE,IAAI;KAClB,CAAC;IAEF,OAAO,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,CAAC;AA1DD,8BA0DC;AAED,SAAgB,WAAW,CAAC,MAA2B;IACrD,MAAM,EAAE,oBAAoB,EAAE,YAAY,EAAE,GAAG,eAAe,EAAE,GAAG,MAAM,CAAC;IAE1E,MAAM,QAAQ,GAAG,IAAA,yBAAgB,EAAC;QAChC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC;QACrC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;QAC7B,OAAO,CAAC,OAAO,CAAC,8CAA8C,CAAC;QAC/D,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC;QACtC,OAAO,CAAC,OAAO,CAAC,kDAAkD,CAAC;QACnE,OAAO,CAAC,OAAO,CAAC,6CAA6C,CAAC;QAC9D,GAAG,iCAAqB,CAAC,+BAA+B,EAAE;KAC3D,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACvD,OAAO;QACL,QAAQ;QACR,IAAA,wBAAU,EAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC3C,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE;KACnE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,CAAC;AAnBD,kCAmBC"}