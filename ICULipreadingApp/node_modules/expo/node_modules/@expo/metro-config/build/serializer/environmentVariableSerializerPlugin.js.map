{"version": 3, "file": "environmentVariableSerializerPlugin.js", "sourceRoot": "", "sources": ["../../src/serializer/environmentVariableSerializerPlugin.ts"], "names": [], "mappings": ";;;;;;AAOA,4EAAoD;AACpD,0EAAkD;AAIlD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,sCAAsC,CAAuB,CAAC;AAE7F,SAAgB,uBAAuB,CAAC,GAAW;IACjD,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAC9D,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACjC,CAAC;AAHD,0DAGC;AAED,SAAS,uBAAuB;IAC9B,qFAAqF;IACrF,MAAM,GAAG,GAAG,EAAE,CAAC;IACf,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;QAC7B,IAAI,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;YAClC,aAAa;YACb,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC7B;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAoB,EAAE,OAA0B;IAC3E,iDAAiD;IACjD,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;QAClD,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,MAAM,GAAG,GAAG,uBAAuB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACvD,OAAO,GAAG,KAAK,MAAM,CAAC;SACvB;QACD,OAAO,KAAK,CAAC;KACd;IAED,8DAA8D;IAC9D,OAAO,KAAK,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,WAAW,KAAK,MAAM,CAAC;AAC9E,CAAC;AAED,0HAA0H;AAC1H,SAAgB,6BAA6B,CAC3C,UAAkB,EAClB,UAA0C,EAC1C,KAAoB,EACpB,OAA0B;IAE1B,IAAI,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;QACvC,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC;QAC3E,IAAI,OAAO,EAAE;YACX,KAAK,CAAC,gEAAgE,CAAC,CAAC;YACxE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;iBACtD,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC;iBAC3C,OAAO,CACN,yFAAyF,EACzF,EAAE,CACH,CAAC;SACL;KACF;IACD,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAClD,CAAC;AAnBD,sEAmBC;AAED,SAAgB,mCAAmC,CACjD,UAAkB,EAClB,UAA0C,EAC1C,KAAoB,EACpB,OAA0B;IAE1B,4CAA4C;IAC5C,IAAI,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;QACvC,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACxE,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;KACjD;IAED,qEAAqE;IACrE,yCAAyC;IACzC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;QAChB,KAAK,CACH,2HAA2H,CAC5H,CAAC;QACF,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;KACjD;IAED,yEAAyE;IACzE,0DAA0D;IAC1D,MAAM,GAAG,GAAG,qDAAqD,MAAM,CAAC,IAAI,CAC1E,uBAAuB,EAAE,CAC1B;SACE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;SACtF,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;IAElB,MAAM,CAAC,WAAW,EAAE,GAAG,WAAW,CAAC,GAAG,UAAU,CAAC;IACjD,yDAAyD;IACzD,cAAc;IACd,OAAO;QACL,UAAU;QACV;YACE,+CAA+C;YAC/C,WAAW;YACX,iDAAiD;YACjD,aAAa,CAAC,GAAG,CAAC;YAClB,sBAAsB;YACtB,GAAG,WAAW;SACf;QACD,KAAK;QACL,OAAO;KACR,CAAC;AACJ,CAAC;AA7CD,kFA6CC;AAED,SAAS,aAAa,CAAC,QAAgB;IACrC,MAAM,IAAI,GAAG,4CAA4C,GAAG,QAAQ,CAAC;IACrE,MAAM,IAAI,GAAG,SAAS,CAAC;IACvB,MAAM,SAAS,GAAG,IAAA,oBAAU,EAAC,IAAI,CAAC,CAAC;IAEnC,OAAO;QACL,YAAY,EAAE,IAAI,GAAG,EAAE;QACvB,SAAS,EAAE,GAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QAC1C,mBAAmB,EAAE,IAAI,qBAAW,EAAE;QACtC,IAAI,EAAE,IAAI;QACV,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE;oBACJ,IAAI;oBACJ,+CAA+C;oBAC/C,SAAS;oBACT,GAAG,EAAE,EAAE;iBACR;aACF;SACF;KACF,CAAC;AACJ,CAAC"}