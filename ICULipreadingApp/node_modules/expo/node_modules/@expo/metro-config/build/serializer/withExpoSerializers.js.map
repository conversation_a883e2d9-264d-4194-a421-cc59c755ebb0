{"version": 3, "file": "withExpoSerializers.js", "sourceRoot": "", "sources": ["../../src/serializer/withExpoSerializers.ts"], "names": [], "mappings": ";;;;;;AAAA;;;;;GAKG;AACH,+CAAyD;AAEzD,yGAAoF;AACpF,kFAA0D;AAG1D,uCAAyC;AACzC,+FAG+C;AAC/C,sDAA0E;AAC1E,uDAA+E;AAE/E,gCAA6B;AAuB7B,MAAM,eAAe,GACnB,OAAO,yBAAkB,KAAK,UAAU;IACtC,CAAC,CAAC,yBAAkB,CAAC,eAAe;IACpC,CAAC,CAAC,yBAAkB,CAAC;AAEzB,SAAgB,mBAAmB,CACjC,MAAoB,EACpB,UAAmC,EAAE;IAErC,MAAM,UAAU,GAAuB,EAAE,CAAC;IAC1C,UAAU,CAAC,IAAI,CAAC,mEAA6B,CAAC,CAAC;IAC/C,IAAI,CAAC,SAAG,CAAC,uBAAuB,EAAE;QAChC,UAAU,CAAC,IAAI,CAAC,yEAAmC,CAAC,CAAC;KACtD;IAED,OAAO,qBAAqB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC;AAXD,kDAWC;AAED,iFAAiF;AACjF,qBAAqB;AACrB,SAAgB,qBAAqB,CACnC,MAAoB,EACpB,UAA8B,EAC9B,UAAmC,EAAE;IAErC,MAAM,kBAAkB,GAAG,MAAM,CAAC,UAAU,EAAE,gBAAgB,CAAC;IAE/D,OAAO;QACL,GAAG,MAAM;QACT,UAAU,EAAE;YACV,GAAG,MAAM,CAAC,UAAU;YACpB,gBAAgB,EAAE,oCAAoC,CACpD,MAAM,EACN,UAAU,EACV,kBAAkB,IAAI,IAAI,EAC1B,OAAO,CACR;SACF;KACF,CAAC;AACJ,CAAC;AAnBD,sDAmBC;AAED,SAAgB,mCAAmC,CACjD,MAA4B,EAC5B,gBAAyC,EAAE;IAE3C,OAAO,KAAK,EACV,UAAkB,EAClB,UAA0C,EAC1C,KAAiC,EACjC,OAAuC,EACU,EAAE;QACnD,MAAM,aAAa,GAAG,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACjD,mGAAmG;QACnG,MAAM,aAAa,GAAG,OAAO,CAAC,eAAe,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC;QAEzE,IAAI,OAA2B,CAAC;QAChC,MAAM,WAAW,GAAG,GAAG,EAAE;YACvB,IAAI,CAAC,aAAa,IAAI,OAAO,EAAE;gBAC7B,OAAO,OAAO,CAAC;aAChB;YAED,8BAA8B;YAC9B,MAAM,MAAM,GAAG,IAAA,2BAAY,EAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE;gBACzD,GAAG,OAAO;gBACV,OAAO,EAAE,SAAS;aACnB,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,IAAA,wBAAc,EAAC,MAAM,CAAC,CAAC,IAAI,CAAC;YAC/C,OAAO,GAAG,IAAA,sBAAY,EAAC,UAAU,CAAC,CAAC;YACnC,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC;QAEF,IAAI,kBAAkB,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;QAEzC,IAAI,UAAU,GAAkB,IAAI,CAAC;QACrC,IAAI,SAAS,GAAkB,IAAI,CAAC;QAEpC,IAAI,MAAM,CAAC,UAAU,EAAE,gBAAgB,EAAE;YACvC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,UAAU,EAAE,gBAAgB,CACtD,UAAU,EACV,kBAAkB,EAClB,KAAK,EACL,OAAO,CACR,CAAC;YACF,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;gBAC9B,UAAU,GAAG,MAAM,CAAC;aACrB;iBAAM;gBACL,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC;gBACzB,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC;aACxB;SACF;aAAM;YACL,MAAM,OAAO,GAAG,WAAW,EAAE,CAAC;YAC9B,IAAI,aAAa,CAAC,wCAAwC,EAAE;gBAC1D,KAAK,MAAM,MAAM,IAAI,aAAa,CAAC,wCAAwC,EAAE;oBAC3E,kBAAkB,GAAG,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,GAAG,kBAAkB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;iBACtF;aACF;YACD,UAAU,GAAG,IAAA,wBAAc,EACzB,IAAA,2BAAY,EAAC,UAAU,EAAE,kBAAkB,EAAE,KAAK,EAAE;gBAClD,GAAG,OAAO;gBACV,OAAO;aACR,CAAC,CACH,CAAC,IAAI,CAAC;SACR;QAED,IAAI,aAAa,EAAE;YACjB,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,OAAO,UAAU,CAAC;aACnB;YACD,OAAO;gBACL,IAAI,EAAE,UAAU;gBAChB,GAAG,EAAE,SAAS;aACf,CAAC;SACH;QAED,cAAc;QAEd,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,eAAe,CACzB,CAAC,GAAG,kBAAkB,EAAE,GAAG,IAAA,kCAAgB,EAAC,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EACvF;gBACE,8BAA8B;gBAC9B,aAAa,EAAE,KAAK;gBACpB,2DAA2D;gBAC3D,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;gBAChD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;aACrD,CACF,CAAC;SACH;QAED,IAAI,aAAa,EAAE;YACjB,MAAM,0BAA0B,GAAG,CAAC,SAAiB,EAAE,EAAE;gBACvD,qHAAqH;gBACrH,8DAA8D;gBAC9D,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBAC9C,eAAe,CAAC,OAAO,GAAG,WAAW,EAAE,CAAC;gBACxC,4CAA4C;gBAC5C,sCAAsC;gBACtC,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACzC,CAAC,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,UAAU;gBAChB,GAAG,EAAE,0BAA0B,CAAC,SAAS,CAAC;aAC3C,CAAC;SACH;QAED,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,GAAG,EAAE,SAAS;SACf,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AA9GD,kFA8GC;AAED,SAAS,oBAAoB,CAC3B,MAAmB,EACnB,kBAAsC,EACtC,gBAAyC,EAAE;IAE3C,MAAM,iBAAiB,GACrB,kBAAkB,IAAI,mCAAmC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAEnF,OAAO,KAAK,EACV,GAAG,KAA2B,EACmB,EAAE;QACnD,MAAM,CAAC,EAAE,AAAD,EAAG,AAAD,EAAG,OAAO,CAAC,GAAG,KAAK,CAAC;QAE9B,MAAM,uBAAuB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QAE1D,uEAAuE;QACvE,gHAAgH;QAChH,MAAM,uBAAuB,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM,CAAC;QAElE,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAE;YAC9B,IAAI,uBAAuB,EAAE;gBAC3B,OAAO;oBACL,UAAU,EAAE,uBAAuB,CAAC,MAAM;oBAC1C,iBAAiB,EAAE,uBAAuB,CAAC,iBAAiB;iBAC7D,CAAC;aACH;YACD,IAAI,OAAO,CAAC,SAAS,EAAE;gBACrB,MAAM,SAAS,GAAG,IAAA,2BAAY,EAAC,OAAO,CAAC,SAAS,CAAC;oBAC/C,CAAC,CAAC,IAAA,0BAAW,EAAC,OAAO,CAAC,SAAS,CAAC;oBAChC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;gBAEtB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;gBAEnD,OAAO;oBACL,UAAU,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,CAAC;oBACrD,iBAAiB,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,MAAM;iBACrE,CAAC;aACH;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,iBAAiB,EAAE,UAAU,KAAK,QAAQ,EAAE;YAC9C,OAAO,iBAAiB,CAAC,GAAG,KAAK,CAAC,CAAC;SACpC;QAED,yDAAyD;QACzD,OAAO,CAAC,iBAAiB,GAAG;YAC1B,GAAG,OAAO,CAAC,iBAAiB;YAC5B,GAAG,iBAAiB;SACrB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,0CAAwB,EAC3C,MAAM,EACN;YACE,iBAAiB,EAAE,CAAC,CAAC,iBAAiB,CAAC,iBAAiB;YACxD,GAAG,aAAa;SACjB,EACD,GAAG,KAAK,CACT,CAAC;QAEF,IAAI,uBAAuB,EAAE;YAC3B,qFAAqF;YACrF,OAAO,MAAM,CAAC;SACf;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,oCAAoC,CAClD,MAAmB,EACnB,UAA4C,EAC5C,kBAAqC,EACrC,UAAmC,EAAE;IAErC,MAAM,eAAe,GAAG,oBAAoB,CAAC,MAAM,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IAClF,OAAO,CAAC,GAAG,KAA2B,EAA0B,EAAE;QAChE,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,IAAI,SAAS,EAAE;gBACb,KAAK,GAAG,SAAS,CAAC,GAAG,KAAK,CAAC,CAAC;aAC7B;SACF;QAED,OAAO,eAAe,CAAC,GAAG,KAAK,CAAC,CAAC;IACnC,CAAC,CAAC;AACJ,CAAC;AAhBD,oFAgBC"}