{"version": 3, "file": "createMultiRuleTransformer.js", "sourceRoot": "", "sources": ["../../src/transformer/createMultiRuleTransformer.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;;;;AAErE,kDAA0B;AAC1B,kDAA0B;AAE1B,gEAAuC;AAEvC,+DAA4D;AAC5D,qDAAkD;AAElD,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,qCAAqC,CAAC,CAAC;AAE3D,IAAI,SAAmD,CAAC;AAExD,SAAS,uBAAuB,CAAC,WAAmB;IAClD,IAAI,SAAS;QAAE,OAAO,SAAS,CAAC;IAChC,SAAS,GAAG,OAAO,CAAC,IAAA,sBAAW,EAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;IAC7D,OAAO,SAAU,CAAC;AACpB,CAAC;AAED,IAAI,WAAuD,CAAC;AAE5D,SAAS,yBAAyB,CAAC,WAAmB;IACpD,IAAI,WAAW;QAAE,OAAO,WAAW,CAAC;IACpC,WAAW,GAAG,OAAO,CAAC,IAAA,sBAAW,EAAC,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC;IACjE,OAAO,WAAY,CAAC;AACtB,CAAC;AAED,SAAS,OAAO,CACd,IAA0B,EAC1B,EACE,UAAU,GAGX;IAED,MAAM,EACJ,GAAG,EACH,QAAQ,EACR,OAAO,EAAE,EAAE,GAAG,EAAE,GACjB,GAAG,IAAI,CAAC;IACT,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;IAEzC,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,EAAE;QAC7B,QAAQ,EAAE,QAAQ;QAClB,UAAU,EAAE,CAAC,GAAG;QAChB,UAAU;KACX,CAAC,CAAC;IAEH,OAAO;QACL,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,WAAW,EAAE,IAAI;KAClB,CAAC;AACJ,CAAC;AAED,MAAM,6BAA6B,GAAG,CAAC,QAAgB,EAAE,EAAE,CAAC;IAC1D,KAAK;IACL,SAAS;IACT,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM;CACjD,CAAC;AAEF,SAAS,QAAQ,CAAC,WAAmB,EAAE,UAAkB;IACvD,MAAM,OAAO,GAAG,yBAAyB,CAAC,WAAW,CAAC,CAAC;IAEvD,OAAO,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE;QAC/B,UAAU,EAAE,aAAa;KAC1B,CAAC,CAAC;AACL,CAAC;AAUD,kEAAkE;AAClE,SAAgB,0BAA0B,CAAC,EACzC,WAAW,EACX,KAAK,GAIN;IACC,iCAAiC;IACjC,OAAO,SAAS,SAAS,CAAC,IAA0B;QAClD,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QACnC,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,YAAY,CAAC;QAE7F,IAAI;YACF,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAEnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,yCAAyC;gBACzC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;oBACvC,SAAS;iBACV;gBAED,MAAM,SAAS,GACb,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACpF,IAAI,SAAS,EAAE;oBACb,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBACrC,6CAA6C;oBAC7C,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;oBAC9B,uGAAuG;oBACvG,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;wBAChC,yFAAyF;wBACzF,OAAO,CAAC,GAAG,GAAG,QAAQ,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;qBAC5D;oBAED,4BAA4B;oBAC5B,mBAAmB;oBACnB,sBAAsB;oBACtB,wFAAwF;oBACxF,gBAAgB;oBAChB,sDAAsD;oBACtD,gCAAgC;oBAChC,yDAAyD;oBACzD,oBAAoB;oBACpB,8JAA8J;oBAC9J,SAAS;oBACT,MAAM;oBACN,IAAI;oBAEJ,OAAO,OAAO,CAAC;iBAChB;aACF;YACD,MAAM,IAAI,KAAK,CAAC,iCAAiC,GAAG,QAAQ,CAAC,CAAC;SAC/D;gBAAS;YACR,IAAI,aAAa,EAAE;gBACjB,OAAO,CAAC,GAAG,CAAC,SAAS,GAAG,aAAa,CAAC;aACvC;SACF;IACH,CAAC,CAAC;AACJ,CAAC;AA1DD,gEA0DC;AAED,SAAS,GAAG,CAAC,IAA0B;IACrC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAE7B,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IACjD,MAAM,WAAW,GAAG;QAClB,0EAA0E;QAC1E,UAAU,EAAE,aAAa;QACzB,GAAG,IAAA,+BAAc,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;QAC7C,yDAAyD;QACzD,MAAM,EAAE;YACN,IAAI,EAAE,OAAO;YAEb,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B;QACD,GAAG,EAAE,IAAI;KACV,CAAC;IAEF,2DAA2D;IAC3D,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE;QACrD,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE,KAAK;QACf,KAAK,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,GAAG,UAAU,GAAQ;YACvD,wEAAwE;YACxE,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM,CAAA,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,EAAE,SAAS,EAAE,oBAAoB,EAAE,GAAG,uBAAuB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACzF,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAE9C,uBAAuB;IACvB,IAAI,CAAC,SAAS;QAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IAErC,MAAM,MAAM,GAAG,oBAAoB,CAAC,SAAS,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IAEjE,2BAA2B;IAC3B,MAAM,WAAW,GAAG,IAAA,yCAAmB,EAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;IACjE,8EAA8E;IAC9E,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;KACnC;IAED,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,WAAW,EAAE,CAAC;AAC1C,CAAC;AAEY,QAAA,OAAO,GAAwD;IAC1E,mEAAmE;IACnE,GAAG;IAEH,uCAAuC;IACvC,iBAAiB,CAAC,IAAI;QACpB,yCAAyC;QACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,gDAAgD,CAAC,EAAE;YAC5E,8GAA8G;YAC9G,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;SAClB;QAED,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,OAAO,OAAO,CAAC,IAAI,EAAE;YACnB,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;SACvC,CAAC,CAAC;IACL,CAAC;IAED,uCAAuC;IACvC,UAAU,CAAC,IAAI;QACb,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,8BAA8B;QAC9B,OAAO,OAAO,CAAC,IAAI,EAAE;YACnB,UAAU,EAAE;gBACV,SAAS;gBACT,+CAA+C;gBAC/C,sCAAsC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK;gBACnE,6BAA6B;gBAC7B,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM;aACnD,CAAC,MAAM,CAAC,OAAO,CAAa;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,oEAAoE;IACpE,kBAAkB,CAAC,IAAI;QACrB,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,OAAO,CAAC,IAAI,EAAE;YACnB,UAAU,EAAE,6BAA6B,CAAC,IAAI,CAAC,QAAQ,CAAC;SACzD,CAAC,CAAC;IACL,CAAC;IAED,qDAAqD;IACrD,iBAAiB,CAAC,IAAI;QACpB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QACxC,KAAK,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAEhC,iGAAiG;QACjG,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;QAE/C,2BAA2B;QAC3B,MAAM,WAAW,GAAG,IAAA,yCAAmB,EAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAE3D,OAAO;YACL,IAAI,EAAE,GAAG;YACT,WAAW;YACX,GAAG;SACJ,CAAC;IACJ,CAAC;CACF,CAAC"}