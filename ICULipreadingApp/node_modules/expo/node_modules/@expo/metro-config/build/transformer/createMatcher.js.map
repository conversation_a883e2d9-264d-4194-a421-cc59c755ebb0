{"version": 3, "file": "createMatcher.js", "sourceRoot": "", "sources": ["../../src/transformer/createMatcher.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;AAErE,SAAgB,mBAAmB,CAAC,EAClC,OAAO,GAAG,CAAC,cAAc,CAAC,EAC1B,SAAS,GAIV;IACC,MAAM,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE3C,MAAM,mBAAmB,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEhD,MAAM,eAAe,GACnB,GAAG,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEzF,OAAO,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC;AACrC,CAAC;AAfD,kDAeC;AAEM,MAAM,wBAAwB,GAAG,CAAC,EAAE,OAAO,EAA0B,EAAE,EAAE,CAC9E,mBAAmB,CAAC;IAClB,OAAO;IACP,SAAS,EAAE,CAAC,eAAe,CAAC;CAC7B,CAAC,CAAC;AAJQ,QAAA,wBAAwB,4BAIhC;AAEE,MAAM,iBAAiB,GAAG,CAAC,EAAE,OAAO,EAA0B,EAAE,EAAE,CACvE,mBAAmB,CAAC;IAClB,OAAO;IACP,qCAAqC;IACrC,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC;CAC1C,CAAC,CAAC;AALQ,QAAA,iBAAiB,qBAKzB;AAEL,8DAA8D;AAC9D,qCAAqC;AAC9B,MAAM,2BAA2B,GAAG,CAAC,EAC1C,OAAO,EACP,SAAS,GAAG,EAAE,MAIZ,EAAE,EAAE,EAAE,CACR,mBAAmB,CAAC;IAClB,OAAO;IACP,SAAS,EAAE;QACT,GAAG,SAAS;QACZ,2DAA2D;QAC3D,sHAAsH;QACtH,SAAS;QACT,uBAAuB;QACvB,4BAA4B;QAC5B,eAAe;QACf,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,WAAW;QACX,6BAA6B;QAC7B,oBAAoB;QACpB,0CAA0C;QAC1C,gBAAgB;QAChB,iBAAiB;QACjB,mBAAmB;KACpB;CACF,CAAC,CAAC;AA5BQ,QAAA,2BAA2B,+BA4BnC"}