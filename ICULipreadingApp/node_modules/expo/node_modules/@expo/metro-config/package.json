{"name": "@expo/metro-config", "version": "0.17.8", "description": "A Metro config for running React Native projects with the Metro bundler", "main": "build/ExpoMetroConfig.js", "scripts": {"build": "expo-module tsc", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && expo-module tsc", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck", "watch": "expo-module tsc --watch --preserveWatchOutput"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/metro-config"}, "keywords": ["expo", "metro"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/metro-config#readme", "files": ["build", "transformer", "file-store", "babel-transformer"], "dependencies": {"@babel/core": "^7.20.0", "@babel/generator": "^7.20.5", "@babel/parser": "^7.20.0", "@babel/types": "^7.20.0", "@expo/config": "~8.5.0", "@expo/env": "~0.2.2", "@expo/json-file": "~8.3.0", "@expo/spawn-async": "^1.7.2", "babel-preset-fbjs": "^3.4.0", "chalk": "^4.1.0", "debug": "^4.3.2", "find-yarn-workspace-root": "~2.0.0", "fs-extra": "^9.1.0", "getenv": "^1.0.0", "glob": "^7.2.3", "jsc-safe-url": "^0.2.4", "lightningcss": "~1.19.0", "postcss": "~8.4.32", "resolve-from": "^5.0.0", "sucrase": "3.34.0"}, "peerDependencies": {"@react-native/babel-preset": "*"}, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.20", "expo-module-scripts": "^3.3.0", "sass": "^1.60.0"}, "publishConfig": {"access": "public"}, "gitHead": "eeaeb00da66634b5a788aa95920fc0cdd3a43332"}