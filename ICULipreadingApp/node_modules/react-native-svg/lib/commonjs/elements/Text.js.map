{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_extractText", "_interopRequireDefault", "_extractProps", "_extractTransform", "_util", "_Shape", "_TextNativeComponent", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "Text", "<PERSON><PERSON><PERSON>", "displayName", "setNativeProps", "props", "matrix", "extractTransform", "prop", "propsAndStyles", "assign", "pickNotNil", "extractText", "root", "render", "extractProps", "x", "y", "ref", "refMethod", "createElement", "exports"], "sourceRoot": "../../../src", "sources": ["elements/Text.tsx"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,aAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,iBAAA,GAAAF,sBAAA,CAAAF,OAAA;AAOA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAO,oBAAA,GAAAL,sBAAA,CAAAF,OAAA;AAAsD,SAAAE,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAavC,MAAMW,IAAI,SAASC,cAAK,CAAY;EACjD,OAAOC,WAAW,GAAG,MAAM;EAE3BC,cAAc,GACZC,KAGC,IACE;IACH,MAAMC,MAAM,GAAGD,KAAK,IAAI,CAACA,KAAK,CAACC,MAAM,IAAI,IAAAC,yBAAgB,EAACF,KAAK,CAAC;IAChE,IAAIC,MAAM,EAAE;MACVD,KAAK,CAACC,MAAM,GAAGA,MAAM;IACvB;IACA,MAAME,IAAI,GAAG,IAAAC,4BAAc,EAACJ,KAAK,CAAC;IAClCZ,MAAM,CAACiB,MAAM,CAACF,IAAI,EAAE,IAAAG,gBAAU,EAAC,IAAAC,oBAAW,EAACJ,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACxD,IAAI,CAACK,IAAI,IAAI,IAAI,CAACA,IAAI,CAACT,cAAc,CAACI,IAAI,CAAC;EAC7C,CAAC;EAEDM,MAAMA,CAAA,EAAG;IACP,MAAMN,IAAI,GAAG,IAAAC,4BAAc,EAAC,IAAI,CAACJ,KAAK,CAAC;IACvC,MAAMA,KAAK,GAAG,IAAAU,qBAAY,EACxB;MACE,GAAGP,IAAI;MACPQ,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACL,CAAC,EACD,IACF,CAAC;IACDxB,MAAM,CAACiB,MAAM,CAACL,KAAK,EAAE,IAAAO,oBAAW,EAACJ,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7CH,KAAK,CAACa,GAAG,GAAG,IAAI,CAACC,SAAiD;IAClE,oBAAOhD,KAAA,CAAAiD,aAAA,CAACxC,oBAAA,CAAAG,OAAS,EAAKsB,KAAQ,CAAC;EACjC;AACF;AAACgB,OAAA,CAAAtC,OAAA,GAAAkB,IAAA", "ignoreList": []}