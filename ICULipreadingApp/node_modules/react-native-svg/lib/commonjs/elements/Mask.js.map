{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_extractProps", "_units", "_interopRequireDefault", "_Shape", "_MaskNativeComponent", "_maskType", "e", "__esModule", "default", "_getRequireWildcardCache", "WeakMap", "r", "t", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "Mask", "<PERSON><PERSON><PERSON>", "displayName", "defaultProps", "x", "y", "width", "height", "render", "props", "maskUnits", "maskContentUnits", "children", "style", "maskProps", "undefined", "units", "maskType", "createElement", "ref", "refMethod", "withoutXY", "exports"], "sourceRoot": "../../../src", "sources": ["elements/Mask.tsx"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AAOA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,oBAAA,GAAAF,sBAAA,CAAAH,OAAA;AAEA,IAAAM,SAAA,GAAAN,OAAA;AAA2C,SAAAG,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,yBAAAH,CAAA,6BAAAI,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAD,wBAAA,YAAAA,CAAAH,CAAA,WAAAA,CAAA,GAAAM,CAAA,GAAAD,CAAA,KAAAL,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAK,CAAA,SAAAA,CAAA,IAAAL,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAE,OAAA,EAAAF,CAAA,QAAAM,CAAA,GAAAH,wBAAA,CAAAE,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAP,CAAA,UAAAM,CAAA,CAAAE,GAAA,CAAAR,CAAA,OAAAS,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAf,CAAA,oBAAAe,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAe,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAd,CAAA,EAAAe,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAf,CAAA,CAAAe,CAAA,YAAAN,CAAA,CAAAP,OAAA,GAAAF,CAAA,EAAAM,CAAA,IAAAA,CAAA,CAAAa,GAAA,CAAAnB,CAAA,EAAAS,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAT,CAAA,MAAAA,CAAA,GAAAuB,SAAA,CAAAC,MAAA,EAAAxB,CAAA,UAAAM,CAAA,GAAAiB,SAAA,CAAAvB,CAAA,YAAAK,CAAA,IAAAC,CAAA,OAAAU,cAAA,CAAAC,IAAA,CAAAX,CAAA,EAAAD,CAAA,MAAAI,CAAA,CAAAJ,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAI,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAiB5B,MAAMG,IAAI,SAASC,cAAK,CAAY;EACjD,OAAOC,WAAW,GAAG,MAAM;EAE3B,OAAOC,YAAY,GAAG;IACpBC,CAAC,EAAE,IAAI;IACPC,CAAC,EAAE,IAAI;IACPC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAC;EAEDC,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM;MACJL,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNG,SAAS;MACTC,gBAAgB;MAChBC,QAAQ;MACRC;IACF,CAAC,GAAGJ,KAAK;IACT,MAAMK,SAAS,GAAG;MAChBV,CAAC;MACDC,CAAC;MACDC,KAAK;MACLC,MAAM;MACNG,SAAS,EAAEA,SAAS,KAAKK,SAAS,GAAGC,cAAK,CAACN,SAAS,CAAC,GAAG,CAAC;MACzDC,gBAAgB,EACdA,gBAAgB,KAAKI,SAAS,GAAGC,cAAK,CAACL,gBAAgB,CAAC,GAAG,CAAC;MAC9DM,QAAQ,EAAEA,kBAAQ,CAAC,CAAAR,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,QAAQ,MAAIJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,QAAQ,KAAI,WAAW;IACtE,CAAC;IACD,oBACEpD,KAAA,CAAAqD,aAAA,CAAC9C,oBAAA,CAAAI,OAAS,EAAAkB,QAAA;MACRyB,GAAG,EAAGA,GAAG,IAAK,IAAI,CAACC,SAAS,CAACD,GAAoC;IAAE,GAC/D,IAAAE,uBAAS,EAAC,IAAI,EAAEZ,KAAK,CAAC,EACtBK,SAAS,GACZF,QACQ,CAAC;EAEhB;AACF;AAACU,OAAA,CAAA9C,OAAA,GAAAwB,IAAA", "ignoreList": []}