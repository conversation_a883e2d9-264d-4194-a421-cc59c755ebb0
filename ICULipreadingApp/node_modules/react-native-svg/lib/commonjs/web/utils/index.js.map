{"version": 3, "names": ["_extractTransform", "require", "hasTouchableProperty", "props", "onPress", "onPressIn", "onPressOut", "onLongPress", "exports", "camelCaseToDashed", "camelCase", "replace", "m", "toLowerCase", "stringifyTransformProps", "transformProps", "transformArray", "translate", "push", "translateX", "translateY", "scale", "scaleX", "scaleY", "rotation", "skewX", "skewY", "parseTransformProp", "transform", "Array", "isArray", "join", "stringifiedProps", "transformsArrayToProps", "length", "undefined", "getBoundingClientRect", "node", "isElement", "nodeType", "Error", "measureLayout", "callback", "relativeNode", "parentNode", "setTimeout", "relativeRect", "height", "left", "top", "width", "x", "y", "remeasure", "tag", "state", "touchable", "responderID", "_handleQueryLayout", "encodeSvg", "svgString", "indexOf"], "sourceRoot": "../../../../src", "sources": ["web/utils/index.ts"], "mappings": ";;;;;;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AAKO,MAAMC,oBAAoB,GAAIC,KAAgB,IACnDA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACG,UAAU,IAAIH,KAAK,CAACI,WAAW;AAACC,OAAA,CAAAN,oBAAA,GAAAA,oBAAA;AAErE,MAAMO,iBAAiB,GAAIC,SAAiB,IAAK;EACtD,OAAOA,SAAS,CAACC,OAAO,CAAC,QAAQ,EAAGC,CAAC,IAAK,GAAG,GAAGA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;AAClE,CAAC;AAACL,OAAA,CAAAC,iBAAA,GAAAA,iBAAA;AAEF,SAASK,uBAAuBA,CAACC,cAA8B,EAAE;EAC/D,MAAMC,cAAc,GAAG,EAAE;EACzB,IAAID,cAAc,CAACE,SAAS,IAAI,IAAI,EAAE;IACpCD,cAAc,CAACE,IAAI,CAAC,aAAaH,cAAc,CAACE,SAAS,GAAG,CAAC;EAC/D;EACA,IAAIF,cAAc,CAACI,UAAU,IAAI,IAAI,IAAIJ,cAAc,CAACK,UAAU,IAAI,IAAI,EAAE;IAC1EJ,cAAc,CAACE,IAAI,CACjB,aAAaH,cAAc,CAACI,UAAU,IAAI,CAAC,KACzCJ,cAAc,CAACK,UAAU,IAAI,CAAC,GAElC,CAAC;EACH;EACA,IAAIL,cAAc,CAACM,KAAK,IAAI,IAAI,EAAE;IAChCL,cAAc,CAACE,IAAI,CAAC,SAASH,cAAc,CAACM,KAAK,GAAG,CAAC;EACvD;EACA,IAAIN,cAAc,CAACO,MAAM,IAAI,IAAI,IAAIP,cAAc,CAACQ,MAAM,IAAI,IAAI,EAAE;IAClEP,cAAc,CAACE,IAAI,CACjB,SAASH,cAAc,CAACO,MAAM,IAAI,CAAC,KAAKP,cAAc,CAACQ,MAAM,IAAI,CAAC,GACpE,CAAC;EACH;EACA;EACA,IAAIR,cAAc,CAACS,QAAQ,IAAI,IAAI,EAAE;IACnCR,cAAc,CAACE,IAAI,CAAC,UAAUH,cAAc,CAACS,QAAQ,GAAG,CAAC;EAC3D;EACA,IAAIT,cAAc,CAACU,KAAK,IAAI,IAAI,EAAE;IAChCT,cAAc,CAACE,IAAI,CAAC,SAASH,cAAc,CAACU,KAAK,GAAG,CAAC;EACvD;EACA,IAAIV,cAAc,CAACW,KAAK,IAAI,IAAI,EAAE;IAChCV,cAAc,CAACE,IAAI,CAAC,SAASH,cAAc,CAACW,KAAK,GAAG,CAAC;EACvD;EACA,OAAOV,cAAc;AACvB;AAEO,SAASW,kBAAkBA,CAChCC,SAAsC,EACtCzB,KAAiB,EACjB;EACA,MAAMa,cAAwB,GAAG,EAAE;EAEnCb,KAAK,IAAIa,cAAc,CAACE,IAAI,CAAC,GAAGJ,uBAAuB,CAACX,KAAK,CAAC,CAAC;EAE/D,IAAI0B,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;IAC5B,IAAI,OAAOA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpCZ,cAAc,CAACE,IAAI,CAAC,UAAUU,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACvD,CAAC,MAAM;MACL,MAAMC,gBAAgB,GAAG,IAAAC,wCAAsB;MAC7C;MACAL,SACF,CAAC;MACDZ,cAAc,CAACE,IAAI,CAAC,GAAGJ,uBAAuB,CAACkB,gBAAgB,CAAC,CAAC;IACnE;EACF,CAAC,MAAM,IAAI,OAAOJ,SAAS,KAAK,QAAQ,EAAE;IACxCZ,cAAc,CAACE,IAAI,CAACU,SAAS,CAAC;EAChC;EAEA,OAAOZ,cAAc,CAACkB,MAAM,GAAGlB,cAAc,CAACe,IAAI,CAAC,GAAG,CAAC,GAAGI,SAAS;AACrE;AAEO,MAAMC,qBAAqB,GAAIC,IAAgB,IAAK;EACzD,IAAIA,IAAI,EAAE;IACR,MAAMC,SAAS,GAAGD,IAAI,CAACE,QAAQ,KAAK,CAAC,CAAC,CAAC;IACvC,IAAID,SAAS,IAAI,OAAOD,IAAI,CAACD,qBAAqB,KAAK,UAAU,EAAE;MACjE,OAAOC,IAAI,CAACD,qBAAqB,CAAC,CAAC;IACrC;EACF;EACA,MAAM,IAAII,KAAK,CAAC,oCAAoC,GAAGH,IAAI,IAAI,WAAW,CAAC;AAC7E,CAAC;AAAC7B,OAAA,CAAA4B,qBAAA,GAAAA,qBAAA;AAEF,MAAMK,aAAa,GAAGA,CACpBJ,IAAgB,EAChBK,QAOS,KACN;EACH,MAAMC,YAAY,GAAGN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,UAAU;EACrC,IAAID,YAAY,EAAE;IAChBE,UAAU,CAAC,MAAM;MACf;MACA,MAAMC,YAAY,GAAGV,qBAAqB,CAACO,YAAY,CAAC;MACxD,MAAM;QAAEI,MAAM;QAAEC,IAAI;QAAEC,GAAG;QAAEC;MAAM,CAAC,GAAGd,qBAAqB,CAACC,IAAI,CAAC;MAChE,MAAMc,CAAC,GAAGH,IAAI,GAAGF,YAAY,CAACE,IAAI;MAClC,MAAMI,CAAC,GAAGH,GAAG,GAAGH,YAAY,CAACG,GAAG;MAChCP,QAAQ,CAACS,CAAC,EAAEC,CAAC,EAAEF,KAAK,EAAEH,MAAM,EAAEC,IAAI,EAAEC,GAAG,CAAC;IAC1C,CAAC,EAAE,CAAC,CAAC;EACP;AACF,CAAC;;AAED;AACO,SAASI,SAASA,CAAA,EAAY;EACnC,MAAMC,GAAG,GAAG,IAAI,CAACC,KAAK,CAACC,SAAS,CAACC,WAAW;EAC5C,IAAIH,GAAG,KAAK,IAAI,EAAE;IAChB;EACF;EACAb,aAAa,CAACa,GAAG,EAAE,IAAI,CAACI,kBAAkB,CAAC;AAC7C;;AAEA;AACO,SAASC,SAASA,CAACC,SAAiB,EAAE;EAC3C,OAAOA,SAAS,CACbjD,OAAO,CACN,MAAM,EACN,CAACiD,SAAS,CAACC,OAAO,CAAC,OAAO,CAAC,GACvB,MAAM,GACN,yCACN,CAAC,CACAlD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AACzB", "ignoreList": []}