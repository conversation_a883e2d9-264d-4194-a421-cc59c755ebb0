{"version": 3, "names": ["hasTouchableProperty", "parseTransformProp", "resolve", "resolveAssetUri", "prepare", "self", "props", "transform", "origin", "originX", "originY", "fontFamily", "fontSize", "fontWeight", "fontStyle", "style", "forwardedRef", "gradientTransform", "patternTransform", "onPress", "rest", "clean", "onStartShouldSetResponder", "touchableHandleStartShouldSetResponder", "onResponderTerminationRequest", "touchableHandleResponderTerminationRequest", "onResponderGrant", "touchableHandleResponderGrant", "onResponderMove", "touchableHandleResponderMove", "onResponderRelease", "touchableHandleResponderRelease", "onResponderTerminate", "touchableHandleResponderTerminate", "toString", "replace", "parsedTransform", "parsedGradientTransform", "parsedPatternTransform", "ref", "el", "elementRef", "current", "styles", "onClick", "href", "undefined", "_resolveAssetUri", "uri"], "sourceRoot": "../../../../src", "sources": ["web/utils/prepare.ts"], "mappings": "AAMA,SAASA,oBAAoB,EAAEC,kBAAkB,QAAQ,GAAG;AAC5D,SAASC,OAAO,QAAQ,mBAAmB;AAE3C,SAASC,eAAe,QAAQ,2BAA2B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAGA,CACrBC,IAAiB,EACjBC,KAAK,GAAGD,IAAI,CAACC,KAAK,KACf;EACH,MAAM;IACJC,SAAS;IACTC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACRC,UAAU;IACVC,SAAS;IACTC,KAAK;IACLC,YAAY;IACZC,iBAAiB;IACjBC,gBAAgB;IAChBC,OAAO;IACP,GAAGC;EACL,CAAC,GAAGd,KAAK;EAET,MAAMe,KAeL,GAAG;IACF,IAAIrB,oBAAoB,CAACM,KAAK,CAAC,GAC3B;MACEgB,yBAAyB,EACvBjB,IAAI,CAACkB,sCAAsC;MAC7CC,6BAA6B,EAC3BnB,IAAI,CAACoB,0CAA0C;MACjDC,gBAAgB,EAAErB,IAAI,CAACsB,6BAA6B;MACpDC,eAAe,EAAEvB,IAAI,CAACwB,4BAA4B;MAClDC,kBAAkB,EAAEzB,IAAI,CAAC0B,+BAA+B;MACxDC,oBAAoB,EAAE3B,IAAI,CAAC4B;IAC7B,CAAC,GACD,IAAI,CAAC;IACT,GAAGb;EACL,CAAC;EAED,IAAIZ,MAAM,IAAI,IAAI,EAAE;IAClBa,KAAK,CAAC,kBAAkB,CAAC,GAAGb,MAAM,CAAC0B,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACjE,CAAC,MAAM,IAAI1B,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,EAAE;IAC7CW,KAAK,CAAC,kBAAkB,CAAC,GAAG,GAAGZ,OAAO,IAAI,CAAC,IAAIC,OAAO,IAAI,CAAC,EAAE;EAC/D;;EAEA;EACA,MAAM0B,eAAe,GAAGnC,kBAAkB,CAACM,SAAS,EAAED,KAAK,CAAC;EAC5D,IAAI8B,eAAe,EAAE;IACnBf,KAAK,CAACd,SAAS,GAAG6B,eAAe;EACnC;EACA,MAAMC,uBAAuB,GAAGpC,kBAAkB,CAACgB,iBAAiB,CAAC;EACrE,IAAIoB,uBAAuB,EAAE;IAC3BhB,KAAK,CAACJ,iBAAiB,GAAGoB,uBAAuB;EACnD;EACA,MAAMC,sBAAsB,GAAGrC,kBAAkB,CAACiB,gBAAgB,CAAC;EACnE,IAAIoB,sBAAsB,EAAE;IAC1BjB,KAAK,CAACH,gBAAgB,GAAGoB,sBAAsB;EACjD;EAEAjB,KAAK,CAACkB,GAAG,GAAIC,EAAqB,IAAK;IACrCnC,IAAI,CAACoC,UAAU,CAACC,OAAO,GAAGF,EAAE;IAC5B,IAAI,OAAOxB,YAAY,KAAK,UAAU,EAAE;MACtCA,YAAY,CAACwB,EAAE,CAAC;IAClB,CAAC,MAAM,IAAIxB,YAAY,EAAE;MACvBA,YAAY,CAAC0B,OAAO,GAAGF,EAAE;IAC3B;EACF,CAAC;EAED,MAAMG,MAKL,GAAG,CAAC,CAAC;EAEN,IAAIhC,UAAU,IAAI,IAAI,EAAE;IACtBgC,MAAM,CAAChC,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIC,QAAQ,IAAI,IAAI,EAAE;IACpB+B,MAAM,CAAC/B,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIC,UAAU,IAAI,IAAI,EAAE;IACtB8B,MAAM,CAAC9B,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIC,SAAS,IAAI,IAAI,EAAE;IACrB6B,MAAM,CAAC7B,SAAS,GAAGA,SAAS;EAC9B;EACAO,KAAK,CAACN,KAAK,GAAGb,OAAO,CAACa,KAAK,EAAE4B,MAAM,CAAC;EACpC,IAAIxB,OAAO,KAAK,IAAI,EAAE;IACpBE,KAAK,CAACuB,OAAO,GAAGtC,KAAK,CAACa,OAAO;EAC/B;EACA,IAAIb,KAAK,CAACuC,IAAI,KAAK,IAAI,IAAIvC,KAAK,CAACuC,IAAI,KAAKC,SAAS,EAAE;IAAA,IAAAC,gBAAA;IACnD1B,KAAK,CAACwB,IAAI,IAAAE,gBAAA,GAAG5C,eAAe,CAACG,KAAK,CAACuC,IAAI,CAAC,cAAAE,gBAAA,uBAA3BA,gBAAA,CAA6BC,GAAG;EAC/C;EACA,OAAO3B,KAAK;AACd,CAAC", "ignoreList": []}