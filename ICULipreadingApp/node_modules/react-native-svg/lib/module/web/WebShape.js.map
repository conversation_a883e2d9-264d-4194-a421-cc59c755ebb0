{"version": 3, "names": ["React", "unstable_createElement", "createElement", "prepare", "convertInt32ColorToRGBA", "camelCaseToDashed", "hasTouchableProperty", "remeasure", "SvgTouchableMixin", "WebShape", "Component", "prepareProps", "props", "elementRef", "createRef", "lastMergedProps", "setNativeProps", "merged", "Object", "assign", "style", "clean", "current", "cleanAttribute", "keys", "cleanValue", "partialStyle", "concat", "value", "setAttribute", "payload", "constructor", "_remeasureMetricsOnActivation", "bind", "render", "tag", "Error"], "sourceRoot": "../../../src", "sources": ["web/WebShape.ts"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;AAEE;AACAC,sBAAsB,IAAIC,aAAa,QAClC,cAAc;AAGrB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,iBAAiB,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,SAAS;AAC5E,OAAOC,iBAAiB,MAAM,0BAA0B;AAExD,OAAO,MAAMC,QAAQ,SAEXT,KAAK,CAACU,SAAS,CAAI;EAGjBC,YAAYA,CAACC,KAAQ,EAAE;IAC/B,OAAOA,KAAK;EACd;EAEAC,UAAU,gBACRb,KAAK,CAACc,SAAS,CAAa,CAAC;EAE/BC,eAAe,GAAe,CAAC,CAAC;;EAEhC;AACF;AACA;EACEC,cAAcA,CAACJ,KAAmB,EAAE;IAClC,MAAMK,MAAM,GAAGC,MAAM,CAACC,MAAM,CAC1B,CAAC,CAAC,EACF,IAAI,CAACP,KAAK,EACV,IAAI,CAACG,eAAe,EACpBH,KAAK,CAACQ,KACR,CAAC;IACD,IAAI,CAACL,eAAe,GAAGE,MAAM;IAC7B,MAAMI,KAAK,GAAGlB,OAAO,CAAC,IAAI,EAAE,IAAI,CAACQ,YAAY,CAACM,MAAM,CAAC,CAAC;IACtD,MAAMK,OAAO,GAAG,IAAI,CAACT,UAAU,CAACS,OAAO;IACvC,IAAIA,OAAO,EAAE;MACX,KAAK,MAAMC,cAAc,IAAIL,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC,EAAE;QAC/C,MAAMI,UAAU,GAAGJ,KAAK,CAACE,cAAc,CAAuB;QAC9D,QAAQA,cAAc;UACpB,KAAK,KAAK;UACV,KAAK,UAAU;YACb;UACF,KAAK,OAAO;YACV;YACA,KAAK,MAAMG,YAAY,IAAK,EAAE,CAAeC,MAAM,CACjDN,KAAK,CAACD,KAAK,IAAI,EACjB,CAAC,EAAE;cACDF,MAAM,CAACC,MAAM,CAACG,OAAO,CAACF,KAAK,EAAEM,YAAY,CAAC;YAC5C;YACA;UACF,KAAK,MAAM;YACT,IAAID,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;cAChD,MAAMG,KAAK,GAAGH,UAAiC;cAC/CH,OAAO,CAACO,YAAY,CAClB,MAAM,EACNzB,uBAAuB,CAACwB,KAAK,CAACE,OAAO,CACvC,CAAC;YACH;YACA;UACF,KAAK,QAAQ;YACX,IAAIL,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;cAChD,MAAMG,KAAK,GAAGH,UAAiC;cAC/CH,OAAO,CAACO,YAAY,CAClB,QAAQ,EACRzB,uBAAuB,CAACwB,KAAK,CAACE,OAAO,CACvC,CAAC;YACH;YACA;UACF;YACE;YACA;YACA;YACAR,OAAO,CAACO,YAAY,CAACxB,iBAAiB,CAACkB,cAAc,CAAC,EAAEE,UAAU,CAAC;YACnE;QACJ;MACF;IACF;EACF;EAeAM,WAAWA,CAACnB,KAAQ,EAAE;IACpB,KAAK,CAACA,KAAK,CAAC;;IAEZ;IACA,IAAIN,oBAAoB,CAACM,KAAK,CAAC,EAAE;MAC/BJ,iBAAiB,CAAC,IAAI,CAAC;IACzB;IAEA,IAAI,CAACwB,6BAA6B,GAAGzB,SAAS,CAAC0B,IAAI,CAAC,IAAI,CAAC;EAC3D;EAEAC,MAAMA,CAAA,EAAgB;IACpB,IAAI,CAAC,IAAI,CAACC,GAAG,EAAE;MACb,MAAM,IAAIC,KAAK,CACb,2EACF,CAAC;IACH;IACA,IAAI,CAACrB,eAAe,GAAG,CAAC,CAAC;IACzB,OAAOb,aAAa,CAClB,IAAI,CAACiC,GAAG,EACRhC,OAAO,CAAC,IAAI,EAAE,IAAI,CAACQ,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC,CAC7C,CAAC;EACH;AACF", "ignoreList": []}