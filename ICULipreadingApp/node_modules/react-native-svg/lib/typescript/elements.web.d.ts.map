{"version": 3, "file": "elements.web.d.ts", "sourceRoot": "", "sources": ["../../src/elements.web.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACvD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AAC3E,OAAO,KAAK,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AACvF,OAAO,KAAK,EACV,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACb,MAAM,gDAAgD,CAAC;AACxD,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AACnF,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AACnF,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACzE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AAC7E,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACzE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACzE,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AACrF,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACzE,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAC3C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AACnD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AACrE,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACvD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACvD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AACrE,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AACnD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAE7C,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAE1C,qBAAa,MAAO,SAAQ,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC;IAC3D,GAAG,WAAqB;CACzB;AAED,qBAAa,QAAS,SAAQ,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC;IAC/D,GAAG,aAAuB;CAC3B;AAED,qBAAa,IAAK,SAAQ,QAAQ;IAChC,GAAG,SAAmB;CACvB;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,aAAc,SAAQ,QAAQ,CAAC,SAAS,GAAG,kBAAkB,CAAC;IACzE,GAAG,kBAA4B;CAChC;AAED,qBAAa,mBAAoB,SAAQ,QAAQ,CAC/C,SAAS,GAAG,wBAAwB,CACrC;IACC,GAAG,wBAAkC;CACtC;AAED,qBAAa,WAAY,SAAQ,QAAQ,CAAC,SAAS,GAAG,gBAAgB,CAAC;IACrE,GAAG,gBAA0B;CAC9B;AAED,qBAAa,gBAAiB,SAAQ,QAAQ,CAC5C,SAAS,GAAG,qBAAqB,CAClC;IACC,GAAG,qBAA+B;CACnC;AAED,qBAAa,iBAAkB,SAAQ,QAAQ,CAC7C,SAAS,GAAG,sBAAsB,CACnC;IACC,GAAG,sBAAgC;CACpC;AAED,qBAAa,iBAAkB,SAAQ,QAAQ,CAC7C,SAAS,GAAG,sBAAsB,CACnC;IACC,GAAG,sBAAgC;CACpC;AAED,qBAAa,cAAe,SAAQ,QAAQ,CAAC,SAAS,GAAG,mBAAmB,CAAC;IAC3E,GAAG,mBAA6B;CACjC;AAED,qBAAa,YAAa,SAAQ,QAAQ,CAAC,SAAS,GAAG,iBAAiB,CAAC;IACvE,GAAG,iBAA2B;CAC/B;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,cAAe,SAAQ,QAAQ,CAAC,SAAS,GAAG,mBAAmB,CAAC;IAC3E,GAAG,mBAA6B;CACjC;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,WAAY,SAAQ,QAAQ,CAAC,SAAS,GAAG,gBAAgB,CAAC;IACrE,GAAG,gBAA0B;CAC9B;AAED,qBAAa,YAAa,SAAQ,QAAQ,CAAC,SAAS,GAAG,iBAAiB,CAAC;IACvE,GAAG,iBAA2B;CAC/B;AAED,qBAAa,QAAS,SAAQ,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC;IAC/D,GAAG,aAAuB;CAC3B;AAED,qBAAa,YAAa,SAAQ,QAAQ,CAAC,SAAS,GAAG,iBAAiB,CAAC;IACvE,GAAG,iBAA2B;CAC/B;AAED,qBAAa,kBAAmB,SAAQ,QAAQ,CAC9C,SAAS,GAAG,uBAAuB,CACpC;IACC,GAAG,uBAAiC;CACrC;AAED,qBAAa,WAAY,SAAQ,QAAQ,CAAC,SAAS,GAAG,gBAAgB,CAAC;IACrE,GAAG,gBAA0B;CAC9B;AAED,qBAAa,MAAO,SAAQ,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC;IAC3D,GAAG,WAAqB;CACzB;AAED,qBAAa,YAAa,SAAQ,QAAQ,CAAC,SAAS,GAAG,iBAAiB,CAAC;IACvE,GAAG,iBAA2B;CAC/B;AAED,qBAAa,MAAO,SAAQ,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC;IAC3D,GAAG,WAAqB;CACzB;AAED,qBAAa,aAAc,SAAQ,QAAQ,CAAC,SAAS,GAAG,kBAAkB,CAAC;IACzE,GAAG,kBAA4B;CAChC;AAED,qBAAa,CAAE,SAAQ,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC;IACjD,GAAG,MAAgB;IACnB,YAAY,CAAC,KAAK,EAAE,SAAS,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBArKqB,MAAO,WAAW,eACnE,MAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6KjC;AAED,qBAAa,KAAM,SAAQ,QAAQ,CAAC,SAAS,GAAG,UAAU,CAAC;IACzD,GAAG,UAAoB;CACxB;AAED,qBAAa,IAAK,SAAQ,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;IACvD,GAAG,SAAmB;CACvB;AAED,qBAAa,cAAe,SAAQ,QAAQ,CAAC,SAAS,GAAG,mBAAmB,CAAC;IAC3E,GAAG,mBAA6B;CACjC;AAED,qBAAa,MAAO,SAAQ,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC;IAC3D,GAAG,WAAqB;CACzB;AAED,qBAAa,IAAK,SAAQ,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;IACvD,GAAG,SAAmB;CACvB;AAED,qBAAa,IAAK,SAAQ,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;IACvD,GAAG,SAAmB;CACvB;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,OAAQ,SAAQ,QAAQ,CAAC,SAAS,GAAG,YAAY,CAAC;IAC7D,GAAG,YAAsB;CAC1B;AAED,qBAAa,QAAS,SAAQ,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC;IAC/D,GAAG,aAAuB;CAC3B;AAED,qBAAa,cAAe,SAAQ,QAAQ,CAAC,SAAS,GAAG,mBAAmB,CAAC;IAC3E,GAAG,mBAA6B;CACjC;AAED,qBAAa,IAAK,SAAQ,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;IACvD,GAAG,SAAmB;CACvB;AAED,qBAAa,IAAK,SAAQ,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;IACvD,GAAG,SAAmB;CACvB;AAED,qBAAa,GAAI,SAAQ,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC;IACrD,GAAG,QAAkB;IACrB,SAAS,CACP,QAAQ,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,EAChC,OAAO,GAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,CAAA;KAAO;CAiCpD;AAED,qBAAa,MAAO,SAAQ,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC;IAC3D,GAAG,WAAqB;CACzB;AAED,qBAAa,KAAM,SAAQ,QAAQ,CAAC,SAAS,GAAG,UAAU,CAAC;IACzD,GAAG,UAAoB;CACxB;AAED,qBAAa,IAAK,SAAQ,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;IACvD,GAAG,SAAmB;CACvB;AAED,qBAAa,QAAS,SAAQ,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC;IAC/D,GAAG,aAAuB;CAC3B;AAED,qBAAa,GAAI,SAAQ,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC;IACrD,GAAG,QAAkB;CACtB;AAED,eAAe,GAAG,CAAC"}