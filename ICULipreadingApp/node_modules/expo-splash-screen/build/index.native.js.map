{"version": 3, "file": "index.native.js", "sourceRoot": "", "sources": ["../src/index.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,MAAM,CAAC;AACzC,OAAO,EAAE,2BAA2B,EAAE,MAAM,mBAAmB,CAAC;AAIhE,MAAM,YAAY,GAAG,2BAA2B,CAA2B,kBAAkB,CAAC,CAAC;AAE/F,MAAM,UAAU,UAAU,CAAC,OAA4B;IACrD,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IAED,IAAI,iBAAiB,EAAE,EAAE,CAAC;QACxB,OAAO,CAAC,IAAI,CACV,sHAAsH,CACvH,CAAC;QACF,OAAO;IACT,CAAC;IAED,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC;AAED,MAAM,UAAU,IAAI;IAClB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,CAAC;AACtB,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,SAAS;IAC7B,IAAI,EAAE,CAAC;AACT,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IAED,OAAO,YAAY,CAAC,oBAAoB,EAAE,CAAC;AAC7C,CAAC", "sourcesContent": ["import { isRunningInExpoGo } from 'expo';\nimport { requireOptionalNativeModule } from 'expo-modules-core';\n\nimport { SplashScreenNativeModule, SplashScreenOptions } from './SplashScreen.types';\n\nconst SplashModule = requireOptionalNativeModule<SplashScreenNativeModule>('ExpoSplashScreen');\n\nexport function setOptions(options: SplashScreenOptions) {\n  if (!SplashModule) {\n    return;\n  }\n\n  if (isRunningInExpoGo()) {\n    console.warn(\n      \"'Splashscreen.setOptions' cannot be used in Expo Go. To customize the splash screen, you can use development builds.\"\n    );\n    return;\n  }\n\n  SplashModule.setOptions(options);\n}\n\nexport function hide() {\n  if (!SplashModule) {\n    return;\n  }\n\n  SplashModule.hide();\n}\n\nexport async function hideAsync(): Promise<void> {\n  hide();\n}\n\nexport async function preventAutoHideAsync() {\n  if (!SplashModule) {\n    return;\n  }\n\n  return SplashModule.preventAutoHideAsync();\n}\n"]}