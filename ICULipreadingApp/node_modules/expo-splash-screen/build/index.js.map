{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,cAAc;AAId;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,OAA4B,IAAS,CAAC;AAEjE,cAAc;AACd;;;;GAIG;AACH,MAAM,UAAU,IAAI,KAAU,CAAC;AAE/B;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,SAAS,KAAmB,CAAC", "sourcesContent": ["// @needsAudit\n\nimport type { SplashScreenOptions } from './SplashScreen.types';\n\n/**\n * Makes the native splash screen (configured in `app.json`) remain visible until `hideAsync` is called.\n *\n * > **Important note**: It is recommended to call this in global scope without awaiting, rather than\n * > inside React components or hooks, because otherwise this might be called too late,\n * > when the splash screen is already hidden.\n *\n * @example\n * ```ts\n * import * as SplashScreen from 'expo-splash-screen';\n *\n * SplashScreen.preventAutoHideAsync();\n *\n * export default function App() {\n *  // ...\n * }\n * ```\n */\nexport async function preventAutoHideAsync(): Promise<boolean> {\n  return false;\n}\n\n/**\n *\n * Configures the splashscreens default animation behavior.\n *\n */\nexport function setOptions(options: SplashScreenOptions): void {}\n\n// @needsAudit\n/**\n * Hides the native splash screen immediately. Be careful to ensure that your app has content ready\n * to display when you hide the splash screen, or you may see a blank screen briefly. See the\n * [\"Usage\"](#usage) section for an example.\n */\nexport function hide(): void {}\n\n/**\n * Hides the native splash screen immediately. This method is provided for backwards compatability. See the\n * [\"Usage\"](#usage) section for an example.\n */\nexport async function hideAsync(): Promise<void> {}\n\nexport { SplashScreenOptions };\n"]}