{"version": 3, "file": "Constants.js", "sourceRoot": "", "sources": ["../src/Constants.ts"], "names": [], "mappings": "AASA,OAAO,EAAE,UAAU,EAAE,2BAA2B,EAAE,MAAM,mBAAmB,CAAC;AAG5E,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAEvD,OAAO,EAEL,YAAY,EAEZ,oBAAoB,EAIpB,kBAAkB,GAEnB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,iBAAiB,MAAM,qBAAqB,CAAC;AAGpD,OAAO,EAEL,YAAY,EAEZ,oBAAoB,EAIpB,kBAAkB,GAEnB,CAAC;AAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACvB,OAAO,CAAC,IAAI,CACV,wGAAwG,CACzG,CAAC;AACJ,CAAC;AAED,MAAM,WAAW,GAAG,2BAA2B,CAAoB,aAAa,CAAC,CAAC;AAElF,IAAI,kBAAkB,GAA2B,IAAI,CAAC;AACtD,gEAAgE;AAChE,IAAI,WAAW,EAAE,CAAC;IAChB,IAAI,eAAmC,CAAC;IACxC,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;QACzB,eAAe,GAAG,WAAW,CAAC,QAAQ,CAAC;IACzC,CAAC;SAAM,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;QACtC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;IAC3D,CAAC;IACD,IAAI,eAAe,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/D,kBAAkB,GAAG,eAAsB,CAAC;IAC9C,CAAC;AACH,CAAC;AAED,gEAAgE;AAChE,IAAI,sBAAsB,GAA+B,IAAI,CAAC;AAC9D,IAAI,aAAa,CAAC,aAAa,EAAE,CAAC;IAChC,IAAI,mBAAmB,CAAC;IACxB,IAAI,aAAa,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAC/C,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAC/E,CAAC;IAED,IAAI,mBAAmB,IAAI,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvE,sBAAsB,GAAG,mBAA0B,CAAC;IACtD,CAAC;AACH,CAAC;AAED,4EAA4E;AAC5E,IAAI,YAAY,GAAsB,IAAI,CAAC;AAC3C,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,QAAQ,EAAE,CAAC;IACpD,MAAM,SAAS,GAAoB,iBAAiB,CAAC,QAAQ,CAAC;IAE9D,yEAAyE;IACzE,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAClC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;SAAM,CAAC;QACN,YAAY,GAAG,SAAgB,CAAC;IAClC,CAAC;AACH,CAAC;AAGD,IAAI,WAAW,GAAuB,kBAAkB,IAAI,sBAAsB,IAAI,YAAY,CAAC;AAEnG,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,eAAe,EAAE,GAAG,CAAC,iBAAiB,IAAI,EAAE,CAAQ,CAAC;AAEpF,MAAM,SAAS,GAAc;IAC3B,GAAG,eAAe;IAClB,uCAAuC;IACvC,YAAY,EAAE,YAAY,IAAI,IAAI;CACnC,CAAC;AAEF,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE;IACjC;;;;;OAKG;IACH,sBAAsB,EAAE;QACtB,GAAG;YACD,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,aAAa,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACzD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,UAAU,EAAE,KAAK;KAClB;IACD,uBAAuB,EAAE;QACvB,GAAG;YACD,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,aAAa,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5D,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,UAAU,EAAE,KAAK;KAClB;IACD,QAAQ,EAAE;QACR,GAAG;YACD,MAAM,aAAa,GAAG,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACzD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;IACD,SAAS,EAAE;QACT,GAAG;YACD,MAAM,aAAa,GAAG,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5D,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,aAAa,CAAC;QACvB,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;IACD,UAAU,EAAE;QACV,GAAG;YAQD,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,wFAAwF;YACxF,mFAAmF;YACnF,IAAI,WAAW,IAAI,WAAW,CAAC,gBAAgB,EAAE,CAAC;gBAChD,OAAO,YAAY,CAAC;YACtB,CAAC;YAED,IAAI,qBAAqB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACzC,OAAO,aAAa,CAAC,KAAK,EAAE,UAAU,IAAI,IAAI,CAAC;YACjD,CAAC;iBAAM,IAAI,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC7C,OAAO,aAAoB,CAAC;YAC9B,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;IACD,YAAY,EAAE;QACZ,GAAG;YACD,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,qBAAqB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACzC,OAAO,aAAa,CAAC,KAAK,EAAE,MAAM,IAAI,IAAI,CAAC;YAC7C,CAAC;iBAAM,IAAI,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC7C,OAAO,aAAoB,CAAC;YAC9B,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;IACD,SAAS,EAAE;QACT,GAAG;YACD,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,qBAAqB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACzC,OAAO,aAAa,CAAC,KAAK,EAAE,GAAG,IAAI,IAAI,CAAC;YAC1C,CAAC;iBAAM,IAAI,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC7C,OAAO,aAAoB,CAAC;YAC9B,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QACD,UAAU,EAAE,IAAI;KACjB;IACD,kBAAkB,EAAE;QAClB,GAAG;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,GAAG,CAAC,KAAyB;YAC3B,WAAW,GAAG,KAAK,CAAC;QACtB,CAAC;QACD,UAAU,EAAE,KAAK;KAClB;CACF,CAAC,CAAC;AAEH,SAAS,kBAAkB,CAAC,QAAqB;IAC/C,OAAO,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,qBAAqB,CAAC,QAAqB;IAClD,OAAO,UAAU,IAAI,QAAQ,CAAC;AAChC,CAAC;AAED,SAAS,WAAW,CAAC,eAAe,GAAG,KAAK;IAC1C,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,MAAM,mBAAmB,GAAG,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;QACxE,IACE,eAAe,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,IAAI;YAClE,QAAQ,CAAC,EAAE,KAAK,KAAK,EACrB,CAAC;YACD,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CACV,yBAAyB,mBAAmB,kKAAkK,CAC/M,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IACL,eAAe,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,WAAW;YACzE,eAAe,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,UAAU,EACxE,CAAC;YACD,sEAAsE;YACtE,mEAAmE;YACnE,MAAM,IAAI,UAAU,CAClB,oCAAoC,EACpC,yBAAyB,mBAAmB,sBAAsB,CACnE,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,eAAe,SAAsB,CAAC", "sourcesContent": ["import type { ExpoConfig } from 'expo/config';\n// @ts-ignore -- optional interface, will gracefully degrade to `any` if not installed\nimport type {\n  EmbeddedManifest,\n  EASConfig,\n  ExpoGoConfig,\n  ExpoUpdatesManifest,\n  // @ts-ignore -- optional interface, will gracefully degrade to `any` if not installed\n} from 'expo-manifests';\nimport { CodedError, requireOptionalNativeModule } from 'expo-modules-core';\n// @ts-ignore -- optional interface, will gracefully degrade to `any` if not installed\nimport type { Manifest as UpdatesManifest, ExpoUpdatesModule } from 'expo-updates';\nimport { Platform, NativeModules } from 'react-native';\n\nimport {\n  AndroidManifest,\n  AppOwnership,\n  Constants,\n  ExecutionEnvironment,\n  IOSManifest,\n  NativeConstants,\n  PlatformManifest,\n  UserInterfaceIdiom,\n  WebManifest,\n} from './Constants.types';\nimport ExponentConstants from './ExponentConstants';\ntype DevLauncherManifest = ExpoUpdatesManifest;\n\nexport {\n  AndroidManifest,\n  AppOwnership,\n  Constants,\n  ExecutionEnvironment,\n  IOSManifest,\n  NativeConstants,\n  PlatformManifest,\n  UserInterfaceIdiom,\n  WebManifest,\n};\n\nif (!ExponentConstants) {\n  console.warn(\n    \"No native ExponentConstants module found, are you sure the expo-constants's module is linked properly?\"\n  );\n}\n\nconst ExpoUpdates = requireOptionalNativeModule<ExpoUpdatesModule>('ExpoUpdates');\n\nlet rawUpdatesManifest: UpdatesManifest | null = null;\n// If expo-updates defines a non-empty manifest, prefer that one\nif (ExpoUpdates) {\n  let updatesManifest: object | undefined;\n  if (ExpoUpdates.manifest) {\n    updatesManifest = ExpoUpdates.manifest;\n  } else if (ExpoUpdates.manifestString) {\n    updatesManifest = JSON.parse(ExpoUpdates.manifestString);\n  }\n  if (updatesManifest && Object.keys(updatesManifest).length > 0) {\n    rawUpdatesManifest = updatesManifest as any;\n  }\n}\n\n// If dev-launcher defines a non-empty manifest, prefer that one\nlet rawDevLauncherManifest: DevLauncherManifest | null = null;\nif (NativeModules.EXDevLauncher) {\n  let devLauncherManifest;\n  if (NativeModules.EXDevLauncher.manifestString) {\n    devLauncherManifest = JSON.parse(NativeModules.EXDevLauncher.manifestString);\n  }\n\n  if (devLauncherManifest && Object.keys(devLauncherManifest).length > 0) {\n    rawDevLauncherManifest = devLauncherManifest as any;\n  }\n}\n\n// Fall back to ExponentConstants.manifest if we don't have one from Updates\nlet rawAppConfig: ExpoConfig | null = null;\nif (ExponentConstants && ExponentConstants.manifest) {\n  const appConfig: object | string = ExponentConstants.manifest;\n\n  // On Android we pass the manifest in JSON form so this step is necessary\n  if (typeof appConfig === 'string') {\n    rawAppConfig = JSON.parse(appConfig);\n  } else {\n    rawAppConfig = appConfig as any;\n  }\n}\n\ntype RawManifest = UpdatesManifest | DevLauncherManifest | ExpoConfig;\nlet rawManifest: RawManifest | null = rawUpdatesManifest ?? rawDevLauncherManifest ?? rawAppConfig;\n\nconst { name, appOwnership, ...nativeConstants } = (ExponentConstants || {}) as any;\n\nconst constants: Constants = {\n  ...nativeConstants,\n  // Ensure this is null in bare workflow\n  appOwnership: appOwnership ?? null,\n};\n\nObject.defineProperties(constants, {\n  /**\n   * Use `manifest` property by default.\n   * This property is only used for internal purposes.\n   * It behaves similarly to the original one, but suppresses warning upon no manifest available.\n   * `expo-asset` uses it to prevent users from seeing mentioned warning.\n   */\n  __unsafeNoWarnManifest: {\n    get(): EmbeddedManifest | null {\n      const maybeManifest = getManifest(true);\n      if (!maybeManifest || !isEmbeddedManifest(maybeManifest)) {\n        return null;\n      }\n      return maybeManifest;\n    },\n    enumerable: false,\n  },\n  __unsafeNoWarnManifest2: {\n    get(): ExpoUpdatesManifest | null {\n      const maybeManifest = getManifest(true);\n      if (!maybeManifest || !isExpoUpdatesManifest(maybeManifest)) {\n        return null;\n      }\n      return maybeManifest;\n    },\n    enumerable: false,\n  },\n  manifest: {\n    get(): EmbeddedManifest | null {\n      const maybeManifest = getManifest();\n      if (!maybeManifest || !isEmbeddedManifest(maybeManifest)) {\n        return null;\n      }\n      return maybeManifest;\n    },\n    enumerable: true,\n  },\n  manifest2: {\n    get(): ExpoUpdatesManifest | null {\n      const maybeManifest = getManifest();\n      if (!maybeManifest || !isExpoUpdatesManifest(maybeManifest)) {\n        return null;\n      }\n      return maybeManifest;\n    },\n    enumerable: true,\n  },\n  expoConfig: {\n    get():\n      | (ExpoConfig & {\n          /**\n           * Only present during development using @expo/cli.\n           */\n          hostUri?: string;\n        })\n      | null {\n      const maybeManifest = getManifest(true);\n      if (!maybeManifest) {\n        return null;\n      }\n\n      // if running an embedded update, maybeManifest is a EmbeddedManifest which doesn't have\n      // the expo config. Instead, the embedded expo-constants app.config should be used.\n      if (ExpoUpdates && ExpoUpdates.isEmbeddedLaunch) {\n        return rawAppConfig;\n      }\n\n      if (isExpoUpdatesManifest(maybeManifest)) {\n        return maybeManifest.extra?.expoClient ?? null;\n      } else if (isEmbeddedManifest(maybeManifest)) {\n        return maybeManifest as any;\n      }\n\n      return null;\n    },\n    enumerable: true,\n  },\n  expoGoConfig: {\n    get(): ExpoGoConfig | null {\n      const maybeManifest = getManifest(true);\n      if (!maybeManifest) {\n        return null;\n      }\n\n      if (isExpoUpdatesManifest(maybeManifest)) {\n        return maybeManifest.extra?.expoGo ?? null;\n      } else if (isEmbeddedManifest(maybeManifest)) {\n        return maybeManifest as any;\n      }\n\n      return null;\n    },\n    enumerable: true,\n  },\n  easConfig: {\n    get(): EASConfig | null {\n      const maybeManifest = getManifest(true);\n      if (!maybeManifest) {\n        return null;\n      }\n\n      if (isExpoUpdatesManifest(maybeManifest)) {\n        return maybeManifest.extra?.eas ?? null;\n      } else if (isEmbeddedManifest(maybeManifest)) {\n        return maybeManifest as any;\n      }\n\n      return null;\n    },\n    enumerable: true,\n  },\n  __rawManifest_TEST: {\n    get(): RawManifest | null {\n      return rawManifest;\n    },\n    set(value: RawManifest | null) {\n      rawManifest = value;\n    },\n    enumerable: false,\n  },\n});\n\nfunction isEmbeddedManifest(manifest: RawManifest): manifest is EmbeddedManifest {\n  return !isExpoUpdatesManifest(manifest);\n}\n\nfunction isExpoUpdatesManifest(manifest: RawManifest): manifest is ExpoUpdatesManifest {\n  return 'metadata' in manifest;\n}\n\nfunction getManifest(suppressWarning = false): RawManifest | null {\n  if (!rawManifest) {\n    const invalidManifestType = rawManifest === null ? 'null' : 'undefined';\n    if (\n      nativeConstants.executionEnvironment === ExecutionEnvironment.Bare &&\n      Platform.OS !== 'web'\n    ) {\n      if (!suppressWarning) {\n        console.warn(\n          `Constants.manifest is ${invalidManifestType} because the embedded app.config could not be read. Ensure that you have installed the expo-constants build scripts if you need to read from Constants.manifest.`\n        );\n      }\n    } else if (\n      nativeConstants.executionEnvironment === ExecutionEnvironment.StoreClient ||\n      nativeConstants.executionEnvironment === ExecutionEnvironment.Standalone\n    ) {\n      // If we somehow get here, this is a truly exceptional state to be in.\n      // Constants.manifest should *always* be defined in those contexts.\n      throw new CodedError(\n        'ERR_CONSTANTS_MANIFEST_UNAVAILABLE',\n        `Constants.manifest is ${invalidManifestType}, must be an object.`\n      );\n    }\n  }\n  return rawManifest;\n}\n\nexport default constants as Constants;\n"]}