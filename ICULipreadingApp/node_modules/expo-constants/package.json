{"name": "expo-constants", "version": "18.0.9", "description": "Provides system information that remains constant throughout the lifetime of your app.", "main": "build/Constants.js", "types": "build/Constants.d.ts", "sideEffects": false, "exports": {"./package.json": "./package.json", ".": {"types": "./build/Constants.d.ts", "react-server": "./build/Constants.server.js", "default": "./build/Constants.js"}}, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "test:rsc": "jest --config jest-rsc.config.js", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "constants"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-constants"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/constants/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"@expo/config": "~12.0.9", "@expo/env": "~2.0.7"}, "devDependencies": {"expo-module-scripts": "^5.0.7"}, "peerDependencies": {"expo": "*", "react-native": "*"}, "gitHead": "e1bc61d046f09264a85b7730a1b7ae931b0f0af4"}