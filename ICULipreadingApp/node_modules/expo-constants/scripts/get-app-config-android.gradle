// Gradle script for generating serialized public app config (from app.config.js/ts or app.json) and bundling it into the APK

import org.apache.tools.ant.taskdefs.condition.Os


def expoConstantsDir = project.providers.exec {
  workingDir(projectDir)
  commandLine("node", "-e", "console.log(require('path').dirname(require.resolve('expo-constants/package.json')));")
}.standardOutput.asText.get().trim()

def config = project.hasProperty("react") ? project.react : [];
def nodeExecutableAndArgs = config.nodeExecutableAndArgs ?: ["node"]

afterEvaluate {
  def projectRoot = file("${rootProject.projectDir}")
  def assetsDir = file("$buildDir/generated/assets/expo-constants")

  def currentCreateConfigTask = tasks.register('createExpoConfig', Exec) {
    description = 'expo-constants: Create app.config.'

    doFirst {
      assetsDir.deleteDir()
      assetsDir.mkdirs()
    }

    // Mark the task as always out-of-date so it always runs - ie. regenerate app.config on every build
    outputs.upToDateWhen { false }

    // Add generated assetsDir into assets.srcDirs
    project.android.sourceSets.main.assets.srcDirs += assetsDir

    // Set up outputs so gradle can cache the result
    outputs.dir assetsDir

    // Switch to project root and generate the app config
    workingDir projectRoot

    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
      commandLine("cmd", "/c", *nodeExecutableAndArgs, "$expoConstantsDir/scripts/getAppConfig.js", projectRoot, assetsDir)
    } else {
      commandLine(*nodeExecutableAndArgs, "$expoConstantsDir/scripts/getAppConfig.js", projectRoot, assetsDir)
    }
  }

  // Generate app.config at preBuild
  tasks.getByName('preBuild').dependsOn(currentCreateConfigTask)
}
