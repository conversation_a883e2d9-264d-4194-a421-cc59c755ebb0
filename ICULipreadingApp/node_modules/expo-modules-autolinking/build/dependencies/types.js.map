{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/dependencies/types.ts"], "names": [], "mappings": "", "sourcesContent": ["export const enum DependencyResolutionSource {\n  RECURSIVE_RESOLUTION,\n  SEARCH_PATH,\n  RN_CLI_LOCAL,\n}\n\nexport interface BaseDependencyResolution {\n  name: string;\n  version: string;\n  path: string;\n  originPath: string;\n}\n\nexport interface DependencyResolution extends BaseDependencyResolution {\n  source: DependencyResolutionSource;\n  duplicates: BaseDependencyResolution[] | null;\n  depth: number;\n  [prop: string]: unknown;\n}\n\nexport type ResolutionResult = Record<string, DependencyResolution | undefined>;\n"]}