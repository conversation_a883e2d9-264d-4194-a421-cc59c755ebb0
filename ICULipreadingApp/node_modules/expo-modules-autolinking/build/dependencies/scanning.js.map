{"version": 3, "file": "scanning.js", "sourceRoot": "", "sources": ["../../src/dependencies/scanning.ts"], "names": [], "mappings": ";;;;;AAiDA,oEA6EC;AA9HD,4CAAoB;AAOpB,mCAAmG;AAEnG,KAAK,UAAU,iBAAiB,CAC9B,QAAgB,EAChB,cAA6B,EAC7B,uBAA4D;IAE5D,IAAI,cAAc,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,EAAE,CAAC;QAC/D,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,IAAA,gBAAQ,EAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAClF,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAa,EAAC,UAAU,CAAC,CAAC;IACjD,MAAM,WAAW,GAAG,MAAM,IAAA,uBAAe,EAAC,IAAA,gBAAQ,EAAC,QAAQ,IAAI,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC;IAC5F,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO;YACL,MAAM,gDAAwC;YAC9C,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,OAAO,EAAE,WAAW,CAAC,OAAO,IAAI,EAAE;YAClC,IAAI,EAAE,QAAQ,IAAI,UAAU;YAC5B,UAAU;YACV,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,CAAC;SACT,CAAC;IACJ,CAAC;SAAM,IAAI,cAAc,IAAI,QAAQ,EAAE,CAAC;QACtC,OAAO;YACL,MAAM,gDAAwC;YAC9C,IAAI,EAAE,cAAc,CAAC,WAAW,EAAE;YAClC,OAAO,EAAE,EAAE;YACX,IAAI,EAAE,QAAQ;YACd,UAAU;YACV,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,CAAC;SACT,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAMM,KAAK,UAAU,4BAA4B,CAChD,OAAe,EACf,EAAE,uBAAuB,GAAG,sCAA8B,KAAwB,EAAE;IAEpF,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAa,EAAC,OAAO,CAAC,CAAC;IAC9C,MAAM,aAAa,GAAqB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5D,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,MAAM,oBAAoB,GAA2B,EAAE,CAAC;IAExD,MAAM,iBAAiB,GAAG,MAAM,IAAA,qBAAa,EAAC,IAAA,gBAAQ,EAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;IAClF,IAAI,iBAAiB,EAAE,CAAC;QACtB,gGAAgG;QAChG,wGAAwG;QACxG,MAAM,UAAU,GAAG,MAAM,iBAAiB,CAAC,QAAQ,EAAE,IAAI,EAAE,uBAAuB,CAAC,CAAC;QACpF,IAAI,UAAU;YAAE,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACxD,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAS,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9E,MAAM,OAAO,CAAC,GAAG,CACf,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC1B,IAAI,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC3B,MAAM,UAAU,GAAG,MAAM,iBAAiB,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;gBAC1F,IAAI,UAAU;oBAAE,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxD,CAAC;iBAAM,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBAClC,oCAAoC;gBACtC,CAAC;gBACD,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBAC1B,wBAAwB;gBAC1B,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBACjC,uDAAuD;oBACvD,MAAM,SAAS,GAAG,IAAA,gBAAQ,EAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;oBACjD,MAAM,YAAY,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnF,MAAM,OAAO,CAAC,GAAG,CACf,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;wBAC/B,MAAM,cAAc,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;wBACrD,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC;4BAClD,MAAM,UAAU,GAAG,MAAM,iBAAiB,CACxC,QAAQ,EACR,cAAc,EACd,uBAAuB,CACxB,CAAC;4BACF,IAAI,UAAU;gCAAE,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACxD,CAAC;oBACH,CAAC,CAAC,CACH,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM,UAAU,GAAG,MAAM,iBAAiB,CACxC,QAAQ,EACR,KAAK,CAAC,IAAI,EACV,uBAAuB,CACxB,CAAC;oBACF,IAAI,UAAU;wBAAE,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;QAC3D,MAAM,UAAU,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,SAAS,GAAG,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,SAAS,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YAC5D,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;gBACzD,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,UAAU,EAAE,UAAU,CAAC,UAAU;aAClC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YAC7B,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,OAAO,aAAa,CAAC;AACvB,CAAC", "sourcesContent": ["import fs from 'fs';\n\nimport {\n  type ResolutionResult,\n  type DependencyResolution,\n  DependencyResolutionSource,\n} from './types';\nimport { defaultShouldIncludeDependency, loadPackageJson, maybeRealpath, fastJoin } from './utils';\n\nasync function resolveDependency(\n  basePath: string,\n  dependencyName: string | null,\n  shouldIncludeDependency: (dependencyName: string) => boolean\n): Promise<DependencyResolution | null> {\n  if (dependencyName && !shouldIncludeDependency(dependencyName)) {\n    return null;\n  }\n  const originPath = dependencyName ? fastJoin(basePath, dependencyName) : basePath;\n  const realPath = await maybeRealpath(originPath);\n  const packageJson = await loadPackageJson(fastJoin(realPath || originPath, 'package.json'));\n  if (packageJson) {\n    return {\n      source: DependencyResolutionSource.SEARCH_PATH,\n      name: packageJson.name,\n      version: packageJson.version || '',\n      path: realPath || originPath,\n      originPath,\n      duplicates: null,\n      depth: 0,\n    };\n  } else if (dependencyName && realPath) {\n    return {\n      source: DependencyResolutionSource.SEARCH_PATH,\n      name: dependencyName.toLowerCase(),\n      version: '',\n      path: realPath,\n      originPath,\n      duplicates: null,\n      depth: 0,\n    };\n  } else {\n    return null;\n  }\n}\n\ninterface ResolutionOptions {\n  shouldIncludeDependency?(name: string): boolean;\n}\n\nexport async function scanDependenciesInSearchPath(\n  rawPath: string,\n  { shouldIncludeDependency = defaultShouldIncludeDependency }: ResolutionOptions = {}\n): Promise<ResolutionResult> {\n  const rootPath = await maybeRealpath(rawPath);\n  const searchResults: ResolutionResult = Object.create(null);\n  if (!rootPath) {\n    return searchResults;\n  }\n\n  const resolvedDependencies: DependencyResolution[] = [];\n\n  const localModuleTarget = await maybeRealpath(fastJoin(rootPath, 'package.json'));\n  if (localModuleTarget) {\n    // If we have a `package.json` file in the search path, we're already dealing with a node module\n    // and can skip the rest. This is a special case created by create-expo-module's `nativeModulesDir: ../`\n    const resolution = await resolveDependency(rootPath, null, shouldIncludeDependency);\n    if (resolution) resolvedDependencies.push(resolution);\n  } else {\n    const dirents = await fs.promises.readdir(rootPath!, { withFileTypes: true });\n    await Promise.all(\n      dirents.map(async (entry) => {\n        if (entry.isSymbolicLink()) {\n          const resolution = await resolveDependency(rootPath, entry.name, shouldIncludeDependency);\n          if (resolution) resolvedDependencies.push(resolution);\n        } else if (entry.isDirectory()) {\n          if (entry.name === 'node_modules') {\n            // Ignore nested node_modules folder\n          }\n          if (entry.name[0] === '.') {\n            // Ignore hidden folders\n          } else if (entry.name[0] === '@') {\n            // NOTE: We don't expect @-scope folders to be symlinks\n            const entryPath = fastJoin(rootPath, entry.name);\n            const childEntries = await fs.promises.readdir(entryPath, { withFileTypes: true });\n            await Promise.all(\n              childEntries.map(async (child) => {\n                const dependencyName = `${entry.name}/${child.name}`;\n                if (child.isDirectory() || child.isSymbolicLink()) {\n                  const resolution = await resolveDependency(\n                    rootPath,\n                    dependencyName,\n                    shouldIncludeDependency\n                  );\n                  if (resolution) resolvedDependencies.push(resolution);\n                }\n              })\n            );\n          } else {\n            const resolution = await resolveDependency(\n              rootPath,\n              entry.name,\n              shouldIncludeDependency\n            );\n            if (resolution) resolvedDependencies.push(resolution);\n          }\n        }\n      })\n    );\n  }\n\n  for (let idx = 0; idx < resolvedDependencies.length; idx++) {\n    const resolution = resolvedDependencies[idx];\n    const prevEntry = searchResults[resolution.name];\n    if (prevEntry != null && resolution.path !== prevEntry.path) {\n      (prevEntry.duplicates ?? (prevEntry.duplicates = [])).push({\n        name: resolution.name,\n        version: resolution.version,\n        path: resolution.path,\n        originPath: resolution.originPath,\n      });\n    } else if (prevEntry == null) {\n      searchResults[resolution.name] = resolution;\n    }\n  }\n\n  return searchResults;\n}\n"]}