{"version": 3, "file": "CachedDependenciesLinker.js", "sourceRoot": "", "sources": ["../../src/dependencies/CachedDependenciesLinker.ts"], "names": [], "mappings": ";;;;;AA0BA,oEAuDC;AAED,oFAiDC;AAED,oFAoBC;AA1JD,4CAAoB;AAGpB,6CAA2D;AAC3D,6CAAmE;AACnE,yCAA0D;AAE1D,mCAA4E;AAC5E,4DAA+D;AAC/D,uEAAoG;AACpG,4DAAkG;AAClG,wDAA8D;AAe9D,SAAgB,4BAA4B,CAAC,MAE5C;IACC,MAAM,wBAAwB,GAAG,IAAA,mDAA8B,EAAC;QAC9D,WAAW,EAAE,MAAM,CAAC,WAAW;KAChC,CAAC,CAAC;IAEH,IAAI,OAAoC,CAAC;IACzC,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC,OAAO,IAAI,CAAC,OAAO,GAAG,wBAAwB,CAAC,UAAU,EAAE,CAAC,CAAC;IAEtF,MAAM,8BAA8B,GAAG,IAAI,GAAG,EAAqC,CAAC;IACpF,IAAI,wBAAsF,CAAC;IAC3F,IAAI,oCAA2E,CAAC;IAChF,IAAI,qBAA4D,CAAC;IAEjE,OAAO;QACL,KAAK,CAAC,qBAAqB,CAAC,QAAQ;YAClC,MAAM,OAAO,GAAG,MAAM,wBAAwB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAC5E,OAAO,mCAAmC,CAAC,OAAO,CAAC,CAAC;QACtD,CAAC;QACD,KAAK,CAAC,4BAA4B;YAChC,IAAI,wBAAwB,KAAK,SAAS,EAAE,CAAC;gBAC3C,wBAAwB,GAAG,IAAA,wBAAe,EACxC,MAAM,UAAU,EAAE,CAC0B,CAAC;YACjD,CAAC;YACD,OAAO,wBAAwB,CAAC;QAClC,CAAC;QACD,KAAK,CAAC,mCAAmC;YACvC,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC3E,OAAO,CACL,oCAAoC;gBACpC,CAAC,oCAAoC,GAAG,IAAA,gDAAmC,EACzE,MAAM,UAAU,EAAE,EAClB,wBAAwB,CACzB,CAAC,CACH,CAAC;QACJ,CAAC;QACD,KAAK,CAAC,2BAA2B;YAC/B,OAAO,CACL,qBAAqB;gBACrB,CAAC,qBAAqB,GAAG,IAAA,wCAA2B,EAAC,MAAM,UAAU,EAAE,CAAC,CAAC,CAC1E,CAAC;QACJ,CAAC;QACD,KAAK,CAAC,4BAA4B,CAAC,UAAkB;YACnD,IAAI,MAAM,GAAG,8BAA8B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,8BAA8B,CAAC,GAAG,CAChC,UAAU,EACV,CAAC,MAAM,GAAG,IAAA,uCAA4B,EAAC,UAAU,CAAC,CAAC,CACpD,CAAC;YACJ,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;KACF,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,oCAAoC,CACxD,MAAgC,EAChC,QAA2B,EAC3B,OAAkB;IAElB,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,MAAM,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IACnF,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;IACtC,MAAM,wBAAwB,GAAG,MAAM,MAAM,CAAC,4BAA4B,EAAE,CAAC;IAE7E,MAAM,WAAW,GAAG,IAAA,8BAAsB,EACxC,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,MAAM,CAAC,mCAAmC,EAAE;QAC5C,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YAChC,OAAO,MAAM,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QACzD,CAAC,CAAC;QACF,MAAM,CAAC,2BAA2B,EAAE;KACrC,CAAC,CACH,CAAC;IAEF,MAAM,YAAY,GAAG,MAAM,IAAA,iCAAyB,EAAC,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACrF,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,OAAO,UAAU,CAAC;QACpB,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,oDAA4C,EAAE,CAAC;YACzE,mFAAmF;YACnF,iDAAiD;YACjD,MAAM,qBAAqB,GAAG,MAAM,IAAA,4CAAwB,EAC1D,UAAU,EACV,wBAAwB,EACxB,QAAQ,EACR,YAAY,CACb,CAAC;YACF,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACxD,IAAA,4CAAwB,EAAC,UAAU,EAAE,wBAAwB,EAAE,QAAQ,EAAE,YAAY,CAAC;gBACtF,IAAA,+BAAiB,EAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC;aACtD,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC,CAAC;IAEH,OAAO,YAAY,CAAC;AACtB,CAAC;AAEM,KAAK,UAAU,oCAAoC,CACxD,MAAgC,EAChC,QAA2B;IAE3B,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,MAAM,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IACnF,MAAM,WAAW,GAAG,IAAA,8BAAsB,EACxC,MAAM,OAAO,CAAC,GAAG,CACf;QACE,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YAChC,OAAO,MAAM,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QACzD,CAAC,CAAC;QACF,MAAM,CAAC,2BAA2B,EAAE;KACrC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAC3B,CACF,CAAC;IACF,OAAO,MAAM,IAAA,iCAAyB,EAAC,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;QACvE,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;YACvC,CAAC,CAAC,MAAM,IAAA,+BAAiB,EAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC;YAC7D,CAAC,CAAC,IAAI,CAAC;IACX,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,mCAAmC,GAAG,CAAC,OAA2B,EAAE,EAAE,CAAC,CAAC;IAC5E,YAAY,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC;IACtC,WAAW,EACT,OAAO,CAAC,gBAAgB,IAAI,YAAE,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC;QACjE,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC;CAClC,CAAC,CAAC", "sourcesContent": ["import fs from 'fs';\n\nimport { PackageRevision, SupportedPlatform } from '../types';\nimport { scanDependenciesRecursively } from './resolution';\nimport { scanDependenciesFromRNProjectConfig } from './rncliLocal';\nimport { scanDependenciesInSearchPath } from './scanning';\nimport { type ResolutionResult, DependencyResolutionSource } from './types';\nimport { filterMapResolutionResult, mergeResolutionResults } from './utils';\nimport { resolveExpoModule } from '../autolinking/findModules';\nimport { AutolinkingOptions, createAutolinkingOptionsLoader } from '../commands/autolinkingOptions';\nimport { resolveReactNativeModule, RNConfigReactNativeProjectConfig } from '../reactNativeConfig';\nimport { loadConfigAsync } from '../reactNativeConfig/config';\n\nexport interface CachedDependenciesSearchOptions {\n  excludeNames: Set<string>;\n  searchPaths: string[];\n}\n\nexport interface CachedDependenciesLinker {\n  getOptionsForPlatform(platform: SupportedPlatform): Promise<CachedDependenciesSearchOptions>;\n  loadReactNativeProjectConfig(): Promise<RNConfigReactNativeProjectConfig | null>;\n  scanDependenciesFromRNProjectConfig(): Promise<ResolutionResult>;\n  scanDependenciesRecursively(): Promise<ResolutionResult>;\n  scanDependenciesInSearchPath(searchPath: string): Promise<ResolutionResult>;\n}\n\nexport function makeCachedDependenciesLinker(params: {\n  projectRoot: string;\n}): CachedDependenciesLinker {\n  const autolinkingOptionsLoader = createAutolinkingOptionsLoader({\n    projectRoot: params.projectRoot,\n  });\n\n  let appRoot: Promise<string> | undefined;\n  const getAppRoot = () => appRoot || (appRoot = autolinkingOptionsLoader.getAppRoot());\n\n  const dependenciesResultBySearchPath = new Map<string, Promise<ResolutionResult>>();\n  let reactNativeProjectConfig: Promise<RNConfigReactNativeProjectConfig | null> | undefined;\n  let reactNativeProjectConfigDependencies: Promise<ResolutionResult> | undefined;\n  let recursiveDependencies: Promise<ResolutionResult> | undefined;\n\n  return {\n    async getOptionsForPlatform(platform) {\n      const options = await autolinkingOptionsLoader.getPlatformOptions(platform);\n      return makeCachedDependenciesSearchOptions(options);\n    },\n    async loadReactNativeProjectConfig() {\n      if (reactNativeProjectConfig === undefined) {\n        reactNativeProjectConfig = loadConfigAsync(\n          await getAppRoot()\n        ) as Promise<RNConfigReactNativeProjectConfig>;\n      }\n      return reactNativeProjectConfig;\n    },\n    async scanDependenciesFromRNProjectConfig() {\n      const reactNativeProjectConfig = await this.loadReactNativeProjectConfig();\n      return (\n        reactNativeProjectConfigDependencies ||\n        (reactNativeProjectConfigDependencies = scanDependenciesFromRNProjectConfig(\n          await getAppRoot(),\n          reactNativeProjectConfig\n        ))\n      );\n    },\n    async scanDependenciesRecursively() {\n      return (\n        recursiveDependencies ||\n        (recursiveDependencies = scanDependenciesRecursively(await getAppRoot()))\n      );\n    },\n    async scanDependenciesInSearchPath(searchPath: string) {\n      let result = dependenciesResultBySearchPath.get(searchPath);\n      if (!result) {\n        dependenciesResultBySearchPath.set(\n          searchPath,\n          (result = scanDependenciesInSearchPath(searchPath))\n        );\n      }\n      return result;\n    },\n  };\n}\n\nexport async function scanDependencyResolutionsForPlatform(\n  linker: CachedDependenciesLinker,\n  platform: SupportedPlatform,\n  include?: string[]\n): Promise<ResolutionResult> {\n  const { excludeNames, searchPaths } = await linker.getOptionsForPlatform(platform);\n  const includeNames = new Set(include);\n  const reactNativeProjectConfig = await linker.loadReactNativeProjectConfig();\n\n  const resolutions = mergeResolutionResults(\n    await Promise.all([\n      linker.scanDependenciesFromRNProjectConfig(),\n      ...searchPaths.map((searchPath) => {\n        return linker.scanDependenciesInSearchPath(searchPath);\n      }),\n      linker.scanDependenciesRecursively(),\n    ])\n  );\n\n  const dependencies = await filterMapResolutionResult(resolutions, async (resolution) => {\n    if (excludeNames.has(resolution.name)) {\n      return null;\n    } else if (includeNames.has(resolution.name)) {\n      return resolution;\n    } else if (resolution.source === DependencyResolutionSource.RN_CLI_LOCAL) {\n      // If the dependency was resolved frpom the React Native project config, we'll only\n      // attempt to resolve it as a React Native module\n      const reactNativeModuleDesc = await resolveReactNativeModule(\n        resolution,\n        reactNativeProjectConfig,\n        platform,\n        excludeNames\n      );\n      if (!reactNativeModuleDesc) {\n        return null;\n      }\n    } else {\n      const [reactNativeModule, expoModule] = await Promise.all([\n        resolveReactNativeModule(resolution, reactNativeProjectConfig, platform, excludeNames),\n        resolveExpoModule(resolution, platform, excludeNames),\n      ]);\n      if (!reactNativeModule && !expoModule) {\n        return null;\n      }\n    }\n    return resolution;\n  });\n\n  return dependencies;\n}\n\nexport async function scanExpoModuleResolutionsForPlatform(\n  linker: CachedDependenciesLinker,\n  platform: SupportedPlatform\n): Promise<Record<string, PackageRevision>> {\n  const { excludeNames, searchPaths } = await linker.getOptionsForPlatform(platform);\n  const resolutions = mergeResolutionResults(\n    await Promise.all(\n      [\n        ...searchPaths.map((searchPath) => {\n          return linker.scanDependenciesInSearchPath(searchPath);\n        }),\n        linker.scanDependenciesRecursively(),\n      ].filter((x) => x != null)\n    )\n  );\n  return await filterMapResolutionResult(resolutions, async (resolution) => {\n    return !excludeNames.has(resolution.name)\n      ? await resolveExpoModule(resolution, platform, excludeNames)\n      : null;\n  });\n}\n\nconst makeCachedDependenciesSearchOptions = (options: AutolinkingOptions) => ({\n  excludeNames: new Set(options.exclude),\n  searchPaths:\n    options.nativeModulesDir && fs.existsSync(options.nativeModulesDir)\n      ? [options.nativeModulesDir, ...(options.searchPaths ?? [])]\n      : (options.searchPaths ?? []),\n});\n"]}