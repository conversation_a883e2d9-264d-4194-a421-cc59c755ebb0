{"version": 3, "file": "generatePackageListCommand.js", "sourceRoot": "", "sources": ["../../src/commands/generatePackageListCommand.ts"], "names": [], "mappings": ";;AAsBA,gEA4CC;AAhED,6DAI8B;AAC9B,4DAA8D;AAC9D,4EAA8E;AAC9E,kEAAoE;AASpE;;;GAGG;AACH,SAAgB,0BAA0B,CAAC,GAA8B;IACvE,OAAO,IAAA,iDAA4B,EAAC,GAAG,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;SACvF,MAAM,CACL,qBAAqB,EACrB,uEAAuE,CACxE;SACA,MAAM,CACL,6BAA6B,EAC7B,kEAAkE,CACnE;SACA,MAAM,CACL,SAAS,EACT,8FAA8F,EAC9F,KAAK,CACN;SACA,MAAM,CACL,KAAK,EAAE,WAA4B,EAAE,gBAA8C,EAAE,EAAE;QACrF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,IAAI,SAAS,CAAC;QACxD,MAAM,wBAAwB,GAAG,IAAA,mDAA8B,EAAC;YAC9D,GAAG,gBAAgB;YACnB,WAAW;SACZ,CAAC,CAAC;QAEH,IAAI,yBAAyB,GAAuB,EAAE,CAAC;QACvD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAC5B,MAAM,kBAAkB,GAAG,MAAM,wBAAwB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAEvF,MAAM,wBAAwB,GAAG,MAAM,IAAA,8BAAgB,EAAC;gBACtD,kBAAkB,EAAE,MAAM,wBAAwB,CAAC,kBAAkB,CAAC,QAAQ,CAAC;gBAC/E,OAAO,EAAE,MAAM,wBAAwB,CAAC,UAAU,EAAE;aACrD,CAAC,CAAC;YACH,yBAAyB,GAAG,MAAM,IAAA,oCAAmB,EACnD,wBAAwB,EACxB,kBAAkB,CACnB,CAAC;QACJ,CAAC;QAED,MAAM,IAAA,8CAAwB,EAAC,yBAAyB,EAAE;YACxD,QAAQ;YACR,UAAU,EAAE,gBAAgB,CAAC,MAAM;YACnC,SAAS,EAAE,gBAAgB,CAAC,SAAS;SACtC,CAAC,CAAC;IACL,CAAC,CACF,CAAC;AACN,CAAC", "sourcesContent": ["import commander from 'commander';\n\nimport {\n  AutolinkingCommonArguments,\n  createAutolinkingOptionsLoader,\n  registerAutolinkingArguments,\n} from './autolinkingOptions';\nimport { findModulesAsync } from '../autolinking/findModules';\nimport { generatePackageListAsync } from '../autolinking/generatePackageList';\nimport { resolveModulesAsync } from '../autolinking/resolveModules';\nimport type { ModuleDescriptor } from '../types';\n\ninterface GeneratePackageListArguments extends AutolinkingCommonArguments {\n  target: string;\n  namespace: string;\n  empty: boolean;\n}\n\n/** Generates a source file listing all packages to link.\n * @privateRemarks\n * This command is deprecated for apple platforms, use `generate-modules-provider` instead.\n */\nexport function generatePackageListCommand(cli: commander.CommanderStatic) {\n  return registerAutolinkingArguments(cli.command('generate-package-list [searchPaths...]'))\n    .option(\n      '-t, --target <path>',\n      'Path to the target file, where the package list should be written to.'\n    )\n    .option(\n      '-n, --namespace <namespace>',\n      'Java package name under which the package list should be placed.'\n    )\n    .option(\n      '--empty',\n      'Whether to only generate an empty list. Might be used when the user opts-out of autolinking.',\n      false\n    )\n    .action(\n      async (searchPaths: string[] | null, commandArguments: GeneratePackageListArguments) => {\n        const platform = commandArguments.platform ?? 'android';\n        const autolinkingOptionsLoader = createAutolinkingOptionsLoader({\n          ...commandArguments,\n          searchPaths,\n        });\n\n        let expoModulesResolveResults: ModuleDescriptor[] = [];\n        if (!commandArguments.empty) {\n          const autolinkingOptions = await autolinkingOptionsLoader.getPlatformOptions(platform);\n\n          const expoModulesSearchResults = await findModulesAsync({\n            autolinkingOptions: await autolinkingOptionsLoader.getPlatformOptions(platform),\n            appRoot: await autolinkingOptionsLoader.getAppRoot(),\n          });\n          expoModulesResolveResults = await resolveModulesAsync(\n            expoModulesSearchResults,\n            autolinkingOptions\n          );\n        }\n\n        await generatePackageListAsync(expoModulesResolveResults, {\n          platform,\n          targetPath: commandArguments.target,\n          namespace: commandArguments.namespace,\n        });\n      }\n    );\n}\n"]}