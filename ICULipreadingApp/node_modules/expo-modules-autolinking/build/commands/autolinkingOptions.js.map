{"version": 3, "file": "autolinkingOptions.js", "sourceRoot": "", "sources": ["../../src/commands/autolinkingOptions.ts"], "names": [], "mappings": ";;;;;;AAwIA,oEAmBC;AAsDD,wEAyCC;AAzPD,4CAAoB;AACpB,gDAAwB;AAmCxB,MAAM,YAAY,GAAG,CAAC,CAAU,EAAgC,EAAE,CAChE,CAAC,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC;AAErC,MAAM,gBAAgB,GAAG,CAAC,MAAe,EAAE,QAAgB,EAAiB,EAAE;IAC5E,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC9C,IAAI,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5B,OAAO,QAAQ,CAAC;IAClB,CAAC;SAAM,IAAI,CAAC,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,YAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QACtE,uFAAuF;QACvF,4FAA4F;QAC5F,oCAAoC;QACpC,OAAO,MAAM,CAAC;IAChB,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAEK,MAAM,oBAAoB,GAAG,CAClC,WAAoB,EACpB,QAAgB,EACM,EAAE;IACxB,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAC/B,OAAO,WAAW;aACf,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aAC3D,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC;IAChD,CAAC;SAAM,CAAC;QACN,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC,CAAC;AAXW,QAAA,oBAAoB,wBAW/B;AAEF,MAAM,uBAAuB,GAAG,CAC9B,WAAoC,EACpC,OAAe,EACf,QAA8C,EACjB,EAAE;IAC/B,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IACtE,MAAM,kBAAkB,GAAG,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5F,IAAI,eAAe,GAAmC,IAAI,CAAC;IAC3D,IAAI,QAAQ,EAAE,CAAC;QACb,eAAe;YACb,kBAAkB,IAAI,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBAC9D,CAAC,CAAC,kBAAkB,CAAC,QAAQ,CAAC;gBAC9B,CAAC,CAAC,IAAI,CAAC;QACX,IAAI,CAAC,eAAe,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC7C,sGAAsG;YACtG,2FAA2F;YAC3F,eAAe;gBACb,kBAAkB,IAAI,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/F,CAAC;IACH,CAAC;IACD,MAAM,aAAa,GAAG,EAAE,GAAG,kBAAkB,EAAE,GAAG,eAAe,EAAE,CAAC;IACpE,MAAM,aAAa,GAAgC,EAAE,CAAC;IACtD,mCAAmC;IACnC,IAAI,aAAa,CAAC,gCAAgC,IAAI,IAAI,EAAE,CAAC;QAC3D,aAAa,CAAC,gCAAgC;YAC5C,CAAC,CAAC,aAAa,CAAC,gCAAgC,CAAC;IACrD,CAAC;IACD,cAAc;IACd,IAAI,OAAO,aAAa,CAAC,WAAW,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC;QAC9F,MAAM,cAAc,GAClB,OAAO,aAAa,CAAC,WAAW,KAAK,QAAQ;YAC3C,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC;YAC7B,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC;QAChC,aAAa,CAAC,WAAW,GAAG,IAAA,4BAAoB,EAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IAC5E,CAAC;IACD,mBAAmB;IACnB,IAAI,OAAO,aAAa,CAAC,gBAAgB,KAAK,QAAQ,EAAE,CAAC;QACvD,aAAa,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,aAAa,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAC7F,CAAC;IACD,UAAU;IACV,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;QACzC,aAAa,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC;IACrF,CAAC;IACD,kBAAkB;IAClB,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC;QACjD,aAAa,CAAC,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,CAClE,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAC7B,CAAC;IACJ,CAAC;IACD,QAAQ;IACR,IAAI,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QACtC,aAAa,CAAC,KAAK,GAAG,EAAE,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;IACnD,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC;AAYF,SAAgB,4BAA4B,CAAC,OAA0B;IACrE,OAAO,OAAO;SACX,MAAM,CACL,4BAA4B,EAC5B,uDAAuD,EACvD,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACpD;SACA,MAAM,CACL,2BAA2B,EAC3B,6FAA6F,EAC7F,OAAO,CACR;SACA,MAAM;IACL,0FAA0F;IAC1F,kGAAkG;IAClG,8BAA8B,EAC9B,4EAA4E,EAC5E,OAAO,CAAC,GAAG,EAAE,CACd,CAAC;AACN,CAAC;AAaD,MAAM,0BAA0B,GAAG,CACjC,IAAgC,EACH,EAAE;IAC/B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC;IAC5C,MAAM,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC;IACnE,MAAM,gBAAgB,GAAG,IAAA,4BAAoB,EAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAC7E,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC;IAC9E,OAAO;QACL,QAAQ;QACR,WAAW;QACX,gBAAgB;QAChB,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,wBAAwB,GAAG,KAAK,EAAE,WAA0B,EAAmB,EAAE;IACrF,MAAM,IAAI,GAAG,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAC1C,KAAK,IAAI,GAAG,GAAG,IAAI,EAAE,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACxE,MAAM,IAAI,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAC/C,IAAI,YAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,8CAA8C,IAAI,GAAG,CAAC,CAAC;AACzE,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,KAAK,EAAE,eAAuB,EAAoC,EAAE;IAC/F,MAAM,eAAe,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAC5E,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;AACrC,CAAC,CAAC;AAWF,SAAgB,8BAA8B,CAC5C,gBAA6C;IAE7C,MAAM,qBAAqB,GAAG,0BAA0B,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;IACjF,MAAM,EAAE,WAAW,EAAE,GAAG,qBAAqB,CAAC;IAE9C,IAAI,iBAA8C,CAAC;IACnD,MAAM,kBAAkB,GAAG,GAAG,EAAE;QAC9B,OAAO,iBAAiB,IAAI,CAAC,iBAAiB,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC,CAAC;IAC1F,CAAC,CAAC;IAEF,IAAI,aAA2D,CAAC;IAChE,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE,CAChC,aAAa,IAAI,CAAC,aAAa,GAAG,oBAAoB,CAAC,MAAM,kBAAkB,EAAE,CAAC,CAAC,CAAC;IAEtF,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE,CAAC,cAAI,CAAC,OAAO,CAAC,MAAM,kBAAkB,EAAE,CAAC,CAAC;IAExE,OAAO;QACL,cAAc,EAAE,GAAG,EAAE,CAAC,WAAW;QACjC,UAAU;QACV,KAAK,CAAC,kBAAkB,CAAC,QAAQ,GAAG,qBAAqB,CAAC,QAAQ;YAChE,MAAM,WAAW,GAAG,MAAM,cAAc,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,MAAM,UAAU,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,uBAAuB,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAExE,IAAI,qBAAqB,CAAC,gBAAgB,EAAE,CAAC;gBAC3C,OAAO,CAAC,WAAW,GAAG;oBACpB,GAAG,qBAAqB,CAAC,gBAAgB;oBACzC,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC;iBAC/B,CAAC;YACJ,CAAC;YACD,IAAI,qBAAqB,CAAC,YAAY,EAAE,CAAC;gBACvC,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,EAAE,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;YACxF,CAAC;YAED,OAAO;gBACL,GAAG,2BAA2B,CAAC,OAAO,EAAE,OAAO,CAAC;gBAChD,QAAQ;aACT,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,2BAA2B,GAAG,CAClC,OAAoC,EACpC,OAAe,EACK,EAAE;IACtB,OAAO;QACL,gCAAgC,EAAE,OAAO,CAAC,gCAAgC,IAAI,KAAK;QACnF,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;QACtC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YACxC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC;YAC/D,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC;QACpD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;QAC9B,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,KAAK,EAAE,OAAO,CAAC,KAAK;KACrB,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import commander from 'commander';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { SupportedPlatform } from '../types';\n\nexport interface AutolinkingOptions {\n  /** Only scan direct \"dependencies\" of a project for React Native modules, rather than including transitive dependencies.\n   * @remarks\n   * Before SDK 54, React Native modules would only be linked if they were listed as dependencies\n   * of a project. However, in SDK 54+ transitive React Native modules dependencies are also\n   * auto-linked, unless this flag is enabled.\n   * @defaultValue `false`\n   */\n  legacy_shallowReactNativeLinking: boolean;\n  /** Extra modules directories to search for native modules.\n   * @defaultValue `[]`\n   */\n  searchPaths: string[];\n  /** Local native modules directory to add to autolinking.\n   * @defaultValue `\"./modules\"`\n   */\n  nativeModulesDir: string | null;\n  /** Native modules to exclude from autolinking by name.\n   * @defaultValue `[]`\n   */\n  exclude: string[];\n  /** A list of package names to opt out of prebuilt Expo modules (Android-only)\n   * @defaultValue `[]`\n   */\n  buildFromSource?: string[];\n  /** CocoaPods flags to pass to each autolinked pod (Apple/iOS-only)\n   * @defaultValue `[]`\n   */\n  flags?: Record<string, any>;\n}\n\nconst isJSONObject = (x: unknown): x is Record<string, unknown> =>\n  x != null && typeof x === 'object';\n\nconst resolvePathMaybe = (target: unknown, basePath: string): string | null => {\n  if (typeof target !== 'string') {\n    return null;\n  }\n  let resolved = path.resolve(basePath, target);\n  if (fs.existsSync(resolved)) {\n    return resolved;\n  } else if ((resolved = path.resolve(target)) && fs.existsSync(target)) {\n    // TODO(@kitten): This is here for legacy support. However, this *will* be inconsistent\n    // This relies on the current working directory, and hence, can behave differently depending\n    // on where the command was invoked.\n    return target;\n  } else {\n    return null;\n  }\n};\n\nexport const filterMapSearchPaths = (\n  searchPaths: unknown,\n  basePath: string\n): string[] | undefined => {\n  if (Array.isArray(searchPaths)) {\n    return searchPaths\n      .map((searchPath) => resolvePathMaybe(searchPath, basePath))\n      .filter((searchPath) => searchPath != null);\n  } else {\n    return undefined;\n  }\n};\n\nconst parsePackageJsonOptions = (\n  packageJson: Record<string, unknown>,\n  appRoot: string,\n  platform: SupportedPlatform | null | undefined\n): Partial<AutolinkingOptions> => {\n  const expo = isJSONObject(packageJson.expo) ? packageJson.expo : null;\n  const autolinkingOptions = expo && isJSONObject(expo.autolinking) ? expo.autolinking : null;\n  let platformOptions: Record<string, unknown> | null = null;\n  if (platform) {\n    platformOptions =\n      autolinkingOptions && isJSONObject(autolinkingOptions[platform])\n        ? autolinkingOptions[platform]\n        : null;\n    if (!platformOptions && platform === 'apple') {\n      // NOTE: `platform: 'apple'` has a fallback on `ios`. This doesn't make much sense, since apple should\n      // be the base option for other apple platforms, but changing this now is a breaking change\n      platformOptions =\n        autolinkingOptions && isJSONObject(autolinkingOptions.ios) ? autolinkingOptions.ios : null;\n    }\n  }\n  const mergedOptions = { ...autolinkingOptions, ...platformOptions };\n  const outputOptions: Partial<AutolinkingOptions> = {};\n  // legacy_shallowReactNativeLinking\n  if (mergedOptions.legacy_shallowReactNativeLinking != null) {\n    outputOptions.legacy_shallowReactNativeLinking =\n      !!mergedOptions.legacy_shallowReactNativeLinking;\n  }\n  // searchPaths\n  if (typeof mergedOptions.searchPaths === 'string' || Array.isArray(mergedOptions.searchPaths)) {\n    const rawSearchPaths =\n      typeof mergedOptions.searchPaths === 'string'\n        ? [mergedOptions.searchPaths]\n        : mergedOptions.searchPaths;\n    outputOptions.searchPaths = filterMapSearchPaths(rawSearchPaths, appRoot);\n  }\n  // nativeModulesDir\n  if (typeof mergedOptions.nativeModulesDir === 'string') {\n    outputOptions.nativeModulesDir = resolvePathMaybe(mergedOptions.nativeModulesDir, appRoot);\n  }\n  // exclude\n  if (Array.isArray(mergedOptions.exclude)) {\n    outputOptions.exclude = mergedOptions.exclude.filter((x) => typeof x === 'string');\n  }\n  // buildFromSource\n  if (Array.isArray(mergedOptions.buildFromSource)) {\n    outputOptions.buildFromSource = mergedOptions.buildFromSource.filter(\n      (x) => typeof x === 'string'\n    );\n  }\n  // flags\n  if (isJSONObject(mergedOptions.flags)) {\n    outputOptions.flags = { ...mergedOptions.flags };\n  }\n  return outputOptions;\n};\n\n/** Common commandline arguments for autolinking commands (Not to be confused with `AutolinkingOptions` */\nexport interface AutolinkingCommonArguments {\n  projectRoot?: string | null;\n  // NOTE(@kitten): These are added to other `searchPaths` entries\n  searchPaths?: string[] | null;\n  // NOTE(@kitten): These are added to other `exclude` entries\n  exclude?: string[] | null;\n  platform?: SupportedPlatform | null;\n}\n\nexport function registerAutolinkingArguments(command: commander.Command): commander.Command {\n  return command\n    .option<string[] | null>(\n      '-e, --exclude <exclude...>',\n      'Package names to exclude when looking up for modules.',\n      (value, previous) => (previous ?? []).concat(value)\n    )\n    .option(\n      '-p, --platform [platform]',\n      'The platform that the resulting modules must support. Available options: \"apple\", \"android\"',\n      'apple'\n    )\n    .option(\n      // NOTE(@kitten): For backwards-compatibility, this is still called `project-root`, but it\n      // really is a replacement path for the current working directory. Henceforth called `commandRoot`\n      '--project-root <projectRoot>',\n      'The path to the root of the project. Defaults to current working directory',\n      process.cwd()\n    );\n}\n\ninterface ArgumentsAutolinkingOptions {\n  /** The root directory that will be considered the base path for all other target paths */\n  commandRoot: string;\n  /** The platform to autolink against. If not passed or unknown, no specific autolinking search logic will be applied */\n  platform?: SupportedPlatform;\n  /** Added search paths to search for native modules (Usually passed as CLI rest argument. */\n  extraSearchPaths?: string[];\n  /** Added native module names to exclude from autolined native modules (Usually passed as CLI argument) */\n  extraExclude?: string[];\n}\n\nconst parseExtraArgumentsOptions = (\n  args: AutolinkingCommonArguments\n): ArgumentsAutolinkingOptions => {\n  const cwd = process.cwd();\n  const platform = args.platform || undefined;\n  const commandRoot = resolvePathMaybe(args.projectRoot, cwd) || cwd;\n  const extraSearchPaths = filterMapSearchPaths(args.searchPaths, commandRoot);\n  const extraExclude = args.exclude?.filter((name) => typeof name === 'string');\n  return {\n    platform,\n    commandRoot,\n    extraSearchPaths,\n    extraExclude,\n  };\n};\n\nconst findPackageJsonPathAsync = async (commandRoot: string | null): Promise<string> => {\n  const root = commandRoot || process.cwd();\n  for (let dir = root; path.dirname(dir) !== dir; dir = path.dirname(dir)) {\n    const file = path.resolve(dir, 'package.json');\n    if (fs.existsSync(file)) {\n      return file;\n    }\n  }\n  throw new Error(`Couldn't find \"package.json\" up from path \"${root}\"`);\n};\n\nconst loadPackageJSONAsync = async (packageJsonPath: string): Promise<Record<string, unknown>> => {\n  const packageJsonText = await fs.promises.readFile(packageJsonPath, 'utf8');\n  return JSON.parse(packageJsonText);\n};\n\nexport interface LinkingOptionsLoader {\n  getCommandRoot(): string;\n  getAppRoot(): Promise<string>;\n  getPlatformOptions<T extends SupportedPlatform | undefined>(\n    platform: T\n  ): Promise<AutolinkingOptions & { platform: T }>;\n  getPlatformOptions(): Promise<AutolinkingOptions>;\n}\n\nexport function createAutolinkingOptionsLoader(\n  argumentsOptions?: AutolinkingCommonArguments\n): LinkingOptionsLoader {\n  const extraArgumentsOptions = parseExtraArgumentsOptions(argumentsOptions ?? {});\n  const { commandRoot } = extraArgumentsOptions;\n\n  let _packageJsonPath$: Promise<string> | undefined;\n  const getPackageJsonPath = () => {\n    return _packageJsonPath$ || (_packageJsonPath$ = findPackageJsonPathAsync(commandRoot));\n  };\n\n  let _packageJson$: Promise<Record<string, unknown>> | undefined;\n  const getPackageJson = async () =>\n    _packageJson$ || (_packageJson$ = loadPackageJSONAsync(await getPackageJsonPath()));\n\n  const getAppRoot = async () => path.dirname(await getPackageJsonPath());\n\n  return {\n    getCommandRoot: () => commandRoot,\n    getAppRoot,\n    async getPlatformOptions(platform = extraArgumentsOptions.platform) {\n      const packageJson = await getPackageJson();\n      const appRoot = await getAppRoot();\n      const options = parsePackageJsonOptions(packageJson, appRoot, platform);\n\n      if (extraArgumentsOptions.extraSearchPaths) {\n        options.searchPaths = [\n          ...extraArgumentsOptions.extraSearchPaths,\n          ...(options.searchPaths ?? []),\n        ];\n      }\n      if (extraArgumentsOptions.extraExclude) {\n        options.exclude = [...(options.exclude ?? []), ...extraArgumentsOptions.extraExclude];\n      }\n\n      return {\n        ...normalizeAutolinkingOptions(options, appRoot),\n        platform,\n      };\n    },\n  };\n}\n\nconst normalizeAutolinkingOptions = (\n  options: Partial<AutolinkingOptions>,\n  appRoot: string\n): AutolinkingOptions => {\n  return {\n    legacy_shallowReactNativeLinking: options.legacy_shallowReactNativeLinking ?? false,\n    searchPaths: options.searchPaths ?? [],\n    nativeModulesDir: options.nativeModulesDir\n      ? (resolvePathMaybe(options.nativeModulesDir, appRoot) ?? null)\n      : (resolvePathMaybe('./modules', appRoot) ?? null),\n    exclude: options.exclude ?? [],\n    buildFromSource: options.buildFromSource,\n    flags: options.flags,\n  };\n};\n"]}