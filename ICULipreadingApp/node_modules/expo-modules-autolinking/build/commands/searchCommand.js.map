{"version": 3, "file": "searchCommand.js", "sourceRoot": "", "sources": ["../../src/commands/searchCommand.ts"], "names": [], "mappings": ";;AAaA,sCAqBC;AAhCD,6DAI8B;AAC9B,4DAA8D;AAM9D,SAAgB,aAAa,CAAC,GAA8B;IAC1D,OAAO,IAAA,iDAA4B,EAAC,GAAG,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;SACxE,MAAM,CAAC,YAAY,EAAE,0CAA0C,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;SACnF,MAAM,CAAC,KAAK,EAAE,WAA4B,EAAE,gBAAiC,EAAE,EAAE;QAChF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,IAAI,OAAO,CAAC;QACtD,MAAM,wBAAwB,GAAG,IAAA,mDAA8B,EAAC;YAC9D,GAAG,gBAAgB;YACnB,WAAW;SACZ,CAAC,CAAC;QAEH,MAAM,wBAAwB,GAAG,MAAM,IAAA,8BAAgB,EAAC;YACtD,kBAAkB,EAAE,MAAM,wBAAwB,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC/E,OAAO,EAAE,MAAM,wBAAwB,CAAC,UAAU,EAAE;SACrD,CAAC,CAAC;QAEH,IAAI,gBAAgB,CAAC,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,wBAAwB,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QACpF,CAAC;IACH,CAAC,CAAC,CAAC;AACP,CAAC", "sourcesContent": ["import commander from 'commander';\n\nimport {\n  AutolinkingCommonArguments,\n  createAutolinkingOptionsLoader,\n  registerAutolinkingArguments,\n} from './autolinkingOptions';\nimport { findModulesAsync } from '../autolinking/findModules';\n\ninterface SearchArguments extends AutolinkingCommonArguments {\n  json?: boolean | null;\n}\n\nexport function searchCommand(cli: commander.CommanderStatic) {\n  return registerAutolinkingArguments(cli.command('search [searchPaths...]'))\n    .option('-j, --json', 'Output results in the plain JSON format.', () => true, false)\n    .action(async (searchPaths: string[] | null, commandArguments: SearchArguments) => {\n      const platform = commandArguments.platform ?? 'apple';\n      const autolinkingOptionsLoader = createAutolinkingOptionsLoader({\n        ...commandArguments,\n        searchPaths,\n      });\n\n      const expoModulesSearchResults = await findModulesAsync({\n        autolinkingOptions: await autolinkingOptionsLoader.getPlatformOptions(platform),\n        appRoot: await autolinkingOptionsLoader.getAppRoot(),\n      });\n\n      if (commandArguments.json) {\n        console.log(JSON.stringify(expoModulesSearchResults));\n      } else {\n        console.log(require('util').inspect(expoModulesSearchResults, false, null, true));\n      }\n    });\n}\n"]}