{"version": 3, "file": "generateModulesProviderCommand.js", "sourceRoot": "", "sources": ["../../src/commands/generateModulesProviderCommand.ts"], "names": [], "mappings": ";;AAmBA,wEA0CC;AA3DD,6DAI8B;AAC9B,4DAA8D;AAC9D,4EAAkF;AAClF,kEAAoE;AASpE,0EAA0E;AAC1E,SAAgB,8BAA8B,CAAC,GAA8B;IAC3E,OAAO,IAAA,iDAA4B,EAAC,GAAG,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;SAC3F,MAAM,CACL,qBAAqB,EACrB,uEAAuE,CACxE;SACA,MAAM,CAAC,sBAAsB,EAAE,mDAAmD,CAAC;SACnF,MAAM,CACL,8BAA8B,EAC9B,qEAAqE,CACtE;SACA,MAAM,CAAC,mBAAmB,EAAE,iCAAiC,CAAC;SAC9D,MAAM,CACL,KAAK,EAAE,WAA4B,EAAE,gBAAkD,EAAE,EAAE;QACzF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,IAAI,OAAO,CAAC;QACtD,MAAM,wBAAwB,GAAG,IAAA,mDAA8B,EAAC;YAC9D,GAAG,gBAAgB;YACnB,WAAW;SACZ,CAAC,CAAC;QACH,MAAM,kBAAkB,GAAG,MAAM,wBAAwB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAEvF,MAAM,wBAAwB,GAAG,MAAM,IAAA,8BAAgB,EAAC;YACtD,kBAAkB,EAAE,MAAM,wBAAwB,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC/E,OAAO,EAAE,gBAAgB,CAAC,OAAO,IAAI,CAAC,MAAM,wBAAwB,CAAC,UAAU,EAAE,CAAC;SACnF,CAAC,CAAC;QACH,MAAM,yBAAyB,GAAG,MAAM,IAAA,oCAAmB,EACzD,wBAAwB,EACxB,kBAAkB,CACnB,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;QAChE,MAAM,eAAe,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAClE,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CACvC,CAAC;QAEF,MAAM,IAAA,kDAA4B,EAAC,eAAe,EAAE;YAClD,QAAQ;YACR,UAAU,EAAE,gBAAgB,CAAC,MAAM;YACnC,eAAe,EAAE,gBAAgB,CAAC,WAAW,IAAI,IAAI;SACtD,CAAC,CAAC;IACL,CAAC,CACF,CAAC;AACN,CAAC", "sourcesContent": ["import commander from 'commander';\n\nimport {\n  AutolinkingCommonArguments,\n  createAutolinkingOptionsLoader,\n  registerAutolinkingArguments,\n} from './autolinkingOptions';\nimport { findModulesAsync } from '../autolinking/findModules';\nimport { generateModulesProviderAsync } from '../autolinking/generatePackageList';\nimport { resolveModulesAsync } from '../autolinking/resolveModules';\n\ninterface GenerateModulesProviderArguments extends AutolinkingCommonArguments {\n  target: string;\n  entitlement?: string;\n  packages?: string[] | null;\n  appRoot?: string;\n}\n\n/** Generates a source file listing all packages to link in the runtime */\nexport function generateModulesProviderCommand(cli: commander.CommanderStatic) {\n  return registerAutolinkingArguments(cli.command('generate-modules-provider [searchPaths...]'))\n    .option(\n      '-t, --target <path>',\n      'Path to the target file, where the package list should be written to.'\n    )\n    .option('--entitlement <path>', 'Path to the Apple code signing entitlements file.')\n    .option(\n      '-p, --packages <packages...>',\n      'Names of the packages to include in the generated modules provider.'\n    )\n    .option('--app-root <path>', 'Path to the app root directory.')\n    .action(\n      async (searchPaths: string[] | null, commandArguments: GenerateModulesProviderArguments) => {\n        const platform = commandArguments.platform ?? 'apple';\n        const autolinkingOptionsLoader = createAutolinkingOptionsLoader({\n          ...commandArguments,\n          searchPaths,\n        });\n        const autolinkingOptions = await autolinkingOptionsLoader.getPlatformOptions(platform);\n\n        const expoModulesSearchResults = await findModulesAsync({\n          autolinkingOptions: await autolinkingOptionsLoader.getPlatformOptions(platform),\n          appRoot: commandArguments.appRoot ?? (await autolinkingOptionsLoader.getAppRoot()),\n        });\n        const expoModulesResolveResults = await resolveModulesAsync(\n          expoModulesSearchResults,\n          autolinkingOptions\n        );\n\n        const includeModules = new Set(commandArguments.packages ?? []);\n        const filteredModules = expoModulesResolveResults.filter((module) =>\n          includeModules.has(module.packageName)\n        );\n\n        await generateModulesProviderAsync(filteredModules, {\n          platform,\n          targetPath: commandArguments.target,\n          entitlementPath: commandArguments.entitlement ?? null,\n        });\n      }\n    );\n}\n"]}