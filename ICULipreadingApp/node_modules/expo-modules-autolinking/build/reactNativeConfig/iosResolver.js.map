{"version": 3, "file": "iosResolver.js", "sourceRoot": "", "sources": ["../../src/reactNativeConfig/iosResolver.ts"], "names": [], "mappings": ";;;;;AASA,kFAyCC;AAlDD,+BAA4B;AAC5B,gDAAwB;AAQjB,KAAK,UAAU,mCAAmC,CACvD,UAA6C,EAC7C,iBAA2E,EAC3E,gBAA0C;IAE1C,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;QAC/B,qCAAqC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,IAAA,WAAI,EAAC,WAAW,EAAE,EAAE,GAAG,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IACnE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,kBAAkB,GAAG,cAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;IACvE,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QACvD,CAAC,CAAC,kBAAkB;QACpB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAE5D,IAAI,iBAAiB,KAAK,SAAS,IAAI,gBAAgB,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;QACnF,oEAAoE;QACpE,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;YACtF,MAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAC/D,OAAO,eAAe,KAAK,WAAW,CAAC;QACzC,CAAC,CAAC,CAAC;QACH,6EAA6E;QAC7E,2EAA2E;QAC3E,+CAA+C;QAC/C,IAAI,sBAAsB,IAAI,IAAI,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO;QACL,WAAW;QACX,OAAO,EAAE,UAAU,CAAC,OAAO;QAC3B,cAAc,EAAE,iBAAiB,EAAE,cAAc,IAAI,EAAE;QACvD,YAAY,EAAE,iBAAiB,EAAE,YAAY,IAAI,EAAE;KACpD,CAAC;AACJ,CAAC", "sourcesContent": ["import { glob } from 'glob';\nimport path from 'path';\n\nimport type {\n  RNConfigDependencyIos,\n  RNConfigReactNativePlatformsConfigIos,\n} from './reactNativeConfig.types';\nimport type { ExpoModuleConfig } from '../ExpoModuleConfig';\n\nexport async function resolveDependencyConfigImplIosAsync(\n  resolution: { path: string; version: string },\n  reactNativeConfig: RNConfigReactNativePlatformsConfigIos | null | undefined,\n  expoModuleConfig?: ExpoModuleConfig | null\n): Promise<RNConfigDependencyIos | null> {\n  if (reactNativeConfig === null) {\n    // Skip autolinking for this package.\n    return null;\n  }\n\n  const podspecs = await glob('*.podspec', { cwd: resolution.path });\n  if (!podspecs?.length) {\n    return null;\n  }\n\n  const mainPackagePodspec = path.basename(resolution.path) + '.podspec';\n  const podspecFile = podspecs.includes(mainPackagePodspec)\n    ? mainPackagePodspec\n    : podspecs.sort((a, b) => a.localeCompare(b))[0];\n  const podspecPath = path.join(resolution.path, podspecFile);\n\n  if (reactNativeConfig === undefined && expoModuleConfig?.supportsPlatform('apple')) {\n    // Check if Expo podspec files contain the React Native podspec file\n    const overlappingPodspecPath = expoModuleConfig.applePodspecPaths().find((targetFile) => {\n      const expoPodspecPath = path.join(resolution.path, targetFile);\n      return expoPodspecPath === podspecPath;\n    });\n    // NOTE(@kitten): If we don't have a react-native.config.{js,ts} file and the\n    // package is also an Expo module, we only link it as a React Native module\n    // if both don't point at the same podspec file\n    if (overlappingPodspecPath != null) {\n      return null;\n    }\n  }\n\n  return {\n    podspecPath,\n    version: resolution.version,\n    configurations: reactNativeConfig?.configurations || [],\n    scriptPhases: reactNativeConfig?.scriptPhases || [],\n  };\n}\n"]}