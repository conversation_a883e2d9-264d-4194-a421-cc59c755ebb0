{"version": 3, "file": "exports.js", "sourceRoot": "", "sources": ["../src/exports.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AA8BA,4DASC;AAQD,0FAaC;AAGD,kDAQC;AAGD,0DAKC;AA/ED,4CAAoB;AACpB,gDAAwB;AAExB,2DAA6D;AAC7D,iEAAmE;AACnE,sEAKuC;AAGvC,0CAAwB;AACxB,gDAA8B;AAC9B,8CAA4B;AAE5B,+CAUwB;AAHtB,4HAAA,4BAA4B,OAAA;AAC5B,oIAAA,oCAAoC,OAAA;AACpC,oIAAA,oCAAoC,OAAA;AAGtC,kBAAkB;AACX,KAAK,UAAU,wBAAwB,CAC5C,gBAAyB;IAEzB,MAAM,wBAAwB,GAAG,IAAA,mDAA8B,EAAC,gBAAgB,CAAC,CAAC;IAClF,OAAO;QACL,GAAG,gBAAgB;QACnB,GAAG,CAAC,MAAM,wBAAwB,CAAC,kBAAkB,EAAE,CAAC;QACxD,WAAW,EAAE,wBAAwB,CAAC,UAAU,EAAE;KACnD,CAAC;AACJ,CAAC;AAOD,kBAAkB;AACX,KAAK,UAAU,uCAAuC,CAC3D,WAAmB,EACnB,OAAiD;IAEjD,MAAM,wBAAwB,GAAG,IAAA,mDAA8B,EAAC;QAC9D,GAAG,OAAO;QACV,iDAAiD;QACjD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,WAAW;KAChD,CAAC,CAAC;IACH,MAAM,OAAO,GAAG,MAAM,wBAAwB,CAAC,UAAU,EAAE,CAAC;IAC5D,MAAM,kBAAkB,GAAG,MAAM,wBAAwB,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC/F,MAAM,aAAa,GAAG,MAAM,IAAA,8BAAgB,EAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAC9E,OAAO,MAAM,IAAA,oCAAmB,EAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;AACtE,CAAC;AAED,kBAAkB;AAClB,SAAgB,mBAAmB,CAAC,MAAc,OAAO,CAAC,GAAG,EAAE;IAC7D,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvE,MAAM,IAAI,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAC/C,IAAI,YAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,8CAA8C,GAAG,GAAG,CAAC,CAAC;AACxE,CAAC;AAED,kBAAkB;AACX,KAAK,UAAU,uBAAuB,CAC3C,WAA4B,EAC5B,GAAW;IAEX,OAAO,IAAA,yCAAoB,EAAC,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AACtD,CAAC", "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { findModulesAsync } from './autolinking/findModules';\nimport { resolveModulesAsync } from './autolinking/resolveModules';\nimport {\n  AutolinkingCommonArguments,\n  AutolinkingOptions,\n  createAutolinkingOptionsLoader,\n  filterMapSearchPaths,\n} from './commands/autolinkingOptions';\nimport { ModuleDescriptor, SupportedPlatform } from './types';\n\nexport * from './types';\nexport * from './autolinking';\nexport * from './platforms';\n\nexport {\n  ResolutionResult,\n  BaseDependencyResolution,\n  DependencyResolution,\n  DependencyResolutionSource,\n  CachedDependenciesLinker,\n  CachedDependenciesSearchOptions,\n  makeCachedDependenciesLinker,\n  scanDependencyResolutionsForPlatform,\n  scanExpoModuleResolutionsForPlatform,\n} from './dependencies';\n\n/** @deprecated */\nexport async function mergeLinkingOptionsAsync<Options extends Partial<AutolinkingCommonArguments>>(\n  argumentsOptions: Options\n): Promise<Options & AutolinkingOptions> {\n  const autolinkingOptionsLoader = createAutolinkingOptionsLoader(argumentsOptions);\n  return {\n    ...argumentsOptions,\n    ...(await autolinkingOptionsLoader.getPlatformOptions()),\n    projectRoot: autolinkingOptionsLoader.getAppRoot(),\n  };\n}\n\ninterface QueryAutolinkingModulesFromProjectParams extends Partial<AutolinkingCommonArguments> {\n  platform: SupportedPlatform;\n  [extra: string]: unknown;\n}\n\n/** @deprecated */\nexport async function queryAutolinkingModulesFromProjectAsync(\n  projectRoot: string,\n  options: QueryAutolinkingModulesFromProjectParams\n): Promise<ModuleDescriptor[]> {\n  const autolinkingOptionsLoader = createAutolinkingOptionsLoader({\n    ...options,\n    // NOTE(@kitten): This has always been duplicated\n    projectRoot: options.projectRoot ?? projectRoot,\n  });\n  const appRoot = await autolinkingOptionsLoader.getAppRoot();\n  const autolinkingOptions = await autolinkingOptionsLoader.getPlatformOptions(options.platform);\n  const searchResults = await findModulesAsync({ appRoot, autolinkingOptions });\n  return await resolveModulesAsync(searchResults, autolinkingOptions);\n}\n\n/** @deprecated */\nexport function findProjectRootSync(cwd: string = process.cwd()): string {\n  for (let dir = cwd; path.dirname(dir) !== dir; dir = path.dirname(dir)) {\n    const file = path.resolve(dir, 'package.json');\n    if (fs.existsSync(file)) {\n      return file;\n    }\n  }\n  throw new Error(`Couldn't find \"package.json\" up from path \"${cwd}\"`);\n}\n\n/** @deprecated */\nexport async function resolveSearchPathsAsync(\n  searchPaths: string[] | null,\n  cwd: string\n): Promise<string[]> {\n  return filterMapSearchPaths(searchPaths, cwd) ?? [];\n}\n"]}