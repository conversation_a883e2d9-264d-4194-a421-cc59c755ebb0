{"version": 3, "file": "getConfiguration.js", "sourceRoot": "", "sources": ["../../src/autolinking/getConfiguration.ts"], "names": [], "mappings": ";;AAQA,4CASC;AAhBD,4CAAmE;AAOnE,SAAgB,gBAAgB,CAAC,EAC/B,kBAAkB,GACK;IACvB,MAAM,eAAe,GAAG,IAAA,+CAAmC,EAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IACzF,IAAI,kBAAkB,IAAI,eAAe,EAAE,CAAC;QAC1C,OAAO,eAAe,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IAC9D,CAAC;SAAM,CAAC;QACN,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC", "sourcesContent": ["import { AutolinkingOptions } from '../commands/autolinkingOptions';\nimport { getLinkingImplementationForPlatform } from '../platforms';\nimport type { SupportedPlatform } from '../types';\n\ninterface GetConfigurationParams {\n  autolinkingOptions: AutolinkingOptions & { platform: SupportedPlatform };\n}\n\nexport function getConfiguration({\n  autolinkingOptions,\n}: GetConfigurationParams): Record<string, any> | undefined {\n  const platformLinking = getLinkingImplementationForPlatform(autolinkingOptions.platform);\n  if ('getConfiguration' in platformLinking) {\n    return platformLinking.getConfiguration(autolinkingOptions);\n  } else {\n    return undefined;\n  }\n}\n"]}