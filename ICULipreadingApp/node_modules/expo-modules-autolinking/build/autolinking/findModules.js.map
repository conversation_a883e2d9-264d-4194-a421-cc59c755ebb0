{"version": 3, "file": "findModules.js", "sourceRoot": "", "sources": ["../../src/autolinking/findModules.ts"], "names": [], "mappings": ";;AAWA,8CAyBC;AAQD,4CAoBC;AAhED,0DAAoE;AAEpE,kDAMyB;AAGlB,KAAK,UAAU,iBAAiB,CACrC,UAAgC,EAChC,QAA2B,EAC3B,YAAyB;IAEzB,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,gBAAgB,GAAG,MAAM,IAAA,gDAA6B,EAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9E,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;QACpE,OAAO;YACL,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,OAAO,EAAE,UAAU,CAAC,OAAO;YAC3B,MAAM,EAAE,gBAAgB;YACxB,UAAU,EACR,UAAU,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBACzC,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B,CAAC,CAAC,IAAI,EAAE;SACZ,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAOD,0DAA0D;AACnD,KAAK,UAAU,gBAAgB,CAAC,EACrC,OAAO,EACP,kBAAkB,GACA;IAClB,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAEzD,yFAAyF;IACzF,MAAM,WAAW,GAAG,kBAAkB,CAAC,gBAAgB;QACrD,CAAC,CAAC,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAC1E,CAAC,CAAC,kBAAkB,CAAC,WAAW,CAAC;IAEnC,OAAO,IAAA,wCAAyB,EAC9B,IAAA,qCAAsB,EACpB,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAA,2CAA4B,EAAC,UAAU,CAAC,CAAC;QAC5E,IAAA,0CAA2B,EAAC,OAAO,CAAC;KACrC,CAAC,CACH,EACD,CAAC,UAAU,EAAE,EAAE,CAAC,iBAAiB,CAAC,UAAU,EAAE,kBAAkB,CAAC,QAAQ,EAAE,YAAY,CAAC,CACzF,CAAC;AACJ,CAAC", "sourcesContent": ["import { discoverExpoModuleConfigAsync } from '../ExpoModuleConfig';\nimport { AutolinkingOptions } from '../commands/autolinkingOptions';\nimport {\n  type DependencyResolution,\n  scanDependenciesRecursively,\n  scanDependenciesInSearchPath,\n  filterMapResolutionResult,\n  mergeResolutionResults,\n} from '../dependencies';\nimport { PackageRevision, SearchResults, SupportedPlatform } from '../types';\n\nexport async function resolveExpoModule(\n  resolution: DependencyResolution,\n  platform: SupportedPlatform,\n  excludeNames: Set<string>\n): Promise<PackageRevision | null> {\n  if (excludeNames.has(resolution.name)) {\n    return null;\n  }\n  const expoModuleConfig = await discoverExpoModuleConfigAsync(resolution.path);\n  if (expoModuleConfig && expoModuleConfig.supportsPlatform(platform)) {\n    return {\n      name: resolution.name,\n      path: resolution.path,\n      version: resolution.version,\n      config: expoModuleConfig,\n      duplicates:\n        resolution.duplicates?.map((duplicate) => ({\n          name: duplicate.name,\n          path: duplicate.path,\n          version: duplicate.version,\n        })) ?? [],\n    };\n  } else {\n    return null;\n  }\n}\n\ninterface FindModulesParams {\n  appRoot: string;\n  autolinkingOptions: AutolinkingOptions & { platform: SupportedPlatform };\n}\n\n/** Searches for modules to link based on given config. */\nexport async function findModulesAsync({\n  appRoot,\n  autolinkingOptions,\n}: FindModulesParams): Promise<SearchResults> {\n  const excludeNames = new Set(autolinkingOptions.exclude);\n\n  // custom native modules should be resolved first so that they can override other modules\n  const searchPaths = autolinkingOptions.nativeModulesDir\n    ? [autolinkingOptions.nativeModulesDir, ...autolinkingOptions.searchPaths]\n    : autolinkingOptions.searchPaths;\n\n  return filterMapResolutionResult(\n    mergeResolutionResults(\n      await Promise.all([\n        ...searchPaths.map((searchPath) => scanDependenciesInSearchPath(searchPath)),\n        scanDependenciesRecursively(appRoot),\n      ])\n    ),\n    (resolution) => resolveExpoModule(resolution, autolinkingOptions.platform, excludeNames)\n  );\n}\n"]}