{"version": 3, "file": "generatePackageList.js", "sourceRoot": "", "sources": ["../../src/autolinking/generatePackageList.ts"], "names": [], "mappings": ";;AAUA,4DASC;AAUD,oEAeC;AA5CD,4CAAmE;AASnE,0EAA0E;AACnE,KAAK,UAAU,wBAAwB,CAC5C,OAA2B,EAC3B,MAAiC;IAEjC,MAAM,eAAe,GAAG,IAAA,+CAAmC,EAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC7E,IAAI,CAAC,CAAC,0BAA0B,IAAI,eAAe,CAAC,EAAE,CAAC;QACrD,MAAM,IAAI,KAAK,CAAC,0DAA0D,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;IAChG,CAAC;IACD,MAAM,eAAe,CAAC,wBAAwB,CAAC,OAAO,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;AAC/F,CAAC;AAQD;GACG;AACI,KAAK,UAAU,4BAA4B,CAChD,OAA2B,EAC3B,MAAqC;IAErC,MAAM,eAAe,GAAG,IAAA,+CAAmC,EAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC7E,IAAI,CAAC,CAAC,8BAA8B,IAAI,eAAe,CAAC,EAAE,CAAC;QACzD,MAAM,IAAI,KAAK,CACb,8DAA8D,MAAM,CAAC,QAAQ,GAAG,CACjF,CAAC;IACJ,CAAC;IACD,MAAM,eAAe,CAAC,4BAA4B,CAChD,OAAgC,EAChC,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,eAAe,CACvB,CAAC;AACJ,CAAC", "sourcesContent": ["import { getLinkingImplementationForPlatform } from '../platforms';\nimport { ModuleDescriptor, ModuleDescriptorIos, SupportedPlatform } from '../types';\n\ninterface GeneratePackageListParams {\n  platform: SupportedPlatform;\n  targetPath: string;\n  namespace: string;\n}\n\n/** Generates a source file listing all packages to link (Android-only) */\nexport async function generatePackageListAsync(\n  modules: ModuleDescriptor[],\n  params: GeneratePackageListParams\n) {\n  const platformLinking = getLinkingImplementationForPlatform(params.platform);\n  if (!('generatePackageListAsync' in platformLinking)) {\n    throw new Error(`Generating package list is not available for platform \"${params.platform}\"`);\n  }\n  await platformLinking.generatePackageListAsync(modules, params.targetPath, params.namespace);\n}\n\ninterface GenerateModulesProviderParams {\n  platform: SupportedPlatform;\n  targetPath: string;\n  entitlementPath: string | null;\n}\n\n/** Generates ExpoModulesProvider file listing all packages to link (Apple-only)\n */\nexport async function generateModulesProviderAsync(\n  modules: ModuleDescriptor[],\n  params: GenerateModulesProviderParams\n) {\n  const platformLinking = getLinkingImplementationForPlatform(params.platform);\n  if (!('generateModulesProviderAsync' in platformLinking)) {\n    throw new Error(\n      `Generating modules provider is not available for platform \"${params.platform}\"`\n    );\n  }\n  await platformLinking.generateModulesProviderAsync(\n    modules as ModuleDescriptorIos[],\n    params.targetPath,\n    params.entitlementPath\n  );\n}\n"]}