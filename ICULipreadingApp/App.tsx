import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  useColorScheme,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Colors } from 'react-native/Libraries/NewAppScreen';

// Import screens
import WelcomeScreen from './src/screens/WelcomeScreen';
import MainInterface from './src/screens/MainInterface';
import ResultsModal from './src/components/ResultsModal';
import ErrorModal from './src/components/ErrorModal';

const Stack = createStackNavigator();

function App(): JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [prediction, setPrediction] = useState('');
  const [recordedVideoUrl, setRecordedVideoUrl] = useState<string | null>(null);

  const backgroundStyle = {
    backgroundColor: isDarkMode ? Colors.darker : Colors.lighter,
    flex: 1,
  };

  const handleShowResults = (prediction: string, videoUrl: string) => {
    setPrediction(prediction);
    setRecordedVideoUrl(videoUrl);
    setShowResultsModal(true);
  };

  const handleShowError = (message: string) => {
    setErrorMessage(message);
    setShowErrorModal(true);
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Welcome"
          screenOptions={{
            headerShown: false,
          }}>
          <Stack.Screen name="Welcome" component={WelcomeScreen} />
          <Stack.Screen name="MainInterface">
            {(props) => (
              <MainInterface
                {...props}
                onShowResults={handleShowResults}
                onError={handleShowError}
              />
            )}
          </Stack.Screen>
        </Stack.Navigator>
      </NavigationContainer>

      {showResultsModal && (
        <ResultsModal
          prediction={prediction}
          videoUrl={recordedVideoUrl}
          onClose={() => setShowResultsModal(false)}
          onRetry={() => {
            setShowResultsModal(false);
          }}
          onRecordAgain={() => {
            setShowResultsModal(false);
          }}
        />
      )}

      {showErrorModal && (
        <ErrorModal
          message={errorMessage}
          onClose={() => setShowErrorModal(false)}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
