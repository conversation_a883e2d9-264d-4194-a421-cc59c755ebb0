import React from 'react';
import {
  Safe<PERSON>reaView,
  StatusBar,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  useColorScheme,
} from 'react-native';
import { Colors } from 'react-native/Libraries/NewAppScreen';

function AppTest(): JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  const backgroundStyle = {
    backgroundColor: isDarkMode ? Colors.darker : Colors.lighter,
    flex: 1,
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>🏥 SMHS Lipreader</Text>
          <Text style={styles.subtitle}>Basic Test - No require() calls</Text>
        </View>
        
        <View style={styles.content}>
          <Text style={styles.status}>✅ App loaded successfully!</Text>
          <Text style={styles.status}>✅ No require() errors</Text>
          <Text style={styles.status}>✅ Hermes engine compatible</Text>
          <Text style={styles.status}>✅ SDK 54 ready</Text>
          
          <View style={styles.illustration}>
            <Text style={styles.emoji}>👨‍⚕️📱</Text>
            <Text style={styles.description}>
              TRUE AI Lipreading for Medical Communication
            </Text>
          </View>
          
          <TouchableOpacity style={styles.button}>
            <Text style={styles.buttonText}>🎤 Test Button Works</Text>
          </TouchableOpacity>
          
          <View style={styles.phrases}>
            <Text style={styles.phrasesTitle}>Medical Phrases:</Text>
            <Text style={styles.phrase}>• "can i have some water?"</Text>
            <Text style={styles.phrase}>• "I'm in pain"</Text>
            <Text style={styles.phrase}>• "I feel cold"</Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#5a2b81',
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
  },
  content: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  status: {
    fontSize: 16,
    color: '#2d5a2d',
    marginBottom: 10,
    fontWeight: '500',
  },
  illustration: {
    alignItems: 'center',
    marginVertical: 30,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    width: '100%',
  },
  emoji: {
    fontSize: 60,
    marginBottom: 15,
  },
  description: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    lineHeight: 22,
  },
  button: {
    backgroundColor: '#5a2b81',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    marginVertical: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  phrases: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '100%',
    marginTop: 20,
  },
  phrasesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  phrase: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
});

export default AppTest;
