# ICU Lipreading App - Deployment Guide

This guide covers the deployment of the ICU Lipreading Application for hospital environments.

## 🏥 Hospital Deployment Overview

The ICU Lipreading App consists of two main components:
1. **Mobile Application** (React Native) - Runs on iOS/Android devices
2. **Backend Service** (Python Flask) - Processes lipreading inference

## 📱 Mobile App Deployment

### iOS Deployment

#### Option 1: App Store Distribution
1. **Apple Developer Account**: Enroll in Apple Developer Program
2. **Code Signing**: Configure certificates and provisioning profiles
3. **Build for Release**:
   ```bash
   cd ios
   xcodebuild -workspace ICULipreadingApp.xcworkspace -scheme ICULipreadingApp -configuration Release -archivePath ICULipreadingApp.xcarchive archive
   ```
4. **Submit to App Store**: Use Xcode or Application Loader

#### Option 2: Enterprise Distribution (Recommended for Hospitals)
1. **Apple Developer Enterprise Program**: Required for internal distribution
2. **In-House Distribution Certificate**: Create enterprise certificate
3. **Build and Sign**:
   ```bash
   xcodebuild -workspace ICULipreadingApp.xcworkspace -scheme ICULipreadingApp -configuration Release -exportArchive -archivePath ICULipreadingApp.xcarchive -exportPath ./build -exportOptionsPlist ExportOptions.plist
   ```
4. **Distribute IPA**: Deploy via MDM or direct installation

#### Option 3: Ad Hoc Distribution (Testing)
1. **Register Test Devices**: Add device UDIDs to Apple Developer account
2. **Create Ad Hoc Provisioning Profile**
3. **Build and Distribute**: Share IPA file with test devices

### Android Deployment

#### Option 1: Google Play Store
1. **Google Play Console**: Create developer account
2. **Build Release APK**:
   ```bash
   cd android
   ./gradlew assembleRelease
   ```
3. **Sign APK**: Use Android keystore
4. **Upload to Play Store**

#### Option 2: Enterprise Distribution (Recommended)
1. **Build Signed APK**:
   ```bash
   cd android
   ./gradlew assembleRelease
   ```
2. **Distribute via MDM**: Use hospital's mobile device management system
3. **Side-loading**: Enable "Unknown Sources" and install APK directly

## 🖥️ Backend Service Deployment

### Option 1: On-Premise Hospital Servers (Recommended)

#### Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **CPU**: 8+ cores (GPU recommended for faster processing)
- **RAM**: 16GB+ (32GB recommended)
- **Storage**: 100GB+ SSD
- **GPU**: NVIDIA GPU with CUDA support (optional but recommended)

#### Installation Steps

1. **Clone Repository**:
   ```bash
   git clone <repository-url>
   cd ICULipreadingApp/backend
   ```

2. **Install Dependencies**:
   ```bash
   sudo apt update
   sudo apt install python3.8 python3-pip git ffmpeg
   pip3 install -r requirements.txt
   ```

3. **Download Models**:
   ```bash
   # Download LRS3_V_WER19.1 model
   wget -O models/LRS3_V_WER19.1.zip "https://drive.google.com/uc?id=1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6"
   unzip models/LRS3_V_WER19.1.zip -d models/
   
   # Download language model
   wget -O models/lm_en_subword.zip "https://drive.google.com/uc?id=1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv"
   unzip models/lm_en_subword.zip -d models/
   ```

4. **Install Ollama**:
   ```bash
   curl -fsSL https://ollama.ai/install.sh | sh
   ollama pull llama3.2
   ```

5. **Configure Service**:
   ```bash
   sudo cp icu-lipreading.service /etc/systemd/system/
   sudo systemctl enable icu-lipreading
   sudo systemctl start icu-lipreading
   ```

6. **Setup Nginx (Optional)**:
   ```bash
   sudo apt install nginx
   sudo cp nginx.conf /etc/nginx/sites-available/icu-lipreading
   sudo ln -s /etc/nginx/sites-available/icu-lipreading /etc/nginx/sites-enabled/
   sudo systemctl restart nginx
   ```

### Option 2: Docker Deployment

1. **Build Docker Image**:
   ```bash
   cd backend
   docker build -t icu-lipreading-backend .
   ```

2. **Run Container**:
   ```bash
   docker run -d \
     --name icu-lipreading \
     -p 5000:5000 \
     -v $(pwd)/models:/app/models \
     --gpus all \
     icu-lipreading-backend
   ```

3. **Docker Compose** (with GPU support):
   ```yaml
   version: '3.8'
   services:
     icu-lipreading:
       build: .
       ports:
         - "5000:5000"
       volumes:
         - ./models:/app/models
       deploy:
         resources:
           reservations:
             devices:
               - driver: nvidia
                 count: 1
                 capabilities: [gpu]
   ```

### Option 3: Cloud Deployment

#### AWS Deployment
1. **EC2 Instance**: Launch GPU-enabled instance (p3.2xlarge recommended)
2. **Security Groups**: Configure ports 5000, 80, 443
3. **Load Balancer**: Use Application Load Balancer for high availability
4. **Auto Scaling**: Configure auto scaling groups

#### Azure Deployment
1. **Virtual Machine**: Use NC-series VMs with GPU
2. **Container Instances**: Deploy using Azure Container Instances
3. **App Service**: Use Azure App Service for Python

#### Google Cloud Deployment
1. **Compute Engine**: Use GPU-enabled instances
2. **Cloud Run**: Serverless deployment option
3. **GKE**: Kubernetes deployment for scalability

## 🔒 Security Configuration

### Network Security
```bash
# Firewall configuration
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 5000/tcp  # Backend API (internal only)
sudo ufw enable
```

### SSL/TLS Configuration
```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### Environment Variables
```bash
# Create .env file
cat > .env << EOF
FLASK_ENV=production
SECRET_KEY=your-secret-key-here
VSR_CONFIG_PATH=./configs/icu_lipreading.ini
OLLAMA_HOST=http://localhost:11434
LOG_LEVEL=INFO
EOF
```

## 📊 Monitoring and Logging

### Application Monitoring
```bash
# Install monitoring tools
pip install prometheus-client
pip install flask-prometheus-metrics
```

### Log Configuration
```python
# logging.conf
[loggers]
keys=root,icu_lipreading

[handlers]
keys=consoleHandler,fileHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=INFO
handlers=consoleHandler

[logger_icu_lipreading]
level=DEBUG
handlers=fileHandler
qualname=icu_lipreading

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=DEBUG
formatter=simpleFormatter
args=('/var/log/icu-lipreading.log',)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

## 🔧 Configuration Management

### Mobile App Configuration
Update `src/config/index.ts`:
```typescript
export const config = {
  API_BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'https://your-hospital-server.com/api'
    : 'http://localhost:5000',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
};
```

### Backend Configuration
Update `configs/icu_lipreading.ini`:
```ini
[server]
host = 0.0.0.0
port = 5000
debug = false

[model]
device = cuda
batch_size = 1
confidence_threshold = 0.7

[security]
cors_origins = https://your-hospital-domain.com
max_file_size = 50MB
rate_limit = 100/hour
```

## 🧪 Testing Deployment

### Backend Health Check
```bash
curl -X GET http://your-server:5000/health
```

### Mobile App Testing
1. **Device Testing**: Test on actual hospital devices
2. **Network Testing**: Test on hospital WiFi networks
3. **Performance Testing**: Test with multiple concurrent users
4. **Accessibility Testing**: Test with assistive technologies

## 📋 Maintenance

### Regular Updates
```bash
# Update backend dependencies
pip install -r requirements.txt --upgrade

# Update mobile app
npm update
```

### Model Updates
```bash
# Download new model versions
./scripts/update_models.sh
```

### Backup Strategy
```bash
# Backup configuration and logs
tar -czf backup-$(date +%Y%m%d).tar.gz configs/ logs/ models/
```

## 🚨 Troubleshooting

### Common Issues

1. **Model Loading Fails**:
   - Check model file permissions
   - Verify GPU drivers (if using GPU)
   - Check available memory

2. **Camera Permission Denied**:
   - Verify app permissions in device settings
   - Check MDM policies

3. **Network Connectivity**:
   - Verify firewall rules
   - Check hospital network policies
   - Test API endpoints

### Support Contacts
- **Technical Support**: <EMAIL>
- **Emergency Contact**: +1-XXX-XXX-XXXX
- **Documentation**: https://docs.icu-lipreading.com

---

**Note**: This deployment guide assumes compliance with hospital IT policies and HIPAA requirements. Always consult with your IT security team before deployment.
