# ICU Lipreading Mobile Application

A specialized mobile application designed for Intensive Care Unit (ICU) patients who cannot speak due to tracheostomy procedures. The app enables these patients to communicate by reading their lip movements using advanced AI technology.

## 🏥 Purpose

This application is specifically designed for ICU patients with tracheostomies who retain the ability to mouth words but cannot vocalize them. It provides a critical communication bridge between patients and their care teams.

## ✨ Features

### Core Functionality
- **Real-time Lipreading**: Advanced AI-powered lip reading using the Chaplin model
- **Medical Context Awareness**: Optimized for common ICU phrases and medical terminology
- **Auto-AVSR Integration**: Enhanced preprocessing for better accuracy
- **Offline Capability**: Works without internet connection for basic functionality

### ICU-Optimized Design
- **Large Touch Targets**: Easy interaction for patients with limited mobility
- **High Contrast Interface**: Optimized for various lighting conditions
- **Medical Phrase Recognition**: Pre-trained on common ICU communication needs
- **Quick Actions**: One-touch access to urgent requests

### Accessibility Features
- **Voice Synthesis**: Text-to-speech for confirmed messages
- **Large Text Display**: Easy-to-read results
- **Simple Navigation**: Minimal steps to communicate
- **Error Recovery**: Robust handling of recognition failures

## 🏗️ Architecture

### Frontend (React Native)
- **Cross-platform**: iOS and Android support
- **Native Performance**: Optimized camera and video processing
- **Responsive Design**: Works on phones and tablets
- **Offline-first**: Core functionality works without network

### Backend (Python Flask)
- **Chaplin Integration**: Real-time lipreading model
- **Auto-AVSR Preprocessing**: Advanced face detection and mouth ROI extraction
- **Medical Context**: LLM-powered text correction with medical awareness
- **RESTful API**: Clean interface for mobile app communication

## 📱 Installation

### Prerequisites
- Node.js 16+ and npm/yarn
- Python 3.8+
- iOS development environment (Xcode for iOS)
- Git

### Mobile App Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ICULipreadingApp
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **iOS Setup**
   ```bash
   cd ios
   pod install
   cd ..
   ```

4. **Run the app**
   ```bash
   # iOS
   npm run ios
   # or
   yarn ios
   ```

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Run setup script**
   ```bash
   python setup.py
   ```

3. **Download model files** (Manual step required)
   - Download [LRS3_V_WER19.1](https://drive.google.com/file/d/1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6/view)
   - Download [lm_en_subword](https://drive.google.com/file/d/1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv/view)
   - Place in `backend/models/` directory

4. **Start the backend**
   ```bash
   ./start_backend.sh
   ```

## 🔧 Configuration

### Backend Configuration
Edit `backend/configs/icu_lipreading.ini`:

```ini
[model]
name = LRS3_V_WER19.1
path = ./models/LRS3_V_WER19.1

[icu_settings]
medical_phrases_enabled = true
confidence_threshold = 0.7
fallback_enabled = true
```

### Mobile App Configuration
Update API endpoint in `src/config.ts`:

```typescript
export const API_BASE_URL = 'http://your-server:5000';
```

## 🏥 Usage

### For Patients

1. **Launch the app** and tap "Start Communication"
2. **Position your face** in the oval guide on the camera screen
3. **Press and hold** the record button while mouthing your words
4. **Review the result** and use quick actions if needed
5. **Share or show** the message to your care team

### For Care Teams

1. **Review patient messages** on the results screen
2. **Use quick actions** for common responses
3. **Check confidence levels** for accuracy assessment
4. **Access message history** for communication tracking

## 🔬 Technical Details

### Lipreading Pipeline

1. **Video Capture**: High-quality video recording optimized for lip reading
2. **Face Detection**: MediaPipe-based face and landmark detection
3. **Preprocessing**: Auto-AVSR pipeline for mouth ROI extraction
4. **Inference**: Chaplin model for lip-to-text conversion
5. **Post-processing**: LLM-based text correction with medical context

### Medical Phrase Recognition

The system is optimized for common ICU phrases:
- "I need water"
- "I'm in pain" 
- "I need suctioning"
- "Call the nurse"
- "Help me please"
- And many more...

### Performance Optimization

- **Mobile-first**: Optimized for mobile hardware constraints
- **Efficient Processing**: Minimal latency for real-time communication
- **Battery Conscious**: Power-efficient video processing
- **Network Resilient**: Graceful degradation without connectivity

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
npm run test:integration
```

### Backend Tests
```bash
cd backend
python -m pytest tests/
```

## 🚀 Deployment

### Mobile App Deployment
- **iOS**: Deploy through App Store or enterprise distribution
- **Android**: Deploy through Google Play or APK distribution

### Backend Deployment
- **Hospital Servers**: On-premise deployment for data privacy
- **Cloud Deployment**: AWS/Azure/GCP with HIPAA compliance
- **Docker**: Containerized deployment for easy scaling

## 🔒 Privacy & Security

- **Local Processing**: Video processing can be done locally
- **Data Encryption**: All communications encrypted in transit
- **HIPAA Compliance**: Designed with healthcare privacy requirements
- **No Data Storage**: Videos are processed and immediately discarded

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Chaplin Project**: Real-time lipreading technology
- **Auto-AVSR**: Advanced preprocessing pipeline
- **Medical Advisors**: ICU specialists who provided domain expertise
- **Patient Advocates**: Feedback from patient communities

## 📞 Support

For technical support or questions:
- Create an issue in this repository
- Contact the development team
- Refer to the troubleshooting guide in the docs/

## 🔮 Future Enhancements

- **Multi-language Support**: Support for additional languages
- **Personalized Models**: Patient-specific model fine-tuning
- **Integration APIs**: EHR and hospital system integration
- **Advanced Analytics**: Communication pattern analysis
- **Wearable Support**: Integration with smartwatches and other devices

---

**Note**: This application is designed as an assistive communication tool and should not replace emergency communication systems in critical situations.
