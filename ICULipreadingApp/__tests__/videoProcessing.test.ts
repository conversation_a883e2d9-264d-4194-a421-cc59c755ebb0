/**
 * Tests for video processing utilities
 */

import { VideoProcessor } from '../src/utils/videoProcessing';
import RNFS from 'react-native-fs';

// Mock RNFS
jest.mock('react-native-fs', () => ({
  exists: jest.fn(),
  stat: jest.fn(),
  unlink: jest.fn(),
}));

const mockRNFS = RNFS as jest.Mocked<typeof RNFS>;

describe('VideoProcessor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getOptimalRecordingOptions', () => {
    it('should return default options when no custom options provided', () => {
      const options = VideoProcessor.getOptimalRecordingOptions();
      
      expect(options).toEqual({
        maxDuration: 10,
        quality: 'medium',
        fps: 25,
        resolution: {
          width: 640,
          height: 480,
        },
      });
    });

    it('should merge custom options with defaults', () => {
      const customOptions = {
        maxDuration: 15,
        quality: 'high' as const,
      };
      
      const options = VideoProcessor.getOptimalRecordingOptions(customOptions);
      
      expect(options).toEqual({
        maxDuration: 15,
        quality: 'high',
        fps: 25,
        resolution: {
          width: 640,
          height: 480,
        },
      });
    });
  });

  describe('getCameraRecordingConfig', () => {
    it('should return correct config for medium quality', () => {
      const options = {
        maxDuration: 10,
        quality: 'medium' as const,
        fps: 25,
        resolution: { width: 640, height: 480 },
      };
      
      const config = VideoProcessor.getCameraRecordingConfig(options);
      
      expect(config).toMatchObject({
        quality: '720p',
        maxDuration: 10,
        maxFileSize: 50 * 1024 * 1024,
        mute: false,
        videoBitrate: 2000000,
        fps: 25,
      });
    });

    it('should return correct config for high quality', () => {
      const options = {
        maxDuration: 10,
        quality: 'high' as const,
        fps: 25,
        resolution: { width: 640, height: 480 },
      };
      
      const config = VideoProcessor.getCameraRecordingConfig(options);
      
      expect(config.quality).toBe('1080p');
      expect(config.videoBitrate).toBe(5000000);
    });

    it('should return correct config for low quality', () => {
      const options = {
        maxDuration: 10,
        quality: 'low' as const,
        fps: 25,
        resolution: { width: 640, height: 480 },
      };
      
      const config = VideoProcessor.getCameraRecordingConfig(options);
      
      expect(config.quality).toBe('480p');
      expect(config.videoBitrate).toBe(1000000);
    });
  });

  describe('validateVideo', () => {
    it('should validate a good video file', async () => {
      mockRNFS.exists.mockResolvedValue(true);
      mockRNFS.stat.mockResolvedValue({
        size: '5242880', // 5MB
        isFile: () => true,
        isDirectory: () => false,
        mtime: new Date(),
        ctime: new Date(),
        mode: 0,
        originalFilepath: 'test.mp4',
      });

      const result = await VideoProcessor.validateVideo('test.mp4');
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.metadata).toMatchObject({
        size: 5242880,
        format: 'mp4',
      });
    });

    it('should reject non-existent file', async () => {
      mockRNFS.exists.mockResolvedValue(false);

      const result = await VideoProcessor.validateVideo('nonexistent.mp4');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Video file does not exist');
    });

    it('should reject file that is too large', async () => {
      mockRNFS.exists.mockResolvedValue(true);
      mockRNFS.stat.mockResolvedValue({
        size: '104857600', // 100MB
        isFile: () => true,
        isDirectory: () => false,
        mtime: new Date(),
        ctime: new Date(),
        mode: 0,
        originalFilepath: 'large.mp4',
      });

      const result = await VideoProcessor.validateVideo('large.mp4');
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('File too large'))).toBe(true);
    });

    it('should reject file that is too small', async () => {
      mockRNFS.exists.mockResolvedValue(true);
      mockRNFS.stat.mockResolvedValue({
        size: '1024', // 1KB
        isFile: () => true,
        isDirectory: () => false,
        mtime: new Date(),
        ctime: new Date(),
        mode: 0,
        originalFilepath: 'tiny.mp4',
      });

      const result = await VideoProcessor.validateVideo('tiny.mp4');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File too small - may be corrupted');
    });

    it('should reject unsupported format', async () => {
      mockRNFS.exists.mockResolvedValue(true);
      mockRNFS.stat.mockResolvedValue({
        size: '5242880',
        isFile: () => true,
        isDirectory: () => false,
        mtime: new Date(),
        ctime: new Date(),
        mode: 0,
        originalFilepath: 'test.avi',
      });

      const result = await VideoProcessor.validateVideo('test.avi');
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('Unsupported format'))).toBe(true);
    });
  });

  describe('cleanupTempFiles', () => {
    it('should clean up existing files', async () => {
      const files = ['file1.mp4', 'file2.mp4'];
      mockRNFS.exists.mockResolvedValue(true);
      mockRNFS.unlink.mockResolvedValue(undefined);

      await VideoProcessor.cleanupTempFiles(files);
      
      expect(mockRNFS.exists).toHaveBeenCalledTimes(2);
      expect(mockRNFS.unlink).toHaveBeenCalledTimes(2);
      expect(mockRNFS.unlink).toHaveBeenCalledWith('file1.mp4');
      expect(mockRNFS.unlink).toHaveBeenCalledWith('file2.mp4');
    });

    it('should skip non-existent files', async () => {
      const files = ['file1.mp4', 'file2.mp4'];
      mockRNFS.exists.mockResolvedValueOnce(true).mockResolvedValueOnce(false);
      mockRNFS.unlink.mockResolvedValue(undefined);

      await VideoProcessor.cleanupTempFiles(files);
      
      expect(mockRNFS.exists).toHaveBeenCalledTimes(2);
      expect(mockRNFS.unlink).toHaveBeenCalledTimes(1);
      expect(mockRNFS.unlink).toHaveBeenCalledWith('file1.mp4');
    });

    it('should handle cleanup errors gracefully', async () => {
      const files = ['file1.mp4'];
      mockRNFS.exists.mockResolvedValue(true);
      mockRNFS.unlink.mockRejectedValue(new Error('Permission denied'));

      // Should not throw
      await expect(VideoProcessor.cleanupTempFiles(files)).resolves.toBeUndefined();
    });
  });

  describe('getDeviceOptimizedSettings', () => {
    it('should return optimized settings', () => {
      const settings = VideoProcessor.getDeviceOptimizedSettings();
      
      expect(settings).toHaveProperty('maxDuration');
      expect(settings).toHaveProperty('quality');
      expect(settings).toHaveProperty('fps');
      expect(settings).toHaveProperty('resolution');
      expect(settings.maxDuration).toBeGreaterThan(0);
      expect(settings.fps).toBeGreaterThan(0);
    });
  });
});
