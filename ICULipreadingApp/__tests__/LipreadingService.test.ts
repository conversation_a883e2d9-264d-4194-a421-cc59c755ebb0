/**
 * Tests for LipreadingService
 */

import { LipreadingService } from '../src/services/LipreadingService';

// Mock fetch
global.fetch = jest.fn();

describe('LipreadingService', () => {
  let service: LipreadingService;
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    service = new LipreadingService('http://localhost:5000', 10000);
    mockFetch.mockClear();
  });

  describe('healthCheck', () => {
    it('should return true when service is healthy', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ status: 'healthy' }),
      } as Response);

      const result = await service.healthCheck();
      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledWith('http://localhost:5000/health', {
        method: 'GET',
        timeout: 5000,
      });
    });

    it('should return false when service is unhealthy', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ status: 'unhealthy' }),
      } as Response);

      const result = await service.healthCheck();
      expect(result).toBe(false);
    });

    it('should return false when request fails', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await service.healthCheck();
      expect(result).toBe(false);
    });
  });

  describe('processVideo', () => {
    it('should process video successfully', async () => {
      const mockResult = {
        status: 'success',
        prediction: 'I need water',
        raw_output: 'I NEED WATER',
        confidence: 0.85,
        timestamp: Date.now(),
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResult,
      } as Response);

      const result = await service.processVideo('file://test-video.mp4');
      
      expect(result).toEqual({
        prediction: 'I need water',
        raw_output: 'I NEED WATER',
        confidence: 0.85,
        timestamp: mockResult.timestamp,
      });
    });

    it('should handle processing errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: 'Processing failed' }),
      } as Response);

      await expect(service.processVideo('file://test-video.mp4'))
        .rejects.toThrow('Processing failed');
    });

    it('should handle network timeout', async () => {
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject({ name: 'AbortError' }), 100)
        )
      );

      await expect(service.processVideo('file://test-video.mp4'))
        .rejects.toThrow('Request timeout - processing took too long');
    });
  });

  describe('getMedicalPhrases', () => {
    it('should return medical phrases', async () => {
      const mockPhrases = ['I need water', 'I\'m in pain', 'Help me'];
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ phrases: mockPhrases }),
      } as Response);

      const result = await service.getMedicalPhrases();
      expect(result).toEqual(mockPhrases);
    });

    it('should return empty array on error', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await service.getMedicalPhrases();
      expect(result).toEqual([]);
    });
  });

  describe('simulateProcessing', () => {
    it('should return simulated result', async () => {
      const result = await service.simulateProcessing();
      
      expect(result).toHaveProperty('prediction');
      expect(result).toHaveProperty('raw_output');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('timestamp');
      expect(result.confidence).toBeGreaterThanOrEqual(0.7);
      expect(result.confidence).toBeLessThanOrEqual(1.0);
    });

    it('should include medical phrases', async () => {
      const results = await Promise.all(
        Array(10).fill(0).map(() => service.simulateProcessing())
      );
      
      const predictions = results.map(r => r.prediction);
      const medicalPhrases = [
        "I need water",
        "I need suctioning", 
        "I'm in pain",
        "I need my medication",
        "I feel nauseous",
        "I can't breathe",
        "I'm feeling dizzy",
        "Can you call the nurse",
        "Help me please",
        "Thank you"
      ];

      predictions.forEach(prediction => {
        expect(medicalPhrases).toContain(prediction);
      });
    });
  });
});
