/**
 * Permission utilities for camera and microphone access
 */

import { Platform, Alert, Linking } from 'react-native';
import { request, check, PERMISSIONS, RESULTS, Permission } from 'react-native-permissions';

export type PermissionStatus = 'granted' | 'denied' | 'blocked' | 'unavailable';

export interface PermissionResult {
  status: PermissionStatus;
  canRequest: boolean;
}

/**
 * Get the appropriate camera permission for the platform
 */
const getCameraPermission = (): Permission => {
  return Platform.OS === 'ios' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA;
};

/**
 * Get the appropriate microphone permission for the platform
 */
const getMicrophonePermission = (): Permission => {
  return Platform.OS === 'ios' ? PERMISSIONS.IOS.MICROPHONE : PERMISSIONS.ANDROID.RECORD_AUDIO;
};

/**
 * Check camera permission status
 */
export const checkCameraPermission = async (): Promise<PermissionResult> => {
  try {
    const permission = getCameraPermission();
    const result = await check(permission);
    
    return {
      status: result as PermissionStatus,
      canRequest: result !== RESULTS.BLOCKED,
    };
  } catch (error) {
    console.error('Error checking camera permission:', error);
    return {
      status: 'unavailable',
      canRequest: false,
    };
  }
};

/**
 * Request camera permission
 */
export const requestCameraPermission = async (): Promise<PermissionResult> => {
  try {
    const permission = getCameraPermission();
    const result = await request(permission);
    
    return {
      status: result as PermissionStatus,
      canRequest: result !== RESULTS.BLOCKED,
    };
  } catch (error) {
    console.error('Error requesting camera permission:', error);
    return {
      status: 'denied',
      canRequest: false,
    };
  }
};

/**
 * Check microphone permission status
 */
export const checkMicrophonePermission = async (): Promise<PermissionResult> => {
  try {
    const permission = getMicrophonePermission();
    const result = await check(permission);
    
    return {
      status: result as PermissionStatus,
      canRequest: result !== RESULTS.BLOCKED,
    };
  } catch (error) {
    console.error('Error checking microphone permission:', error);
    return {
      status: 'unavailable',
      canRequest: false,
    };
  }
};

/**
 * Request microphone permission
 */
export const requestMicrophonePermission = async (): Promise<PermissionResult> => {
  try {
    const permission = getMicrophonePermission();
    const result = await request(permission);
    
    return {
      status: result as PermissionStatus,
      canRequest: result !== RESULTS.BLOCKED,
    };
  } catch (error) {
    console.error('Error requesting microphone permission:', error);
    return {
      status: 'denied',
      canRequest: false,
    };
  }
};

/**
 * Request all required permissions for the app
 */
export const requestAllPermissions = async (): Promise<{
  camera: PermissionResult;
  microphone: PermissionResult;
}> => {
  const [camera, microphone] = await Promise.all([
    requestCameraPermission(),
    requestMicrophonePermission(),
  ]);

  return { camera, microphone };
};

/**
 * Show permission denied alert with option to open settings
 */
export const showPermissionDeniedAlert = (permissionType: 'camera' | 'microphone') => {
  const title = `${permissionType === 'camera' ? 'Camera' : 'Microphone'} Permission Required`;
  const message = `This app needs ${permissionType} access to function properly. Please enable ${permissionType} permission in Settings.`;

  Alert.alert(
    title,
    message,
    [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Open Settings',
        onPress: () => Linking.openSettings(),
      },
    ]
  );
};

/**
 * Show permission blocked alert
 */
export const showPermissionBlockedAlert = (permissionType: 'camera' | 'microphone') => {
  const title = `${permissionType === 'camera' ? 'Camera' : 'Microphone'} Access Blocked`;
  const message = `${permissionType === 'camera' ? 'Camera' : 'Microphone'} access has been permanently denied. Please enable it in Settings to use this feature.`;

  Alert.alert(
    title,
    message,
    [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Open Settings',
        onPress: () => Linking.openSettings(),
      },
    ]
  );
};

/**
 * Handle permission result and show appropriate alerts
 */
export const handlePermissionResult = (
  result: PermissionResult,
  permissionType: 'camera' | 'microphone'
): boolean => {
  switch (result.status) {
    case 'granted':
      return true;
    
    case 'denied':
      if (result.canRequest) {
        showPermissionDeniedAlert(permissionType);
      } else {
        showPermissionBlockedAlert(permissionType);
      }
      return false;
    
    case 'blocked':
      showPermissionBlockedAlert(permissionType);
      return false;
    
    case 'unavailable':
      Alert.alert(
        'Feature Unavailable',
        `${permissionType === 'camera' ? 'Camera' : 'Microphone'} is not available on this device.`
      );
      return false;
    
    default:
      return false;
  }
};
