/**
 * Mobile-optimized video processing utilities
 * Handles video capture, compression, and preprocessing for lipreading
 */

import React, { useState } from 'react';
import { Platform } from 'react-native';
import RNFS from 'react-native-fs';

export interface VideoProcessingOptions {
  maxDuration: number;
  quality: 'low' | 'medium' | 'high';
  fps: number;
  resolution: {
    width: number;
    height: number;
  };
}

export interface ProcessedVideo {
  uri: string;
  duration: number;
  size: number;
  fps: number;
  resolution: {
    width: number;
    height: number;
  };
}

export class VideoProcessor {
  private static readonly DEFAULT_OPTIONS: VideoProcessingOptions = {
    maxDuration: 10, // 10 seconds
    quality: 'medium',
    fps: 25, // Optimal for lipreading
    resolution: {
      width: 640,
      height: 480,
    },
  };

  /**
   * Get optimal video recording options for lipreading
   */
  static getOptimalRecordingOptions(customOptions?: Partial<VideoProcessingOptions>): VideoProcessingOptions {
    return {
      ...VideoProcessor.DEFAULT_OPTIONS,
      ...customOptions,
    };
  }

  /**
   * Get camera recording configuration for React Native Camera
   */
  static getCameraRecordingConfig(options: VideoProcessingOptions) {
    const qualityMap = {
      low: '480p',
      medium: '720p',
      high: '1080p',
    };

    return {
      quality: qualityMap[options.quality],
      maxDuration: options.maxDuration,
      maxFileSize: 50 * 1024 * 1024, // 50MB max
      mute: false, // We might want audio for future enhancements
      mirrorVideo: Platform.OS === 'ios', // Mirror front camera on iOS
      videoBitrate: options.quality === 'high' ? 5000000 : 
                   options.quality === 'medium' ? 2000000 : 1000000,
      fps: options.fps,
    };
  }

  /**
   * Validate video file for lipreading processing
   */
  static async validateVideo(videoUri: string): Promise<{
    isValid: boolean;
    errors: string[];
    metadata?: {
      duration: number;
      size: number;
      format: string;
    };
  }> {
    const errors: string[] = [];

    try {
      // Check if file exists
      const fileExists = await RNFS.exists(videoUri);
      if (!fileExists) {
        errors.push('Video file does not exist');
        return { isValid: false, errors };
      }

      // Get file stats
      const stats = await RNFS.stat(videoUri);
      const fileSizeBytes = parseInt(stats.size);
      const fileSizeMB = fileSizeBytes / (1024 * 1024);

      // Validate file size (max 50MB)
      if (fileSizeMB > 50) {
        errors.push(`File too large: ${fileSizeMB.toFixed(1)}MB (max 50MB)`);
      }

      // Validate minimum file size (at least 100KB)
      if (fileSizeBytes < 100 * 1024) {
        errors.push('File too small - may be corrupted');
      }

      // Basic format validation (extension check)
      const extension = videoUri.split('.').pop()?.toLowerCase();
      const supportedFormats = ['mp4', 'mov', 'webm'];
      if (!extension || !supportedFormats.includes(extension)) {
        errors.push(`Unsupported format: ${extension}. Supported: ${supportedFormats.join(', ')}`);
      }

      return {
        isValid: errors.length === 0,
        errors,
        metadata: {
          duration: 0, // Would need native module to get actual duration
          size: fileSizeBytes,
          format: extension || 'unknown',
        },
      };

    } catch (error) {
      errors.push(`Validation error: ${error.message}`);
      return { isValid: false, errors };
    }
  }

  /**
   * Compress video for optimal processing
   * Note: This would require a native video processing library
   */
  static async compressVideo(
    inputUri: string,
    options: Partial<VideoProcessingOptions> = {}
  ): Promise<ProcessedVideo> {
    const processingOptions = {
      ...VideoProcessor.DEFAULT_OPTIONS,
      ...options,
    };

    // For now, return the original video
    // In a production app, you'd use a library like react-native-video-processing
    // or implement native video compression
    
    try {
      const stats = await RNFS.stat(inputUri);
      
      return {
        uri: inputUri,
        duration: processingOptions.maxDuration,
        size: parseInt(stats.size),
        fps: processingOptions.fps,
        resolution: processingOptions.resolution,
      };
    } catch (error) {
      throw new Error(`Video compression failed: ${error.message}`);
    }
  }

  /**
   * Extract frames from video for preprocessing
   * Note: This would require native implementation
   */
  static async extractFrames(
    videoUri: string,
    frameCount: number = 25
  ): Promise<string[]> {
    // Placeholder implementation
    // In production, this would extract actual frames from the video
    // and return their file paths
    
    console.log(`Extracting ${frameCount} frames from ${videoUri}`);
    
    // Return empty array for now
    return [];
  }

  /**
   * Optimize video for lipreading processing
   */
  static async optimizeForLipreading(videoUri: string): Promise<ProcessedVideo> {
    // Validate the video first
    const validation = await VideoProcessor.validateVideo(videoUri);
    if (!validation.isValid) {
      throw new Error(`Video validation failed: ${validation.errors.join(', ')}`);
    }

    // Apply lipreading-specific optimizations
    const optimizedOptions: VideoProcessingOptions = {
      maxDuration: 10,
      quality: 'medium', // Balance between quality and processing speed
      fps: 25, // Standard for lipreading models
      resolution: {
        width: 640, // Sufficient for face detection
        height: 480,
      },
    };

    return VideoProcessor.compressVideo(videoUri, optimizedOptions);
  }

  /**
   * Clean up temporary video files
   */
  static async cleanupTempFiles(videoUris: string[]): Promise<void> {
    const cleanupPromises = videoUris.map(async (uri) => {
      try {
        const exists = await RNFS.exists(uri);
        if (exists) {
          await RNFS.unlink(uri);
          console.log(`Cleaned up temp file: ${uri}`);
        }
      } catch (error) {
        console.warn(`Failed to cleanup file ${uri}:`, error);
      }
    });

    await Promise.all(cleanupPromises);
  }

  /**
   * Get video metadata (requires native implementation)
   */
  static async getVideoMetadata(videoUri: string): Promise<{
    duration: number;
    fps: number;
    resolution: { width: number; height: number };
    bitrate: number;
    format: string;
  }> {
    // Placeholder implementation
    // In production, this would use a native module to extract actual metadata
    
    return {
      duration: 5.0,
      fps: 25,
      resolution: { width: 640, height: 480 },
      bitrate: 2000000,
      format: 'mp4',
    };
  }

  /**
   * Calculate optimal processing parameters based on device capabilities
   */
  static getDeviceOptimizedSettings(): VideoProcessingOptions {
    // Basic device detection - in production, you'd use more sophisticated detection
    const isLowEndDevice = Platform.OS === 'android'; // Simplified assumption
    
    if (isLowEndDevice) {
      return {
        maxDuration: 8,
        quality: 'low',
        fps: 20,
        resolution: { width: 480, height: 360 },
      };
    }

    return VideoProcessor.DEFAULT_OPTIONS;
  }
}

/**
 * Video recording hook for React Native components
 */
export const useVideoRecording = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordedVideo, setRecordedVideo] = useState<ProcessedVideo | null>(null);
  const [processingError, setProcessingError] = useState<string | null>(null);

  const startRecording = async (cameraRef: any, options?: Partial<VideoProcessingOptions>) => {
    try {
      setIsRecording(true);
      setProcessingError(null);
      
      const recordingOptions = VideoProcessor.getCameraRecordingConfig(
        VideoProcessor.getOptimalRecordingOptions(options)
      );

      const data = await cameraRef.current?.recordAsync(recordingOptions);
      
      if (data?.uri) {
        const processedVideo = await VideoProcessor.optimizeForLipreading(data.uri);
        setRecordedVideo(processedVideo);
      }
    } catch (error) {
      setProcessingError(error.message);
      console.error('Recording error:', error);
    } finally {
      setIsRecording(false);
    }
  };

  const stopRecording = async (cameraRef: any) => {
    try {
      await cameraRef.current?.stopRecording();
    } catch (error) {
      console.error('Stop recording error:', error);
    }
  };

  const clearRecording = async () => {
    if (recordedVideo) {
      await VideoProcessor.cleanupTempFiles([recordedVideo.uri]);
      setRecordedVideo(null);
    }
    setProcessingError(null);
  };

  return {
    isRecording,
    recordedVideo,
    processingError,
    startRecording,
    stopRecording,
    clearRecording,
  };
};
