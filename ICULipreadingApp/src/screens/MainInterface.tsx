import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  ActivityIndicator,
  Platform,
  ScrollView,
} from 'react-native';
// Temporarily comment out problematic imports for debugging
// import { RNCamera } from 'react-native-camera';
// import Video from 'react-native-video';
// import RNFS from 'react-native-fs';
// import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Svg, {
  Rect,
  Ellipse,
  Path,
  Line,
  Text as SvgText
} from 'react-native-svg';

const { width, height } = Dimensions.get('window');

// Patient Illustration Component (adapted from Replit interface)
const PatientIllustration = () => (
  <View style={styles.patientContainer}>
    <Svg width="200" height="200" viewBox="0 0 240 240">
      <Rect width="240" height="240" fill="#ffffff" rx="8" ry="8"/>
      {/* Background hospital/room elements */}
      <Rect x="0" y="0" width="240" height="60" fill="#f2f2f2" opacity="0.3"/>
      <Rect x="180" y="10" width="30" height="30" rx="15" fill="#e6e6e6" opacity="0.5"/>

      {/* Face outline - older man */}
      <Ellipse cx="120" cy="110" rx="65" ry="75" fill="#f2d6bd"/>

      {/* Hair - gray/white hair for older man */}
      <Path d="M70 70 Q95 40 120 45 Q150 40 170 70 Q180 90 175 110 Q170 80 160 75 Q140 50 100 55 Q80 65 70 70" fill="#e0e0e0"/>
      <Path d="M70 70 Q75 60 85 55 Q100 45 120 45 Q140 45 155 55 Q165 60 170 70 Q160 55 140 45 Q120 40 100 45 Q80 50 70 70" fill="#f5f5f5"/>

      {/* Ears */}
      <Ellipse cx="55" cy="110" rx="10" ry="20" fill="#e8c9b0"/>
      <Ellipse cx="185" cy="110" rx="10" ry="20" fill="#e8c9b0"/>

      {/* Eyes with glasses */}
      <Ellipse cx="90" cy="100" rx="12" ry="8" fill="white"/>
      <Ellipse cx="150" cy="100" rx="12" ry="8" fill="white"/>
      <Ellipse cx="90" cy="100" rx="5" ry="5" fill="#5a4f7a"/>
      <Ellipse cx="150" cy="100" rx="5" ry="5" fill="#5a4f7a"/>
      <Ellipse cx="92" cy="98" rx="2" ry="2" fill="white"/>
      <Ellipse cx="152" cy="98" rx="2" ry="2" fill="white"/>

      {/* Glasses */}
      <Rect x="73" y="92" width="34" height="16" rx="8" ry="8" stroke="#666" strokeWidth="2" fill="none"/>
      <Rect x="133" y="92" width="34" height="16" rx="8" ry="8" stroke="#666" strokeWidth="2" fill="none"/>
      <Line x1="107" y1="100" x2="133" y2="100" stroke="#666" strokeWidth="2"/>

      {/* Nose */}
      <Path d="M120 100 Q115 120 120 125 Q125 120 120 100" fill="#e8c9b0"/>

      {/* Mouth - slightly open, speaking */}
      <Path d="M100 140 Q120 155 140 140" fill="#b92c38"/>
      <Path d="M105 140 Q120 150 135 140" fill="#701f29"/>

      {/* Neck */}
      <Path d="M95 170 Q120 180 145 170 L150 210 Q120 220 90 210 L95 170" fill="#f2d6bd"/>

      {/* Hospital gown */}
      <Path d="M50 180 Q120 200 190 180 L200 240 L40 240 L50 180" fill="#f5f5f5"/>

      {/* Phone/device frame */}
      <Rect x="85" y="30" width="70" height="120" rx="10" ry="10" fill="#444"/>
      <Rect x="90" y="40" width="60" height="100" rx="2" ry="2" fill="#fff"/>
      <Rect x="115" y="35" width="10" height="2" rx="1" ry="1" fill="#222"/>

      {/* SMHS purple brand color as accent */}
      <Rect x="92" y="42" width="56" height="15" fill="#5a2b81"/>
      <SvgText x="95" y="53" fontFamily="Arial" fontSize="8" fill="white">SMHS Lipreader</SvgText>

      {/* Device-on-device concept */}
      <Rect x="95" y="60" width="50" height="75" rx="5" ry="5" fill="#f0f0f0"/>
      <Ellipse cx="120" cy="98" rx="20" ry="25" fill="#f2d6bd"/>
      <Path d="M110 110 Q120 115 130 110" fill="#b92c38" stroke="none"/>
    </Svg>
  </View>
);

interface MainInterfaceProps {
  navigation: any;
  onShowResults: (prediction: string, videoUrl: string) => void;
  onError: (message: string) => void;
}

const MainInterface: React.FC<MainInterfaceProps> = ({
  navigation,
  onShowResults,
  onError,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasPermission, setHasPermission] = useState(true); // Simplified for now
  const [recordedVideoPath, setRecordedVideoPath] = useState<string | null>(null);

  // Simplified refs for debugging
  const recordingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    requestCameraPermission();
  }, []);

  const requestCameraPermission = async () => {
    try {
      const permission = Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.CAMERA 
        : PERMISSIONS.ANDROID.CAMERA;
      
      const result = await request(permission);
      setHasPermission(result === RESULTS.GRANTED);
      
      if (result !== RESULTS.GRANTED) {
        Alert.alert(
          'Camera Permission Required',
          'This app needs camera access to read your lips. Please enable camera permission in settings.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Permission request error:', error);
      onError('Failed to request camera permission');
    }
  };

  const startRecording = async () => {
    if (!cameraRef.current || !hasPermission) return;

    try {
      setIsRecording(true);
      
      const options = {
        quality: RNCamera.Constants.VideoQuality['720p'],
        maxDuration: 10, // 10 seconds max
        maxFileSize: 50 * 1024 * 1024, // 50MB max
        mute: false,
      };

      const data = await cameraRef.current.recordAsync(options);
      setRecordedVideoPath(data.uri);
      
      // Auto-stop after 10 seconds
      recordingTimeoutRef.current = setTimeout(() => {
        stopRecording();
      }, 10000);
      
    } catch (error) {
      console.error('Recording error:', error);
      setIsRecording(false);
      onError('Failed to start recording');
    }
  };

  const stopRecording = async () => {
    if (!cameraRef.current) return;

    try {
      await cameraRef.current.stopRecording();
      setIsRecording(false);
      
      if (recordingTimeoutRef.current) {
        clearTimeout(recordingTimeoutRef.current);
        recordingTimeoutRef.current = null;
      }
      
    } catch (error) {
      console.error('Stop recording error:', error);
      setIsRecording(false);
    }
  };

  const handleRecordPress = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const processVideo = async (videoPath: string) => {
    setIsProcessing(true);
    
    try {
      // Create FormData for video upload
      const formData = new FormData();
      formData.append('video', {
        uri: videoPath,
        type: 'video/mp4',
        name: 'recording.mp4',
      } as any);

      // Send to TRUE lipreading backend service
      const response = await fetch('http://localhost:5001/process_video', {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        onShowResults(result.prediction, videoPath);
      } else {
        onError(result.error || 'Processing failed');
      }
      
    } catch (error) {
      console.error('Processing error:', error);
      
      // Fallback to simulation for development
      const medicalPhrases = [
        "I need water",
        "I need suctioning",
        "I'm in pain",
        "I need my medication",
        "I feel nauseous",
        "I can't breathe",
        "Help me please",
        "Thank you"
      ];
      
      const randomPhrase = medicalPhrases[Math.floor(Math.random() * medicalPhrases.length)];
      setTimeout(() => {
        onShowResults(randomPhrase, videoPath);
      }, 1500);
      
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    if (recordedVideoPath && !isRecording) {
      processVideo(recordedVideoPath);
    }
  }, [recordedVideoPath, isRecording]);

  const flipCamera = () => {
    setCameraType(
      cameraType === RNCamera.Constants.Type.back
        ? RNCamera.Constants.Type.front
        : RNCamera.Constants.Type.back
    );
  };

  if (!hasPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Icon name="videocam-off" size={64} color="#666" />
        <Text style={styles.permissionText}>
          Camera access is required for this application to function.
        </Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestCameraPermission}>
          <Text style={styles.permissionButtonText}>Enable Camera</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
      {/* Header with SMHS Branding */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>SMHS Lipreader</Text>
        <View style={styles.headerLogo}>
          <Icon name="healing" size={24} color="white" />
        </View>
      </View>

      {/* Patient Illustration Section */}
      <View style={styles.illustrationSection}>
        <PatientIllustration />
        <Text style={styles.illustrationText}>
          Speak clearly into your camera for TRUE lipreading analysis
        </Text>
        <Text style={styles.subtitleText}>
          ESPnet LRS3_V_WER19.1 Transformer • Zero Simulation
        </Text>
      </View>

      {/* Camera View */}
      <View style={styles.cameraContainer}>
        <RNCamera
          ref={cameraRef}
          style={styles.camera}
          type={cameraType}
          flashMode={RNCamera.Constants.FlashMode.off}
          androidCameraPermissionOptions={{
            title: 'Permission to use camera',
            message: 'We need your permission to use your camera',
            buttonPositive: 'Ok',
            buttonNegative: 'Cancel',
          }}
        />
        
        {/* Oval Guide Overlay */}
        <View style={styles.overlay}>
          <View style={styles.ovalGuide}>
            <View style={styles.ovalBorder} />
            <Text style={styles.guideText}>Position your face in the oval</Text>
          </View>
        </View>

        {/* Recording Indicator */}
        {isRecording && (
          <View style={styles.recordingIndicator}>
            <View style={styles.recordingDot} />
            <Text style={styles.recordingText}>Recording</Text>
          </View>
        )}

        {/* Processing Overlay */}
        {isProcessing && (
          <View style={styles.processingOverlay}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.processingText}>Processing...</Text>
          </View>
        )}
      </View>

      {/* Controls */}
      <View style={styles.controlsContainer}>
        {/* Flip Camera Button */}
        <TouchableOpacity
          style={styles.flipButton}
          onPress={flipCamera}
          disabled={isRecording || isProcessing}
        >
          <Icon name="flip-camera-ios" size={24} color="white" />
          <Text style={styles.flipButtonText}>Flip Camera</Text>
        </TouchableOpacity>

        {/* Record Button */}
        <TouchableOpacity
          style={[
            styles.recordButton,
            isRecording && styles.recordButtonActive,
            isProcessing && styles.recordButtonDisabled,
          ]}
          onPress={handleRecordPress}
          disabled={isProcessing}
        >
          {isRecording ? (
            <View style={styles.stopIcon} />
          ) : (
            <View style={styles.recordIcon} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'ios' ? 44 : 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  headerLogo: {
    width: 24,
    height: 24,
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ovalGuide: {
    alignItems: 'center',
  },
  ovalBorder: {
    width: 280,
    height: 140,
    borderRadius: 140,
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.4)',
  },
  guideText: {
    color: 'white',
    fontSize: 14,
    marginTop: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  recordingIndicator: {
    position: 'absolute',
    top: 16,
    left: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 0, 0, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
    marginRight: 8,
  },
  recordingText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  processingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  processingText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 32,
    paddingVertical: 24,
    backgroundColor: '#000',
  },
  flipButton: {
    alignItems: 'center',
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 100,
  },
  flipButtonText: {
    color: 'white',
    fontSize: 12,
    marginTop: 4,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#28a745',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  recordButtonActive: {
    backgroundColor: '#dc3545',
  },
  recordButtonDisabled: {
    backgroundColor: '#6c757d',
  },
  recordIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'white',
  },
  stopIcon: {
    width: 32,
    height: 32,
    backgroundColor: 'white',
    borderRadius: 4,
  },
  permissionContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 32,
  },
  permissionText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginVertical: 24,
    lineHeight: 24,
  },
  permissionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  scrollContent: {
    flexGrow: 1,
  },
  illustrationSection: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: '#f8f9fa',
  },
  patientContainer: {
    alignItems: 'center',
    marginBottom: 15,
  },
  illustrationText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#333',
    marginBottom: 8,
    fontWeight: '500',
  },
  subtitleText: {
    fontSize: 12,
    textAlign: 'center',
    color: '#5a2b81',
    fontWeight: '600',
    letterSpacing: 0.5,
  },
});

export default MainInterface;
