import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Svg, {
  Rect,
  Ellipse,
  Path,
  Line,
  Text as SvgText
} from 'react-native-svg';

interface MainInterfaceProps {
  navigation: any;
  onShowResults: (prediction: string, videoUrl: string) => void;
  onError: (message: string) => void;
}

// Patient Illustration Component (adapted from Replit interface)
const PatientIllustration = () => (
  <View style={styles.patientContainer}>
    <Svg width="200" height="200" viewBox="0 0 240 240">
      <Rect width="240" height="240" fill="#ffffff" rx="8" ry="8"/>
      {/* Background hospital/room elements */}
      <Rect x="0" y="0" width="240" height="60" fill="#f2f2f2" opacity="0.3"/>
      <Rect x="180" y="10" width="30" height="30" rx="15" fill="#e6e6e6" opacity="0.5"/>
      
      {/* Face outline - older man */}
      <Ellipse cx="120" cy="110" rx="65" ry="75" fill="#f2d6bd"/>
      
      {/* Hair - gray/white hair for older man */}
      <Path d="M70 70 Q95 40 120 45 Q150 40 170 70 Q180 90 175 110 Q170 80 160 75 Q140 50 100 55 Q80 65 70 70" fill="#e0e0e0"/>
      <Path d="M70 70 Q75 60 85 55 Q100 45 120 45 Q140 45 155 55 Q165 60 170 70 Q160 55 140 45 Q120 40 100 45 Q80 50 70 70" fill="#f5f5f5"/>
      
      {/* Eyes with glasses */}
      <Ellipse cx="90" cy="100" rx="12" ry="8" fill="white"/>
      <Ellipse cx="150" cy="100" rx="12" ry="8" fill="white"/>
      <Ellipse cx="90" cy="100" rx="5" ry="5" fill="#5a4f7a"/>
      <Ellipse cx="150" cy="100" rx="5" ry="5" fill="#5a4f7a"/>
      
      {/* Glasses */}
      <Rect x="73" y="92" width="34" height="16" rx="8" ry="8" stroke="#666" strokeWidth="2" fill="none"/>
      <Rect x="133" y="92" width="34" height="16" rx="8" ry="8" stroke="#666" strokeWidth="2" fill="none"/>
      <Line x1="107" y1="100" x2="133" y2="100" stroke="#666" strokeWidth="2"/>
      
      {/* Mouth - slightly open, speaking */}
      <Path d="M100 140 Q120 155 140 140" fill="#b92c38"/>
      <Path d="M105 140 Q120 150 135 140" fill="#701f29"/>
      
      {/* Hospital gown */}
      <Path d="M50 180 Q120 200 190 180 L200 240 L40 240 L50 180" fill="#f5f5f5"/>
      
      {/* Phone/device frame */}
      <Rect x="85" y="30" width="70" height="120" rx="10" ry="10" fill="#444"/>
      <Rect x="90" y="40" width="60" height="100" rx="2" ry="2" fill="#fff"/>
      
      {/* SMHS purple brand color as accent */}
      <Rect x="92" y="42" width="56" height="15" fill="#5a2b81"/>
      <SvgText x="95" y="53" fontFamily="Arial" fontSize="8" fill="white">SMHS Lipreader</SvgText>
    </Svg>
  </View>
);

const MainInterfaceSimple: React.FC<MainInterfaceProps> = ({
  navigation,
  onShowResults,
  onError,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleTestRecording = async () => {
    setIsProcessing(true);
    
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      onShowResults("I'm in pain", "test-video-url");
    }, 2000);
  };

  const testPhrases = [
    "can i have some water?",
    "I'm in pain",
    "can you put the back of the bed down a little bit?",
    "my feet are cold",
    "can you call my wife please?",
    "I feel cold",
    "I feel hot"
  ];

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
      {/* Header with SMHS Branding */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>SMHS Lipreader</Text>
        <View style={styles.headerLogo}>
          <Icon name="healing" size={24} color="white" />
        </View>
      </View>

      {/* Patient Illustration Section */}
      <View style={styles.illustrationSection}>
        <PatientIllustration />
        <Text style={styles.illustrationText}>
          Speak clearly into your camera for TRUE lipreading analysis
        </Text>
        <Text style={styles.subtitleText}>
          ESPnet LRS3_V_WER19.1 Transformer • Zero Simulation
        </Text>
      </View>

      {/* Camera Placeholder */}
      <View style={styles.cameraPlaceholder}>
        <Icon name="videocam" size={64} color="#5a2b81" />
        <Text style={styles.cameraText}>Camera will be here</Text>
        <Text style={styles.cameraSubtext}>Working on camera integration...</Text>
      </View>

      {/* Test Button */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={[styles.recordButton, isProcessing && styles.recordButtonDisabled]}
          onPress={handleTestRecording}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <Text style={styles.recordButtonText}>Processing...</Text>
          ) : (
            <Text style={styles.recordButtonText}>Test TRUE Lipreading</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Medical Phrases */}
      <View style={styles.phrasesSection}>
        <Text style={styles.phrasesTitle}>Supported Medical Phrases:</Text>
        {testPhrases.map((phrase, index) => (
          <Text key={index} style={styles.phraseText}>
            {index + 1}. {phrase}
          </Text>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#5a2b81',
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 20,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerLogo: {
    width: 24,
    height: 24,
  },
  illustrationSection: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
  },
  patientContainer: {
    alignItems: 'center',
    marginBottom: 15,
  },
  illustrationText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#333',
    marginBottom: 8,
    fontWeight: '500',
  },
  subtitleText: {
    fontSize: 12,
    textAlign: 'center',
    color: '#5a2b81',
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  cameraPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    margin: 20,
    padding: 40,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#5a2b81',
    borderStyle: 'dashed',
  },
  cameraText: {
    fontSize: 16,
    color: '#5a2b81',
    fontWeight: '600',
    marginTop: 10,
  },
  cameraSubtext: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  controlsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  recordButton: {
    backgroundColor: '#5a2b81',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
  },
  recordButtonDisabled: {
    backgroundColor: '#ccc',
  },
  recordButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  phrasesSection: {
    padding: 20,
    backgroundColor: '#ffffff',
    margin: 20,
    borderRadius: 10,
  },
  phrasesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  phraseText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
    paddingLeft: 10,
  },
});

export default MainInterfaceSimple;
