import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';

const { width, height } = Dimensions.get('window');

interface WelcomeScreenProps {
  navigation: any;
}

const WelcomeScreenMinimal: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const handleEnterApp = () => {
    navigation.navigate('MainInterface');
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoEmoji}>🏥</Text>
          <Text style={styles.logoText}>SMHS</Text>
        </View>
        <Text style={styles.title}>SMHS Lipreader</Text>
        <Text style={styles.subtitle}>TRUE AI Lipreading for Medical Communication</Text>
      </View>

      {/* Hero Section */}
      <View style={styles.heroSection}>
        <View style={styles.illustrationContainer}>
          <Text style={styles.heroEmoji}>👨‍⚕️</Text>
          <Text style={styles.phoneEmoji}>📱</Text>
        </View>
        <Text style={styles.heroTitle}>Communicate Without Speaking</Text>
        <Text style={styles.heroDescription}>
          Advanced lipreading technology designed specifically for medical environments. 
          Helps patients communicate essential needs when they cannot speak.
        </Text>
      </View>

      {/* Features Section */}
      <View style={styles.featuresSection}>
        <Text style={styles.sectionTitle}>Key Features</Text>
        
        <View style={styles.featureItem}>
          <Text style={styles.featureEmoji}>🤖</Text>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>TRUE AI Lipreading</Text>
            <Text style={styles.featureDescription}>
              ESPnet LRS3_V_WER19.1 Transformer - Zero simulation, real analysis
            </Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <Text style={styles.featureEmoji}>🏥</Text>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Medical Focus</Text>
            <Text style={styles.featureDescription}>
              Trained on 7 essential medical phrases for patient communication
            </Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <Text style={styles.featureEmoji}>📱</Text>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Mobile Ready</Text>
            <Text style={styles.featureDescription}>
              Works directly on your phone with real-time video processing
            </Text>
          </View>
        </View>

        <View style={styles.featureItem}>
          <Text style={styles.featureEmoji}>🔒</Text>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Privacy First</Text>
            <Text style={styles.featureDescription}>
              All processing happens locally - your data stays private
            </Text>
          </View>
        </View>
      </View>

      {/* Medical Phrases Preview */}
      <View style={styles.phrasesSection}>
        <Text style={styles.sectionTitle}>Supported Medical Phrases</Text>
        <View style={styles.phrasesList}>
          <Text style={styles.phraseItem}>• "can i have some water?"</Text>
          <Text style={styles.phraseItem}>• "I'm in pain"</Text>
          <Text style={styles.phraseItem}>• "can you put the back of the bed down a little bit?"</Text>
          <Text style={styles.phraseItem}>• "my feet are cold"</Text>
          <Text style={styles.phraseItem}>• "can you call my wife please?"</Text>
          <Text style={styles.phraseItem}>• "I feel cold"</Text>
          <Text style={styles.phraseItem}>• "I feel hot"</Text>
        </View>
      </View>

      {/* CTA Button */}
      <View style={styles.ctaSection}>
        <TouchableOpacity style={styles.ctaButton} onPress={handleEnterApp}>
          <Text style={styles.ctaButtonText}>🎤 Start Lipreading</Text>
        </TouchableOpacity>
        
        <Text style={styles.disclaimerText}>
          This app is designed to assist medical communication. 
          Always consult healthcare professionals for medical decisions.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    flexGrow: 1,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
    backgroundColor: '#5a2b81',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  logoEmoji: {
    fontSize: 48,
    marginBottom: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    letterSpacing: 2,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  heroSection: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
    backgroundColor: 'white',
  },
  illustrationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  heroEmoji: {
    fontSize: 80,
    marginRight: 10,
  },
  phoneEmoji: {
    fontSize: 60,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 15,
  },
  heroDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.85,
  },
  featuresSection: {
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
  },
  featureEmoji: {
    fontSize: 24,
    marginRight: 15,
    marginTop: 2,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 5,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  phrasesSection: {
    paddingVertical: 30,
    paddingHorizontal: 20,
    backgroundColor: 'white',
  },
  phrasesList: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#5a2b81',
  },
  phraseItem: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
    lineHeight: 20,
  },
  ctaSection: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  ctaButton: {
    backgroundColor: '#5a2b81',
    paddingVertical: 18,
    paddingHorizontal: 40,
    borderRadius: 30,
    marginBottom: 20,
    minWidth: width * 0.7,
    alignItems: 'center',
  },
  ctaButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  disclaimerText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 18,
    maxWidth: width * 0.8,
  },
});

export default WelcomeScreenMinimal;
