import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const { width, height } = Dimensions.get('window');

interface WelcomeScreenProps {
  navigation: any;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const handleEnterApp = () => {
    navigation.navigate('MainInterface');
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Icon name="healing" size={48} color="#007AFF" />
        </View>
        <Text style={styles.title}>ICU Lipreading Assistant</Text>
        <Text style={styles.subtitle}>Helping you communicate when you can't speak</Text>
      </View>

      {/* Patient Illustration */}
      <View style={styles.illustrationContainer}>
        <View style={styles.patientIllustration}>
          <Icon name="face" size={120} color="#007AFF" />
          <View style={styles.speechBubble}>
            <Text style={styles.speechText}>I need water</Text>
          </View>
        </View>
      </View>

      {/* Features */}
      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>How it works:</Text>
        
        <View style={styles.feature}>
          <View style={styles.featureIcon}>
            <Icon name="videocam" size={24} color="#007AFF" />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>1. Position Your Face</Text>
            <Text style={styles.featureDescription}>
              Place your face in the oval guide on the camera screen
            </Text>
          </View>
        </View>

        <View style={styles.feature}>
          <View style={styles.featureIcon}>
            <Icon name="record-voice-over" size={24} color="#007AFF" />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>2. Mouth Your Words</Text>
            <Text style={styles.featureDescription}>
              Speak clearly with your lips while recording (no sound needed)
            </Text>
          </View>
        </View>

        <View style={styles.feature}>
          <View style={styles.featureIcon}>
            <Icon name="text-fields" size={24} color="#007AFF" />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>3. Get Your Message</Text>
            <Text style={styles.featureDescription}>
              The app will convert your lip movements to text
            </Text>
          </View>
        </View>
      </View>

      {/* Common Phrases */}
      <View style={styles.phrasesContainer}>
        <Text style={styles.phrasesTitle}>Common ICU phrases we recognize:</Text>
        <View style={styles.phrasesList}>
          <Text style={styles.phrase}>• "I need water"</Text>
          <Text style={styles.phrase}>• "I'm in pain"</Text>
          <Text style={styles.phrase}>• "I need suctioning"</Text>
          <Text style={styles.phrase}>• "Call the nurse"</Text>
          <Text style={styles.phrase}>• "Help me please"</Text>
          <Text style={styles.phrase}>• "Thank you"</Text>
        </View>
      </View>

      {/* Tips */}
      <View style={styles.tipsContainer}>
        <Text style={styles.tipsTitle}>Tips for best results:</Text>
        <Text style={styles.tip}>✓ Ensure good lighting on your face</Text>
        <Text style={styles.tip}>✓ Keep your face centered in the oval</Text>
        <Text style={styles.tip}>✓ Speak slowly and clearly</Text>
        <Text style={styles.tip}>✓ Use simple, direct phrases</Text>
      </View>

      {/* Start Button */}
      <TouchableOpacity style={styles.startButton} onPress={handleEnterApp}>
        <Text style={styles.startButtonText}>Start Communication</Text>
        <Icon name="arrow-forward" size={24} color="white" style={styles.startButtonIcon} />
      </TouchableOpacity>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Designed for ICU patients with tracheostomy
        </Text>
        <Text style={styles.footerSubtext}>
          This app helps you communicate with your care team
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    paddingBottom: 32,
  },
  header: {
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 24,
    paddingBottom: 32,
    backgroundColor: 'white',
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#e3f2fd',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  illustrationContainer: {
    alignItems: 'center',
    paddingVertical: 32,
    backgroundColor: 'white',
    marginTop: 1,
  },
  patientIllustration: {
    alignItems: 'center',
    position: 'relative',
  },
  speechBubble: {
    position: 'absolute',
    top: -20,
    right: -60,
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderBottomLeftRadius: 4,
  },
  speechText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  featuresContainer: {
    backgroundColor: 'white',
    marginTop: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 24,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#e3f2fd',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  phrasesContainer: {
    backgroundColor: 'white',
    marginTop: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  phrasesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  phrasesList: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
  },
  phrase: {
    fontSize: 14,
    color: '#495057',
    marginBottom: 8,
    lineHeight: 20,
  },
  tipsContainer: {
    backgroundColor: 'white',
    marginTop: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  tipsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  tip: {
    fontSize: 14,
    color: '#495057',
    marginBottom: 8,
    lineHeight: 20,
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    marginHorizontal: 24,
    marginTop: 32,
    paddingVertical: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginRight: 8,
  },
  startButtonIcon: {
    marginLeft: 4,
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
});

export default WelcomeScreen;
