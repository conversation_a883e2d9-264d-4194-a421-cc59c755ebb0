import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const { width } = Dimensions.get('window');

interface ErrorModalProps {
  message: string;
  onClose: () => void;
}

const ErrorModal: React.FC<ErrorModalProps> = ({ message, onClose }) => {
  const getErrorType = (errorMessage: string) => {
    if (errorMessage.toLowerCase().includes('camera')) {
      return 'camera';
    } else if (errorMessage.toLowerCase().includes('network') || errorMessage.toLowerCase().includes('connection')) {
      return 'network';
    } else if (errorMessage.toLowerCase().includes('processing')) {
      return 'processing';
    }
    return 'general';
  };

  const getErrorIcon = (type: string) => {
    switch (type) {
      case 'camera':
        return 'videocam-off';
      case 'network':
        return 'wifi-off';
      case 'processing':
        return 'error-outline';
      default:
        return 'error';
    }
  };

  const getErrorTitle = (type: string) => {
    switch (type) {
      case 'camera':
        return 'Camera Error';
      case 'network':
        return 'Connection Error';
      case 'processing':
        return 'Processing Error';
      default:
        return 'Error';
    }
  };

  const getSuggestions = (type: string) => {
    switch (type) {
      case 'camera':
        return [
          'Check camera permissions in Settings',
          'Ensure camera is not being used by another app',
          'Try restarting the app',
        ];
      case 'network':
        return [
          'Check your internet connection',
          'Try connecting to a different network',
          'Contact your IT support team',
        ];
      case 'processing':
        return [
          'Try recording again with better lighting',
          'Ensure your face is clearly visible',
          'Speak more slowly and clearly',
        ];
      default:
        return [
          'Try closing and reopening the app',
          'Contact technical support if the problem persists',
        ];
    }
  };

  const errorType = getErrorType(message);
  const errorIcon = getErrorIcon(errorType);
  const errorTitle = getErrorTitle(errorType);
  const suggestions = getSuggestions(errorType);

  return (
    <Modal
      visible={true}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          {/* Error Icon */}
          <View style={styles.iconContainer}>
            <Icon name={errorIcon} size={48} color="#dc3545" />
          </View>

          {/* Error Title */}
          <Text style={styles.title}>{errorTitle}</Text>

          {/* Error Message */}
          <Text style={styles.message}>{message}</Text>

          {/* Suggestions */}
          <View style={styles.suggestionsContainer}>
            <Text style={styles.suggestionsTitle}>Try these solutions:</Text>
            {suggestions.map((suggestion, index) => (
              <View key={index} style={styles.suggestionItem}>
                <Text style={styles.suggestionBullet}>•</Text>
                <Text style={styles.suggestionText}>{suggestion}</Text>
              </View>
            ))}
          </View>

          {/* Actions */}
          <View style={styles.actions}>
            <TouchableOpacity style={styles.button} onPress={onClose}>
              <Text style={styles.buttonText}>OK</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  modal: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: width - 48,
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ffeaea',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  suggestionsContainer: {
    width: '100%',
    marginBottom: 24,
  },
  suggestionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  suggestionBullet: {
    fontSize: 16,
    color: '#007AFF',
    marginRight: 8,
    marginTop: 2,
  },
  suggestionText: {
    flex: 1,
    fontSize: 14,
    color: '#495057',
    lineHeight: 20,
  },
  actions: {
    width: '100%',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});

export default ErrorModal;
