import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';

interface ResultsModalProps {
  prediction: string;
  videoUrl: string | null;
  onClose: () => void;
  onRetry: () => void;
  onRecordAgain: () => void;
}

const ResultsModalMinimal: React.FC<ResultsModalProps> = ({
  prediction,
  videoUrl,
  onClose,
  onRetry,
  onRecordAgain,
}) => {
  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>SMHS Lipreader Results</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Results Section */}
          <View style={styles.resultsSection}>
            <Text style={styles.sectionTitle}>🎯 Prediction Result</Text>
            <View style={styles.predictionContainer}>
              <Text style={styles.predictionText}>"{prediction}"</Text>
            </View>
            
            <Text style={styles.confidenceText}>
              ✅ TRUE ESPnet LRS3_V_WER19.1 Analysis Complete
            </Text>
          </View>

          {/* Video Section */}
          <View style={styles.videoSection}>
            <Text style={styles.sectionTitle}>📹 Recorded Video</Text>
            <View style={styles.videoPlaceholder}>
              <Text style={styles.videoEmoji}>🎬</Text>
              <Text style={styles.videoText}>Video recorded successfully</Text>
              <Text style={styles.videoSubtext}>
                {videoUrl ? "Video available for playback" : "No video URL"}
              </Text>
            </View>
          </View>

          {/* Medical Context */}
          <View style={styles.contextSection}>
            <Text style={styles.sectionTitle}>🏥 Medical Context</Text>
            <Text style={styles.contextText}>
              This prediction was generated using TRUE lipreading analysis 
              specifically trained for medical communication scenarios.
            </Text>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity style={styles.primaryButton} onPress={onRecordAgain}>
              <Text style={styles.primaryButtonText}>🎤 Record Again</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.secondaryButton} onPress={onRetry}>
              <Text style={styles.secondaryButtonText}>🔄 Retry Analysis</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.tertiaryButton} onPress={onClose}>
              <Text style={styles.tertiaryButtonText}>✅ Done</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#5a2b81',
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 20,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultsSection: {
    padding: 20,
    backgroundColor: '#ffffff',
    margin: 20,
    borderRadius: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  predictionContainer: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#5a2b81',
    marginBottom: 10,
  },
  predictionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2d5a2d',
    textAlign: 'center',
  },
  confidenceText: {
    fontSize: 12,
    color: '#5a2b81',
    textAlign: 'center',
    fontWeight: '500',
  },
  videoSection: {
    padding: 20,
    backgroundColor: '#ffffff',
    margin: 20,
    marginTop: 0,
    borderRadius: 10,
  },
  videoPlaceholder: {
    alignItems: 'center',
    padding: 30,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  videoEmoji: {
    fontSize: 48,
    marginBottom: 10,
  },
  videoText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  videoSubtext: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  contextSection: {
    padding: 20,
    backgroundColor: '#ffffff',
    margin: 20,
    marginTop: 0,
    borderRadius: 10,
  },
  contextText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actionsContainer: {
    padding: 20,
    gap: 10,
  },
  primaryButton: {
    backgroundColor: '#5a2b81',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#ffffff',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#5a2b81',
  },
  secondaryButtonText: {
    color: '#5a2b81',
    fontSize: 16,
    fontWeight: '600',
  },
  tertiaryButton: {
    backgroundColor: '#f8f9fa',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  tertiaryButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ResultsModalMinimal;
