/**
 * ICU-specific accessibility features for patients with limited mobility
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Slider,
  Alert,
  AccessibilityInfo,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const { width, height } = Dimensions.get('window');

interface ICUAccessibilityProps {
  onSettingsChange: (settings: AccessibilitySettings) => void;
}

export interface AccessibilitySettings {
  highContrast: boolean;
  largeText: boolean;
  textSize: number;
  voiceOver: boolean;
  simplifiedUI: boolean;
  autoSpeak: boolean;
  touchSensitivity: number;
  buttonSize: 'small' | 'medium' | 'large' | 'extra-large';
}

const DEFAULT_SETTINGS: AccessibilitySettings = {
  highContrast: false,
  largeText: false,
  textSize: 16,
  voiceOver: false,
  simplifiedUI: false,
  autoSpeak: true,
  touchSensitivity: 1.0,
  buttonSize: 'large',
};

const ICUAccessibilityFeatures: React.FC<ICUAccessibilityProps> = ({
  onSettingsChange,
}) => {
  const [settings, setSettings] = useState<AccessibilitySettings>(DEFAULT_SETTINGS);
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] = useState(false);

  useEffect(() => {
    // Check if screen reader is enabled
    AccessibilityInfo.isScreenReaderEnabled().then(setIsScreenReaderEnabled);
    
    // Listen for screen reader changes
    const subscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      setIsScreenReaderEnabled
    );

    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    onSettingsChange(settings);
  }, [settings, onSettingsChange]);

  const updateSetting = <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const getButtonSizeValue = (size: AccessibilitySettings['buttonSize']): number => {
    switch (size) {
      case 'small': return 40;
      case 'medium': return 60;
      case 'large': return 80;
      case 'extra-large': return 100;
      default: return 80;
    }
  };

  const resetToDefaults = () => {
    Alert.alert(
      'Reset Settings',
      'Reset all accessibility settings to defaults?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          onPress: () => setSettings(DEFAULT_SETTINGS),
        },
      ]
    );
  };

  return (
    <View style={[styles.container, settings.highContrast && styles.highContrastContainer]}>
      <Text style={[
        styles.title,
        settings.highContrast && styles.highContrastText,
        { fontSize: settings.textSize + 4 }
      ]}>
        Accessibility Settings
      </Text>

      {/* Visual Settings */}
      <View style={styles.section}>
        <Text style={[
          styles.sectionTitle,
          settings.highContrast && styles.highContrastText,
          { fontSize: settings.textSize + 2 }
        ]}>
          Visual Settings
        </Text>

        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize }
            ]}>
              High Contrast Mode
            </Text>
            <Text style={[
              styles.settingDescription,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize - 2 }
            ]}>
              Improves visibility in bright ICU lighting
            </Text>
          </View>
          <Switch
            value={settings.highContrast}
            onValueChange={(value) => updateSetting('highContrast', value)}
            trackColor={{ false: '#767577', true: '#007AFF' }}
            thumbColor={settings.highContrast ? '#ffffff' : '#f4f3f4'}
          />
        </View>

        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize }
            ]}>
              Text Size: {settings.textSize}px
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={12}
            maximumValue={24}
            value={settings.textSize}
            onValueChange={(value) => updateSetting('textSize', Math.round(value))}
            minimumTrackTintColor="#007AFF"
            maximumTrackTintColor="#d3d3d3"
            thumbStyle={{ backgroundColor: '#007AFF' }}
          />
        </View>

        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize }
            ]}>
              Simplified Interface
            </Text>
            <Text style={[
              styles.settingDescription,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize - 2 }
            ]}>
              Reduces visual complexity
            </Text>
          </View>
          <Switch
            value={settings.simplifiedUI}
            onValueChange={(value) => updateSetting('simplifiedUI', value)}
            trackColor={{ false: '#767577', true: '#007AFF' }}
          />
        </View>
      </View>

      {/* Touch Settings */}
      <View style={styles.section}>
        <Text style={[
          styles.sectionTitle,
          settings.highContrast && styles.highContrastText,
          { fontSize: settings.textSize + 2 }
        ]}>
          Touch & Interaction
        </Text>

        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize }
            ]}>
              Button Size: {settings.buttonSize}
            </Text>
          </View>
          <View style={styles.buttonSizeOptions}>
            {(['small', 'medium', 'large', 'extra-large'] as const).map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.sizeOption,
                  {
                    width: getButtonSizeValue(size) / 2,
                    height: getButtonSizeValue(size) / 2,
                  },
                  settings.buttonSize === size && styles.selectedSizeOption,
                  settings.highContrast && styles.highContrastButton,
                ]}
                onPress={() => updateSetting('buttonSize', size)}
              >
                <Text style={[
                  styles.sizeOptionText,
                  settings.highContrast && styles.highContrastText,
                  { fontSize: Math.min(settings.textSize - 4, 10) }
                ]}>
                  {size[0].toUpperCase()}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize }
            ]}>
              Touch Sensitivity: {settings.touchSensitivity.toFixed(1)}x
            </Text>
          </View>
          <Slider
            style={styles.slider}
            minimumValue={0.5}
            maximumValue={2.0}
            value={settings.touchSensitivity}
            onValueChange={(value) => updateSetting('touchSensitivity', Math.round(value * 10) / 10)}
            minimumTrackTintColor="#007AFF"
            maximumTrackTintColor="#d3d3d3"
          />
        </View>
      </View>

      {/* Audio Settings */}
      <View style={styles.section}>
        <Text style={[
          styles.sectionTitle,
          settings.highContrast && styles.highContrastText,
          { fontSize: settings.textSize + 2 }
        ]}>
          Audio & Voice
        </Text>

        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Text style={[
              styles.settingLabel,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize }
            ]}>
              Auto-Speak Results
            </Text>
            <Text style={[
              styles.settingDescription,
              settings.highContrast && styles.highContrastText,
              { fontSize: settings.textSize - 2 }
            ]}>
              Automatically read recognized text aloud
            </Text>
          </View>
          <Switch
            value={settings.autoSpeak}
            onValueChange={(value) => updateSetting('autoSpeak', value)}
            trackColor={{ false: '#767577', true: '#007AFF' }}
          />
        </View>

        {isScreenReaderEnabled && (
          <View style={styles.screenReaderNotice}>
            <Icon name="accessibility" size={20} color="#007AFF" />
            <Text style={[
              styles.screenReaderText,
              { fontSize: settings.textSize - 2 }
            ]}>
              Screen reader detected - optimized for voice navigation
            </Text>
          </View>
        )}
      </View>

      {/* Reset Button */}
      <TouchableOpacity
        style={[
          styles.resetButton,
          settings.highContrast && styles.highContrastButton,
          { height: getButtonSizeValue(settings.buttonSize) / 2 }
        ]}
        onPress={resetToDefaults}
        accessibilityLabel="Reset all settings to defaults"
        accessibilityRole="button"
      >
        <Icon 
          name="refresh" 
          size={20} 
          color={settings.highContrast ? '#000' : '#666'} 
        />
        <Text style={[
          styles.resetButtonText,
          settings.highContrast && styles.highContrastText,
          { fontSize: settings.textSize }
        ]}>
          Reset to Defaults
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  highContrastContainer: {
    backgroundColor: '#000000',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 24,
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  slider: {
    width: 120,
    height: 40,
  },
  buttonSizeOptions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sizeOption: {
    borderRadius: 8,
    backgroundColor: '#f1f3f4',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedSizeOption: {
    borderColor: '#007AFF',
    backgroundColor: '#e3f2fd',
  },
  sizeOptionText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#666',
  },
  screenReaderNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  screenReaderText: {
    marginLeft: 8,
    flex: 1,
    color: '#1565c0',
    fontSize: 14,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f1f3f4',
    borderRadius: 8,
    paddingHorizontal: 16,
    marginTop: 16,
  },
  resetButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  highContrastText: {
    color: '#ffffff',
  },
  highContrastButton: {
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
});

export default ICUAccessibilityFeatures;
