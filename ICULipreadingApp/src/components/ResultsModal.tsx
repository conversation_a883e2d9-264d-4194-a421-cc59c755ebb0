import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
  Share,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Video from 'react-native-video';

const { width, height } = Dimensions.get('window');

interface ResultsModalProps {
  prediction: string;
  videoUrl: string | null;
  onClose: () => void;
  onRetry: () => void;
  onRecordAgain: () => void;
}

const ResultsModal: React.FC<ResultsModalProps> = ({
  prediction,
  videoUrl,
  onClose,
  onRetry,
  onRecordAgain,
}) => {
  const [showVideo, setShowVideo] = useState(false);

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Patient communication: "${prediction}"`,
        title: 'ICU Lipreading Result',
      });
    } catch (error) {
      console.error('Share error:', error);
      Alert.alert('Error', 'Failed to share message');
    }
  };

  const handleCopyToClipboard = () => {
    // In a real app, you'd use Clipboard from @react-native-clipboard/clipboard
    Alert.alert('Copied', 'Message copied to clipboard');
  };

  const getMessageType = (text: string) => {
    const urgentKeywords = ['pain', 'help', 'emergency', 'can\'t breathe', 'hurt'];
    const needsKeywords = ['need', 'want', 'water', 'medication', 'suctioning'];
    
    if (urgentKeywords.some(keyword => text.toLowerCase().includes(keyword))) {
      return 'urgent';
    } else if (needsKeywords.some(keyword => text.toLowerCase().includes(keyword))) {
      return 'need';
    }
    return 'general';
  };

  const messageType = getMessageType(prediction);

  const getMessageIcon = () => {
    switch (messageType) {
      case 'urgent':
        return { name: 'warning', color: '#dc3545' };
      case 'need':
        return { name: 'info', color: '#007AFF' };
      default:
        return { name: 'chat', color: '#28a745' };
    }
  };

  const getMessageStyle = () => {
    switch (messageType) {
      case 'urgent':
        return styles.urgentMessage;
      case 'need':
        return styles.needMessage;
      default:
        return styles.generalMessage;
    }
  };

  const icon = getMessageIcon();

  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Icon name="close" size={24} color="#666" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Your Message</Text>
          <TouchableOpacity onPress={handleShare} style={styles.shareButton}>
            <Icon name="share" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Message Result */}
          <View style={[styles.messageContainer, getMessageStyle()]}>
            <View style={styles.messageHeader}>
              <Icon name={icon.name} size={32} color={icon.color} />
              <Text style={styles.messageLabel}>
                {messageType === 'urgent' ? 'Urgent Request' : 
                 messageType === 'need' ? 'Patient Need' : 'Message'}
              </Text>
            </View>
            
            <Text style={styles.messageText}>{prediction}</Text>
            
            <TouchableOpacity 
              style={styles.copyButton} 
              onPress={handleCopyToClipboard}
            >
              <Icon name="content-copy" size={16} color="#666" />
              <Text style={styles.copyButtonText}>Copy message</Text>
            </TouchableOpacity>
          </View>

          {/* Video Playback */}
          {videoUrl && (
            <View style={styles.videoContainer}>
              <TouchableOpacity 
                style={styles.videoToggle}
                onPress={() => setShowVideo(!showVideo)}
              >
                <Icon 
                  name={showVideo ? 'videocam-off' : 'videocam'} 
                  size={20} 
                  color="#007AFF" 
                />
                <Text style={styles.videoToggleText}>
                  {showVideo ? 'Hide' : 'Show'} Recording
                </Text>
              </TouchableOpacity>
              
              {showVideo && (
                <View style={styles.videoPlayer}>
                  <Video
                    source={{ uri: videoUrl }}
                    style={styles.video}
                    controls={true}
                    resizeMode="contain"
                    paused={false}
                  />
                </View>
              )}
            </View>
          )}

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <Text style={styles.quickActionsTitle}>Quick Actions</Text>
            
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="volume-up" size={20} color="#007AFF" />
              <Text style={styles.actionButtonText}>Read Aloud</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="text-increase" size={20} color="#007AFF" />
              <Text style={styles.actionButtonText}>Large Text View</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="translate" size={20} color="#007AFF" />
              <Text style={styles.actionButtonText}>Translate</Text>
            </TouchableOpacity>
          </View>

          {/* Confidence & Info */}
          <View style={styles.infoContainer}>
            <Text style={styles.infoTitle}>Recognition Info</Text>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Confidence:</Text>
              <Text style={styles.infoValue}>85%</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Processing time:</Text>
              <Text style={styles.infoValue}>1.2 seconds</Text>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Actions */}
        <View style={styles.bottomActions}>
          <TouchableOpacity 
            style={[styles.bottomButton, styles.retryButton]} 
            onPress={onRetry}
          >
            <Icon name="refresh" size={20} color="#666" />
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.bottomButton, styles.recordButton]} 
            onPress={onRecordAgain}
          >
            <Icon name="videocam" size={20} color="white" />
            <Text style={styles.recordButtonText}>New Recording</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  shareButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messageContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  urgentMessage: {
    borderLeftWidth: 4,
    borderLeftColor: '#dc3545',
  },
  needMessage: {
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  generalMessage: {
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  messageLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#495057',
    marginLeft: 12,
  },
  messageText: {
    fontSize: 24,
    fontWeight: '500',
    color: '#1a1a1a',
    lineHeight: 32,
    marginBottom: 16,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  copyButtonText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  videoContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  videoToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  videoToggleText: {
    fontSize: 16,
    color: '#007AFF',
    marginLeft: 8,
  },
  videoPlayer: {
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
  },
  video: {
    flex: 1,
  },
  quickActions: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  quickActionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  actionButtonText: {
    fontSize: 16,
    color: '#495057',
    marginLeft: 12,
  },
  infoContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1a1a1a',
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  bottomButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 8,
  },
  retryButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  retryButtonText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
  },
  recordButton: {
    backgroundColor: '#007AFF',
  },
  recordButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: 8,
  },
});

export default ResultsModal;
