/**
 * Real-time Communication Service
 * Handles efficient client-server communication with caching and offline support
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';
import { LipreadingService, LipreadingResult } from './LipreadingService';

export interface CachedResult {
  id: string;
  videoUri: string;
  result: LipreadingResult;
  timestamp: number;
  synced: boolean;
}

export interface QueuedRequest {
  id: string;
  videoUri: string;
  timestamp: number;
  retryCount: number;
}

export class RealtimeCommunicationService {
  private lipreadingService: LipreadingService;
  private isOnline: boolean = true;
  private processingQueue: QueuedRequest[] = [];
  private cache: Map<string, CachedResult> = new Map();
  private maxCacheSize: number = 100;
  private maxRetries: number = 3;
  private retryDelay: number = 2000; // 2 seconds

  constructor(lipreadingService: LipreadingService) {
    this.lipreadingService = lipreadingService;
    this.initializeNetworkMonitoring();
    this.loadCacheFromStorage();
  }

  /**
   * Initialize network monitoring
   */
  private initializeNetworkMonitoring(): void {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      // If we just came back online, process queued requests
      if (wasOffline && this.isOnline) {
        this.processQueuedRequests();
      }
    });
  }

  /**
   * Load cached results from AsyncStorage
   */
  private async loadCacheFromStorage(): Promise<void> {
    try {
      const cachedData = await AsyncStorage.getItem('lipreading_cache');
      if (cachedData) {
        const parsedCache: CachedResult[] = JSON.parse(cachedData);
        parsedCache.forEach(item => {
          this.cache.set(item.id, item);
        });
      }

      const queuedData = await AsyncStorage.getItem('processing_queue');
      if (queuedData) {
        this.processingQueue = JSON.parse(queuedData);
      }
    } catch (error) {
      console.error('Failed to load cache from storage:', error);
    }
  }

  /**
   * Save cache to AsyncStorage
   */
  private async saveCacheToStorage(): Promise<void> {
    try {
      const cacheArray = Array.from(this.cache.values());
      await AsyncStorage.setItem('lipreading_cache', JSON.stringify(cacheArray));
      await AsyncStorage.setItem('processing_queue', JSON.stringify(this.processingQueue));
    } catch (error) {
      console.error('Failed to save cache to storage:', error);
    }
  }

  /**
   * Generate unique ID for requests
   */
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if result is cached
   */
  private getCachedResult(videoUri: string): CachedResult | null {
    for (const cached of this.cache.values()) {
      if (cached.videoUri === videoUri) {
        return cached;
      }
    }
    return null;
  }

  /**
   * Add result to cache
   */
  private addToCache(id: string, videoUri: string, result: LipreadingResult, synced: boolean = true): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = Array.from(this.cache.keys())[0];
      this.cache.delete(oldestKey);
    }

    const cachedResult: CachedResult = {
      id,
      videoUri,
      result,
      timestamp: Date.now(),
      synced,
    };

    this.cache.set(id, cachedResult);
    this.saveCacheToStorage();
  }

  /**
   * Add request to processing queue
   */
  private addToQueue(videoUri: string): string {
    const id = this.generateId();
    const queuedRequest: QueuedRequest = {
      id,
      videoUri,
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.processingQueue.push(queuedRequest);
    this.saveCacheToStorage();
    return id;
  }

  /**
   * Process queued requests when back online
   */
  private async processQueuedRequests(): Promise<void> {
    if (!this.isOnline || this.processingQueue.length === 0) {
      return;
    }

    const queue = [...this.processingQueue];
    this.processingQueue = [];

    for (const request of queue) {
      try {
        const result = await this.lipreadingService.processVideo(request.videoUri);
        this.addToCache(request.id, request.videoUri, result, true);
        
        // Notify listeners about successful processing
        this.notifyProcessingComplete(request.id, result);
      } catch (error) {
        request.retryCount++;
        
        if (request.retryCount < this.maxRetries) {
          // Add back to queue for retry
          this.processingQueue.push(request);
          
          // Exponential backoff
          setTimeout(() => {
            this.processQueuedRequests();
          }, this.retryDelay * Math.pow(2, request.retryCount - 1));
        } else {
          console.error(`Failed to process queued request ${request.id} after ${this.maxRetries} retries`);
          this.notifyProcessingFailed(request.id, error.message);
        }
      }
    }

    this.saveCacheToStorage();
  }

  /**
   * Process video with real-time optimizations
   */
  async processVideoRealtime(videoUri: string): Promise<{
    id: string;
    result?: LipreadingResult;
    isFromCache: boolean;
    isQueued: boolean;
  }> {
    const id = this.generateId();

    // Check cache first
    const cachedResult = this.getCachedResult(videoUri);
    if (cachedResult) {
      return {
        id: cachedResult.id,
        result: cachedResult.result,
        isFromCache: true,
        isQueued: false,
      };
    }

    // If online, try to process immediately
    if (this.isOnline) {
      try {
        // Check backend health first
        const isHealthy = await this.lipreadingService.healthCheck();
        
        if (isHealthy) {
          const result = await this.lipreadingService.processVideo(videoUri);
          this.addToCache(id, videoUri, result, true);
          
          return {
            id,
            result,
            isFromCache: false,
            isQueued: false,
          };
        } else {
          // Backend unhealthy, fall back to simulation
          const result = await this.lipreadingService.simulateProcessing();
          this.addToCache(id, videoUri, result, false);
          
          return {
            id,
            result,
            isFromCache: false,
            isQueued: false,
          };
        }
      } catch (error) {
        console.error('Real-time processing failed:', error);
        
        // Fall back to offline processing
        const result = await this.lipreadingService.simulateProcessing();
        this.addToCache(id, videoUri, result, false);
        
        // Queue for later processing
        this.addToQueue(videoUri);
        
        return {
          id,
          result,
          isFromCache: false,
          isQueued: true,
        };
      }
    } else {
      // Offline mode - use simulation and queue for later
      const result = await this.lipreadingService.simulateProcessing();
      this.addToCache(id, videoUri, result, false);
      
      const queueId = this.addToQueue(videoUri);
      
      return {
        id: queueId,
        result,
        isFromCache: false,
        isQueued: true,
      };
    }
  }

  /**
   * Get processing status
   */
  getProcessingStatus(): {
    isOnline: boolean;
    queueLength: number;
    cacheSize: number;
    unsyncedCount: number;
  } {
    const unsyncedCount = Array.from(this.cache.values())
      .filter(item => !item.synced).length;

    return {
      isOnline: this.isOnline,
      queueLength: this.processingQueue.length,
      cacheSize: this.cache.size,
      unsyncedCount,
    };
  }

  /**
   * Clear cache and queue
   */
  async clearCache(): Promise<void> {
    this.cache.clear();
    this.processingQueue = [];
    await AsyncStorage.removeItem('lipreading_cache');
    await AsyncStorage.removeItem('processing_queue');
  }

  /**
   * Get cached results
   */
  getCachedResults(): CachedResult[] {
    return Array.from(this.cache.values())
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Retry failed requests
   */
  async retryFailedRequests(): Promise<void> {
    if (this.isOnline) {
      await this.processQueuedRequests();
    }
  }

  /**
   * Event listeners for processing completion
   */
  private processingCompleteListeners: Array<(id: string, result: LipreadingResult) => void> = [];
  private processingFailedListeners: Array<(id: string, error: string) => void> = [];

  onProcessingComplete(callback: (id: string, result: LipreadingResult) => void): () => void {
    this.processingCompleteListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.processingCompleteListeners.indexOf(callback);
      if (index > -1) {
        this.processingCompleteListeners.splice(index, 1);
      }
    };
  }

  onProcessingFailed(callback: (id: string, error: string) => void): () => void {
    this.processingFailedListeners.push(callback);
    
    return () => {
      const index = this.processingFailedListeners.indexOf(callback);
      if (index > -1) {
        this.processingFailedListeners.splice(index, 1);
      }
    };
  }

  private notifyProcessingComplete(id: string, result: LipreadingResult): void {
    this.processingCompleteListeners.forEach(callback => {
      try {
        callback(id, result);
      } catch (error) {
        console.error('Error in processing complete callback:', error);
      }
    });
  }

  private notifyProcessingFailed(id: string, error: string): void {
    this.processingFailedListeners.forEach(callback => {
      try {
        callback(id, error);
      } catch (error) {
        console.error('Error in processing failed callback:', error);
      }
    });
  }
}

// Default service instance
export const realtimeCommunicationService = new RealtimeCommunicationService(
  new LipreadingService()
);
