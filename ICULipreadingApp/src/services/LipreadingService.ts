/**
 * Lipreading Service
 * Handles communication with the backend lipreading service
 */

export interface LipreadingResult {
  prediction: string;
  raw_output: string;
  confidence: number;
  timestamp: number;
}

export interface ProcessingError {
  error: string;
  code?: string;
}

export class LipreadingService {
  private baseUrl: string;
  private timeout: number;

  constructor(baseUrl: string = 'http://*************:5001', timeout: number = 30000) {
    this.baseUrl = baseUrl;
    this.timeout = timeout;
  }

  /**
   * Check if the backend service is healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        timeout: 5000,
      });
      
      if (response.ok) {
        const data = await response.json();
        return data.status === 'healthy';
      }
      return false;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Process a video file for lipreading
   */
  async processVideo(videoUri: string): Promise<LipreadingResult> {
    try {
      const formData = new FormData();
      formData.append('video', {
        uri: videoUri,
        type: 'video/mp4',
        name: 'recording.mp4',
      } as any);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(`${this.baseUrl}/process_video`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.status !== 'success') {
        throw new Error(result.error || 'Processing failed');
      }

      return {
        prediction: result.prediction,
        raw_output: result.raw_output,
        confidence: result.confidence,
        timestamp: result.timestamp,
      };

    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('Request timeout - processing took too long');
      }
      throw error;
    }
  }

  /**
   * Get list of medical phrases the system recognizes
   */
  async getMedicalPhrases(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/medical_phrases`);
      
      if (response.ok) {
        const data = await response.json();
        return data.phrases || [];
      }
      return [];
    } catch (error) {
      console.error('Failed to get medical phrases:', error);
      return [];
    }
  }

  /**
   * Load the VSR model (if not already loaded)
   */
  async loadModel(configPath?: string, detector?: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/load_model`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config_path: configPath,
          detector: detector || 'mediapipe',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        return data.status === 'success';
      }
      return false;
    } catch (error) {
      console.error('Failed to load model:', error);
      return false;
    }
  }

  /**
   * Simulate processing for development/testing
   */
  async simulateProcessing(): Promise<LipreadingResult> {
    const medicalPhrases = [
      "I need water",
      "I need suctioning",
      "I'm in pain",
      "I need my medication",
      "I feel nauseous",
      "I can't breathe",
      "I'm feeling dizzy",
      "Can you call the nurse",
      "Help me please",
      "Thank you",
      "I'm cold",
      "I'm hot",
      "Turn me over",
      "I need to use the bathroom"
    ];

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    const randomPhrase = medicalPhrases[Math.floor(Math.random() * medicalPhrases.length)];
    
    return {
      prediction: randomPhrase,
      raw_output: randomPhrase.toUpperCase(),
      confidence: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
      timestamp: Date.now(),
    };
  }
}

// Default service instance
export const lipreadingService = new LipreadingService();
