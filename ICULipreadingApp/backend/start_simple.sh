#!/bin/bash
# Simple startup script for ICU Lipreading Backend

set -e

echo "🚀 ICU Lipreading Backend - Simple Startup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the backend directory
if [ ! -f "simple_app.py" ]; then
    print_error "Please run this script from the backend directory"
    print_status "Usage: cd ICULipreadingApp/backend && ./start_simple.sh"
    exit 1
fi

# Check Python installation
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not installed"
    exit 1
fi

print_status "Python version: $(python3 --version)"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_status "Creating virtual environment..."
    python3 -m venv venv
    print_success "Virtual environment created"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install basic requirements
print_status "Installing basic requirements..."
pip install --quiet --upgrade pip

# Install required packages
pip install --quiet flask flask-cors torch torchvision torchaudio

print_success "Dependencies installed"

# Check model files
print_status "Checking model files..."
if [ -d "models/LRS3_V_WER19.1" ]; then
    if [ -f "models/LRS3_V_WER19.1/model.pth" ] && [ -f "models/LRS3_V_WER19.1/model.json" ]; then
        print_success "✅ LRS3 model files found"
        MODEL_STATUS="available"
    else
        print_warning "⚠️  LRS3 model files incomplete"
        MODEL_STATUS="incomplete"
    fi
else
    print_warning "⚠️  LRS3 model not found - will run in simulation mode"
    MODEL_STATUS="missing"
fi

# Create necessary directories
mkdir -p uploads
mkdir -p logs

print_status "Directory structure:"
echo "backend/"
echo "├── models/"
if [ "$MODEL_STATUS" = "available" ]; then
    echo "│   └── LRS3_V_WER19.1/ ✅"
else
    echo "│   └── LRS3_V_WER19.1/ ⚠️"
fi
echo "├── uploads/ (created)"
echo "├── logs/ (created)"
echo "└── simple_app.py ✅"

print_success "🎉 Setup complete!"

if [ "$MODEL_STATUS" = "available" ]; then
    print_status "🤖 Model files detected - will attempt to load LRS3 model"
elif [ "$MODEL_STATUS" = "incomplete" ]; then
    print_warning "🔄 Incomplete model files - will run in simulation mode"
else
    print_warning "🔄 No model files - will run in simulation mode"
fi

print_status "🌐 Starting server on http://localhost:5001"
print_status "📱 Make sure your mobile app points to this URL"
print_status ""
print_status "Available endpoints:"
print_status "  GET  /health           - Health check"
print_status "  POST /process_video    - Process video file"
print_status "  GET  /medical_phrases  - Get medical phrases"
print_status "  POST /simulate         - Simulate processing"
print_status "  GET  /model_info       - Model information"
print_status ""
print_status "Press Ctrl+C to stop the server"
print_status ""

# Start the server
python3 simple_app.py
