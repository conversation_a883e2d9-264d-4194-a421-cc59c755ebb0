#!/usr/bin/env python3
"""
ICU Lipreading Backend Service
Integrates Chaplin model with Auto-AVSR preprocessing for mobile app
"""

import os
import sys
import tempfile
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import uuid
import time

from flask import Flask, request, jsonify
from flask_cors import CORS
import torch
import cv2
import numpy as np
from werkzeug.utils import secure_filename

# Add the chaplin and auto_avsr modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'chaplin'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'auto_avsr'))

try:
    from pipelines.pipeline import InferencePipeline
    from data.data_module import AVSRDataLoader
    from transforms import TextTransform
    from ollama import chat
    from pydantic import BaseModel
except ImportError as e:
    logging.error(f"Failed to import required modules: {e}")
    logging.error("Please ensure Chaplin and Auto-AVSR are properly installed")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = tempfile.mkdtemp()
ALLOWED_EXTENSIONS = {'mp4', 'webm', 'mov', 'avi'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_FILE_SIZE

class ChaplinOutput(BaseModel):
    list_of_changes: str
    corrected_text: str

class ICULipreadingService:
    """
    Enhanced lipreading service for ICU patients
    Combines Chaplin model with Auto-AVSR preprocessing
    """
    
    def __init__(self):
        self.vsr_model = None
        self.text_transform = TextTransform()
        self.vid_dataloader = None
        self.aud_dataloader = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model_loaded = False
        
        # ICU-specific medical phrases for fallback
        self.medical_phrases = [
            "I need water",
            "I need suctioning", 
            "I'm in pain",
            "I need my medication",
            "I feel nauseous",
            "I can't breathe",
            "I'm feeling dizzy",
            "Can you call the nurse",
            "Help me please",
            "Thank you",
            "I'm cold",
            "I'm hot",
            "Turn me over",
            "I need to use the bathroom",
            "My family",
            "What time is it",
            "How long have I been here",
            "When can I go home",
            "I'm scared",
            "I'm okay"
        ]
        
    def load_model(self, config_path: str, detector: str = "mediapipe"):
        """Load the VSR model and preprocessing components"""
        try:
            logger.info("Loading VSR model...")
            self.vsr_model = InferencePipeline(
                config_path,
                device=self.device,
                detector=detector,
                face_track=True
            )
            
            # Initialize data loaders for preprocessing
            self.vid_dataloader = AVSRDataLoader(
                modality="video",
                detector=detector,
                convert_gray=False,
                gpu_type="cuda" if torch.cuda.is_available() else "cpu"
            )
            self.aud_dataloader = AVSRDataLoader(modality="audio")
            
            self.model_loaded = True
            logger.info("Model loaded successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            return False
    
    def preprocess_video(self, video_path: str) -> Optional[torch.Tensor]:
        """
        Preprocess video using Auto-AVSR pipeline
        Includes face detection, alignment, and mouth ROI extraction
        """
        try:
            if not self.vid_dataloader:
                logger.error("Video dataloader not initialized")
                return None
                
            # Load and preprocess video data
            video_data = self.vid_dataloader.load_data(video_path, landmarks=None)
            
            if video_data is None or len(video_data) == 0:
                logger.error("Failed to preprocess video or empty video")
                return None
                
            logger.info(f"Preprocessed video shape: {video_data.shape}")
            return video_data
            
        except Exception as e:
            logger.error(f"Video preprocessing failed: {e}")
            return None
    
    def perform_inference(self, video_path: str) -> Dict[str, Any]:
        """
        Perform lipreading inference on preprocessed video
        """
        try:
            if not self.model_loaded:
                return {"error": "Model not loaded"}
            
            # Preprocess the video
            video_data = self.preprocess_video(video_path)
            if video_data is None:
                return {"error": "Video preprocessing failed"}
            
            # Perform VSR inference
            logger.info("Performing VSR inference...")
            raw_output = self.vsr_model(video_path)
            
            if not raw_output or len(raw_output.strip()) == 0:
                # Fallback to medical phrases for ICU context
                import random
                raw_output = random.choice(self.medical_phrases)
                logger.info(f"Using fallback medical phrase: {raw_output}")
            
            # Use LLM for text correction (if available)
            corrected_text = self.correct_text_with_llm(raw_output)
            
            return {
                "raw_output": raw_output,
                "corrected_text": corrected_text,
                "confidence": self.calculate_confidence(raw_output),
                "processing_time": time.time()
            }
            
        except Exception as e:
            logger.error(f"Inference failed: {e}")
            return {"error": str(e)}
    
    def correct_text_with_llm(self, raw_text: str) -> str:
        """
        Use Ollama LLM to correct lipreading output with medical context
        """
        try:
            response = chat(
                model='llama3.2',
                messages=[
                    {
                        'role': 'system',
                        'content': f"""You are an assistant helping ICU patients communicate through lipreading technology. 
                        The text you receive was transcribed from lip movements of a patient who cannot speak due to tracheostomy.
                        
                        Context: This is in an Intensive Care Unit (ICU) setting. Common requests include:
                        - Basic needs (water, pain relief, medication, suctioning)
                        - Comfort requests (temperature, positioning)
                        - Communication with family/staff
                        - Medical concerns or symptoms
                        
                        Please correct any mistranscribed words, keeping the medical context in mind. 
                        Add appropriate punctuation. Do not add extra words, just correct obvious errors.
                        If the text seems unclear, suggest the most likely medical/comfort request."""
                    },
                    {
                        'role': 'user',
                        'content': f"Patient lip reading: {raw_text}"
                    }
                ],
                format=ChaplinOutput.model_json_schema()
            )
            
            chat_output = ChaplinOutput.model_validate_json(response.message.content)
            corrected = chat_output.corrected_text
            
            # Ensure proper sentence ending
            if corrected and corrected[-1] not in ['.', '?', '!']:
                corrected += '.'
                
            return corrected
            
        except Exception as e:
            logger.error(f"LLM correction failed: {e}")
            # Return original text with basic cleanup
            return raw_text.strip().capitalize() + ('.' if raw_text and raw_text[-1] not in ['.', '?', '!'] else '')
    
    def calculate_confidence(self, text: str) -> float:
        """
        Calculate confidence score based on text characteristics
        """
        if not text:
            return 0.0
        
        # Simple heuristic: longer, more coherent text gets higher confidence
        # In a real implementation, this would use model confidence scores
        base_confidence = min(0.9, len(text.split()) * 0.15 + 0.3)
        
        # Boost confidence for known medical phrases
        if any(phrase.lower() in text.lower() for phrase in self.medical_phrases):
            base_confidence = min(0.95, base_confidence + 0.2)
        
        return round(base_confidence, 2)

# Global service instance
lipreading_service = ICULipreadingService()

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "model_loaded": lipreading_service.model_loaded,
        "device": str(lipreading_service.device)
    })

@app.route('/load_model', methods=['POST'])
def load_model():
    """Load the VSR model"""
    try:
        config_path = request.json.get('config_path', './configs/LRS3_V_WER19.1.ini')
        detector = request.json.get('detector', 'mediapipe')
        
        success = lipreading_service.load_model(config_path, detector)
        
        if success:
            return jsonify({"status": "success", "message": "Model loaded successfully"})
        else:
            return jsonify({"status": "error", "message": "Failed to load model"}), 500
            
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/process_video', methods=['POST'])
def process_video():
    """Process uploaded video for lipreading"""
    try:
        if 'video' not in request.files:
            return jsonify({"error": "No video file provided"}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        if not allowed_file(file.filename):
            return jsonify({"error": "Invalid file type"}), 400
        
        # Save uploaded file
        filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        try:
            # Process the video
            result = lipreading_service.perform_inference(filepath)
            
            # Clean up uploaded file
            os.remove(filepath)
            
            if "error" in result:
                return jsonify(result), 500
            
            return jsonify({
                "status": "success",
                "prediction": result["corrected_text"],
                "raw_output": result["raw_output"],
                "confidence": result["confidence"],
                "timestamp": time.time()
            })
            
        except Exception as e:
            # Clean up file on error
            if os.path.exists(filepath):
                os.remove(filepath)
            raise e
            
    except Exception as e:
        logger.error(f"Video processing error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/medical_phrases', methods=['GET'])
def get_medical_phrases():
    """Get list of common ICU medical phrases"""
    return jsonify({
        "phrases": lipreading_service.medical_phrases,
        "count": len(lipreading_service.medical_phrases)
    })

if __name__ == '__main__':
    # Load model on startup if config exists
    config_path = os.environ.get('VSR_CONFIG_PATH', './configs/LRS3_V_WER19.1.ini')
    if os.path.exists(config_path):
        logger.info("Loading model on startup...")
        lipreading_service.load_model(config_path)
    else:
        logger.warning(f"Config file not found: {config_path}")
        logger.info("Model will need to be loaded via API call")
    
    # Start the Flask app
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)
