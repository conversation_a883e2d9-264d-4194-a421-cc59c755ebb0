#!/bin/bash
# Automated model download script for ICU Lipreading App

set -e  # Exit on any error

echo "🚀 ICU Lipreading App - Model Download Script"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "app.py" ]; then
    print_error "Please run this script from the backend directory"
    print_status "Usage: cd ICULipreadingApp/backend && ./download_models.sh"
    exit 1
fi

# Create models directory
print_status "Creating models directory..."
mkdir -p models
cd models

# Check if gdown is available
if ! command -v gdown &> /dev/null; then
    print_warning "gdown not found. Installing..."
    pip install gdown
    
    if [ $? -ne 0 ]; then
        print_error "Failed to install gdown. Please install manually:"
        print_status "pip install gdown"
        exit 1
    fi
fi

# Function to download with progress and retry
download_model() {
    local file_id=$1
    local filename=$2
    local description=$3
    local max_attempts=3
    local attempt=1

    print_status "Downloading $description..."
    
    while [ $attempt -le $max_attempts ]; do
        print_status "Attempt $attempt/$max_attempts: $filename"
        
        # Use gdown with progress bar
        if gdown "https://drive.google.com/uc?id=$file_id" -O "$filename" --fuzzy; then
            print_success "Downloaded $filename"
            return 0
        else
            print_warning "Download attempt $attempt failed"
            if [ $attempt -lt $max_attempts ]; then
                print_status "Retrying in 5 seconds..."
                sleep 5
            fi
            ((attempt++))
        fi
    done
    
    print_error "Failed to download $filename after $max_attempts attempts"
    print_status "Manual download required from: https://drive.google.com/file/d/$file_id/view"
    return 1
}

# Function to extract and verify
extract_and_verify() {
    local filename=$1
    local expected_dir=$2
    
    if [ -f "$filename" ]; then
        print_status "Extracting $filename..."
        
        # Check file size (basic verification)
        file_size=$(stat -f%z "$filename" 2>/dev/null || stat -c%s "$filename" 2>/dev/null || echo "0")
        if [ "$file_size" -lt 1000000 ]; then  # Less than 1MB is suspicious
            print_warning "File $filename seems too small ($file_size bytes). May be incomplete."
        fi
        
        # Extract
        if unzip -q "$filename"; then
            print_success "Extracted $filename"
            rm "$filename"  # Clean up zip file
            
            # Verify extraction
            if [ -d "$expected_dir" ]; then
                print_success "Verified directory: $expected_dir"
                ls -la "$expected_dir" | head -5
            else
                print_warning "Expected directory $expected_dir not found after extraction"
            fi
        else
            print_error "Failed to extract $filename"
            return 1
        fi
    else
        print_error "File $filename not found"
        return 1
    fi
}

# Download LRS3 Model
print_status "📥 Downloading LRS3_V_WER19.1 model (Main lipreading model)..."
print_status "This is a large file (~2-3 GB) and may take several minutes..."

if download_model "1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6" "LRS3_V_WER19.1.zip" "LRS3 Lipreading Model"; then
    extract_and_verify "LRS3_V_WER19.1.zip" "LRS3_V_WER19.1"
else
    print_error "Failed to download LRS3 model"
    DOWNLOAD_FAILED=1
fi

# Download Language Model
print_status "📥 Downloading lm_en_subword (Language model for text correction)..."
print_status "This file is smaller (~500 MB) but still may take a few minutes..."

if download_model "1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv" "lm_en_subword.zip" "English Language Model"; then
    extract_and_verify "lm_en_subword.zip" "lm_en_subword"
else
    print_error "Failed to download language model"
    DOWNLOAD_FAILED=1
fi

# Final verification
print_status "🔍 Verifying model installation..."

if [ -d "LRS3_V_WER19.1" ] && [ -d "lm_en_subword" ]; then
    print_success "✅ All models downloaded and extracted successfully!"
    print_status "📁 Model directory structure:"
    echo "models/"
    echo "├── LRS3_V_WER19.1/"
    ls -la LRS3_V_WER19.1/ | head -3 | sed 's/^/│   /'
    echo "└── lm_en_subword/"
    ls -la lm_en_subword/ | head -3 | sed 's/^/    /'
    
    # Calculate total size
    total_size=$(du -sh . | cut -f1)
    print_status "📊 Total model size: $total_size"
    
    print_success "🎉 Model download complete! You can now start the backend service."
    print_status "Next steps:"
    print_status "1. cd .."
    print_status "2. python app.py"
    
else
    print_error "❌ Model installation incomplete"
    if [ -n "$DOWNLOAD_FAILED" ]; then
        print_status "Some downloads failed. You can:"
        print_status "1. Try running this script again"
        print_status "2. Download manually from Google Drive:"
        print_status "   - LRS3: https://drive.google.com/file/d/1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6/view"
        print_status "   - Language Model: https://drive.google.com/file/d/1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv/view"
        print_status "3. Use simulation mode for testing (see README.md)"
    fi
    exit 1
fi

print_status "🔧 Setting up configuration..."
cd ..

# Update config file if it exists
if [ -f "configs/icu_lipreading.ini" ]; then
    print_status "Updating configuration file..."
    # Ensure model paths are correct
    sed -i.bak 's|path = .*LRS3_V_WER19.1|path = ./models/LRS3_V_WER19.1|g' configs/icu_lipreading.ini
    sed -i.bak 's|language_model = .*lm_en_subword|language_model = ./models/lm_en_subword|g' configs/icu_lipreading.ini
    print_success "Configuration updated"
fi

print_success "🚀 Setup complete! Ready to start the ICU Lipreading backend."
print_status "Run: python app.py"
