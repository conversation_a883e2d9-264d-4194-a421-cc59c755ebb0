#!/usr/bin/env python3
"""
Setup script for ICU Lipreading Backend
Downloads and configures Chaplin and Auto-AVSR components
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and return success status"""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, check=True, 
                              capture_output=True, text=True)
        print(f"✓ {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {command}")
        print(f"Error: {e.stderr}")
        return False

def download_file(url, destination):
    """Download a file from URL to destination"""
    try:
        print(f"Downloading {url}...")
        urllib.request.urlretrieve(url, destination)
        print(f"✓ Downloaded to {destination}")
        return True
    except Exception as e:
        print(f"✗ Failed to download {url}: {e}")
        return False

def setup_directories():
    """Create necessary directories"""
    directories = [
        'chaplin',
        'auto_avsr', 
        'models',
        'configs',
        'temp'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def clone_repositories():
    """Clone Chaplin and Auto-AVSR repositories"""
    repos = [
        {
            'url': 'https://github.com/amanvirparhar/chaplin.git',
            'dir': 'chaplin'
        },
        {
            'url': 'https://github.com/mpc001/auto_avsr.git', 
            'dir': 'auto_avsr'
        }
    ]
    
    for repo in repos:
        if os.path.exists(repo['dir']):
            print(f"✓ {repo['dir']} already exists")
            continue
            
        success = run_command(f"git clone {repo['url']} {repo['dir']}")
        if not success:
            print(f"✗ Failed to clone {repo['url']}")
            return False
    
    return True

def download_models():
    """Download pre-trained models"""
    print("\n=== Downloading Models ===")
    
    # Note: These are placeholder URLs - in practice, you'd need to download from Google Drive
    # or other sources as specified in the Chaplin README
    models = [
        {
            'name': 'LRS3_V_WER19.1',
            'url': 'https://drive.google.com/file/d/1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6/view',
            'note': 'Manual download required from Google Drive'
        },
        {
            'name': 'lm_en_subword',
            'url': 'https://drive.google.com/file/d/1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv/view',
            'note': 'Manual download required from Google Drive'
        }
    ]
    
    print("Note: Model files need to be downloaded manually from Google Drive:")
    for model in models:
        print(f"  - {model['name']}: {model['url']}")
    
    print("\nAfter downloading, place them in the following structure:")
    print("  backend/")
    print("  ├── models/")
    print("  │   ├── LRS3_V_WER19.1/")
    print("  │   └── lm_en_subword/")
    
    return True

def setup_configs():
    """Create configuration files"""
    print("\n=== Setting up Configurations ===")
    
    # Create a basic config file
    config_content = """
# ICU Lipreading Configuration
[model]
name = LRS3_V_WER19.1
path = ./models/LRS3_V_WER19.1
language_model = ./models/lm_en_subword

[preprocessing]
detector = mediapipe
face_track = true
convert_gray = false

[inference]
device = auto
batch_size = 1
max_duration = 10

[icu_settings]
medical_phrases_enabled = true
confidence_threshold = 0.7
fallback_enabled = true
"""
    
    with open('configs/icu_lipreading.ini', 'w') as f:
        f.write(config_content)
    
    print("✓ Created ICU lipreading configuration")
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("\n=== Installing Dependencies ===")
    
    # Install requirements
    success = run_command(f"{sys.executable} -m pip install -r requirements.txt")
    if not success:
        print("✗ Failed to install requirements")
        return False
    
    # Install additional dependencies for Auto-AVSR
    additional_deps = [
        "torch torchvision torchaudio",
        "opencv-python",
        "mediapipe",
        "ffmpeg-python"
    ]
    
    for dep in additional_deps:
        success = run_command(f"{sys.executable} -m pip install {dep}")
        if not success:
            print(f"Warning: Failed to install {dep}")
    
    return True

def setup_ollama():
    """Setup Ollama for text correction"""
    print("\n=== Setting up Ollama ===")
    
    # Check if Ollama is installed
    if not run_command("ollama --version"):
        print("Ollama not found. Please install Ollama manually:")
        print("  macOS: brew install ollama")
        print("  Linux: curl -fsSL https://ollama.ai/install.sh | sh")
        print("  Windows: Download from https://ollama.ai/download")
        return False
    
    # Pull the required model
    success = run_command("ollama pull llama3.2")
    if not success:
        print("Warning: Failed to pull llama3.2 model")
        print("You can pull it manually later with: ollama pull llama3.2")
    
    return True

def create_startup_script():
    """Create startup script for the backend"""
    startup_script = """#!/bin/bash
# ICU Lipreading Backend Startup Script

echo "Starting ICU Lipreading Backend..."

# Check if models exist
if [ ! -d "models/LRS3_V_WER19.1" ]; then
    echo "Warning: LRS3_V_WER19.1 model not found"
    echo "Please download from: https://drive.google.com/file/d/1t8RHhzDTTvOQkLQhmK1LZGnXRRXOXGi6/view"
fi

if [ ! -d "models/lm_en_subword" ]; then
    echo "Warning: lm_en_subword model not found"
    echo "Please download from: https://drive.google.com/file/d/1g31HGxJnnOwYl17b70ObFQZ1TSnPvRQv/view"
fi

# Start Ollama if not running
if ! pgrep -x "ollama" > /dev/null; then
    echo "Starting Ollama..."
    ollama serve &
    sleep 5
fi

# Set environment variables
export VSR_CONFIG_PATH="./configs/icu_lipreading.ini"
export FLASK_ENV="development"
export FLASK_DEBUG="1"

# Start the Flask app
python app.py
"""
    
    with open('start_backend.sh', 'w') as f:
        f.write(startup_script)
    
    # Make executable
    os.chmod('start_backend.sh', 0o755)
    print("✓ Created startup script: start_backend.sh")

def main():
    """Main setup function"""
    print("=== ICU Lipreading Backend Setup ===\n")
    
    # Change to backend directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    steps = [
        ("Setting up directories", setup_directories),
        ("Cloning repositories", clone_repositories),
        ("Installing dependencies", install_dependencies),
        ("Setting up configurations", setup_configs),
        ("Downloading models", download_models),
        ("Setting up Ollama", setup_ollama),
        ("Creating startup script", create_startup_script),
    ]
    
    for step_name, step_func in steps:
        print(f"\n=== {step_name} ===")
        try:
            success = step_func()
            if not success:
                print(f"✗ {step_name} failed")
                print("Setup incomplete. Please check the errors above.")
                return False
        except Exception as e:
            print(f"✗ {step_name} failed with exception: {e}")
            return False
    
    print("\n=== Setup Complete ===")
    print("Next steps:")
    print("1. Download the model files manually from Google Drive (see above)")
    print("2. Place them in the models/ directory")
    print("3. Run: ./start_backend.sh")
    print("4. The backend will be available at http://localhost:5000")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
