{"name": "ICULipreadingApp", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "backend": "cd backend && python app.py"}, "dependencies": {"react": "18.2.0", "react-native": "0.73.0", "react-native-camera": "^4.2.1", "react-native-video": "^5.2.1", "react-native-fs": "^2.20.0", "react-native-permissions": "^4.1.5", "react-native-vector-icons": "^10.0.3", "react-native-gesture-handler": "^2.14.0", "react-native-reanimated": "^3.6.0", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "react-native-elements": "^3.4.3", "react-native-paper": "^5.11.6", "axios": "^1.6.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.77.0", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}