{"name": "ICULipreadingApp", "version": "1.0.0", "private": true, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint .", "test": "jest", "backend": "cd backend && python simple_app.py"}, "dependencies": {"@expo/cli": "^54.0.10", "@expo/vector-icons": "^14.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "axios": "^1.6.2", "babel-preset-expo": "~13.0.0", "expo": "~53.0.0", "expo-av": "~15.1.7", "expo-camera": "~16.1.11", "expo-file-system": "~18.1.11", "expo-permissions": "~14.4.0", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.11.6", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.77.0", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "^5.3.0"}, "engines": {"node": ">=16"}}