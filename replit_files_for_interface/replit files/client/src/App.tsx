import { useState } from "react";
import WelcomeScreen from "@/components/WelcomeScreen";
import MainInterface from "@/components/MainInterface";
import ResultsModal from "@/components/ResultsModal";
import ErrorModal from "@/components/ErrorModal";

function App() {
  const [currentScreen, setCurrentScreen] = useState<"welcome" | "main">("welcome");
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [prediction, setPrediction] = useState("");
  const [recordedVideoUrl, setRecordedVideoUrl] = useState<string | null>(null);
  
  const handleEnterApp = () => {
    setCurrentScreen("main");
  };

  const handleBackToWelcome = () => {
    setCurrentScreen("welcome");
  };

  const handleShowResults = (prediction: string, videoUrl: string) => {
    setPrediction(prediction);
    setRecordedVideoUrl(videoUrl);
    setShowResultsModal(true);
  };

  const handleShowError = (message: string) => {
    setErrorMessage(message);
    setShowErrorModal(true);
  };

  return (
    <div className="relative max-w-md mx-auto h-screen bg-white shadow-lg overflow-hidden">
      {/* Test buttons for easy navigation in preview mode */}
      <div className="fixed top-0 right-0 z-50 p-2 bg-black/70 rounded-bl-lg shadow-lg">
        <h3 className="text-white text-xs font-bold mb-2 text-center">Test Controls</h3>
        <div className="flex flex-col space-y-2">
          <button 
            onClick={handleEnterApp} 
            className="bg-primary text-white px-3 py-1.5 rounded text-xs font-medium hover:bg-primary/90 transition-colors"
            style={{ cursor: 'pointer', pointerEvents: 'auto' }}
          >
            Go to Main Screen
          </button>
          <button 
            onClick={handleBackToWelcome} 
            className="bg-primary/80 text-white px-3 py-1.5 rounded text-xs font-medium hover:bg-primary/70 transition-colors"
            style={{ cursor: 'pointer', pointerEvents: 'auto' }}
          >
            Back to Welcome
          </button>

        </div>
      </div>

      {currentScreen === "welcome" && (
        <WelcomeScreen onEnterApp={handleEnterApp} />
      )}

      {currentScreen === "main" && (
        <MainInterface 
          onBackToWelcome={handleBackToWelcome}
          onShowResults={handleShowResults}
          onError={handleShowError}
        />
      )}

      {showResultsModal && (
        <ResultsModal
          prediction={prediction}
          videoUrl={recordedVideoUrl}
          onClose={() => setShowResultsModal(false)}
          onRetry={() => {
            setShowResultsModal(false);
          }}
          onRecordAgain={() => {
            setShowResultsModal(false);
            // Stay on main interface screen to allow recording again
          }}
        />
      )}

      {showErrorModal && (
        <ErrorModal
          message={errorMessage}
          onClose={() => setShowErrorModal(false)}
        />
      )}
    </div>
  );
}

export default App;
