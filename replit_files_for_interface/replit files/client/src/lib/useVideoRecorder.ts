import { useState, useEffect, useRef, useCallback } from 'react';
import RecordRTC from 'recordrtc';
import { isIOS, isSafari } from './utils';

interface UseVideoRecorderProps {
  stream: MediaStream | null;
  videoRef: React.RefObject<HTMLVideoElement>;
}

interface UseVideoRecorderReturn {
  isRecording: boolean;
  recordedVideoUrl: string | null;
  startRecording: () => void;
  stopRecording: () => void;
  error: Error | null;
  isSupported: boolean;
  warning: string | null;
}

const useVideoRecorder = ({ stream, videoRef }: UseVideoRecorderProps): UseVideoRecorderReturn => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordedVideoUrl, setRecordedVideoUrl] = useState<string | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isSupported, setIsSupported] = useState(true);
  const [warning, setWarning] = useState<string | null>(null);
  
  const recorderRef = useRef<RecordRTC | null>(null);

  // Check MediaRecorder support on mount
  useEffect(() => {
    const checkSupport = () => {
      if (!('MediaRecorder' in window)) {
        setIsSupported(false);
        setError(new Error('Video recording is not supported in this browser'));
        return;
      }

      // Check for iOS Safari limitations (only on actual iOS devices, not desktop Safari)
      if (isIOS()) {
        // iOS Safari 14.3+ has limited MediaRecorder support
        const userAgent = navigator.userAgent;
        const match = userAgent.match(/OS (\d+)_(\d+)/);
        const majorVersion = match ? parseInt(match[1], 10) : 0;
        const minorVersion = match ? parseInt(match[2], 10) : 0;
        
        // Check if iOS version is 14.3 or higher
        const isVersionSupported = majorVersion > 14 || (majorVersion === 14 && minorVersion >= 3);
        
        if (majorVersion > 0 && !isVersionSupported) {
          setIsSupported(false);
          setError(new Error('iOS 14.3 or higher is required for video recording'));
        } else if (isVersionSupported) {
          setWarning('iOS video recording has limited functionality. Recorded videos may not play back correctly in all cases.');
        }
      }
    };

    checkSupport();
  }, []);

  const startRecording = useCallback(() => {
    if (!stream) {
      setError(new Error('No camera stream available'));
      return;
    }

    try {
      // Configure RecordRTC based on browser/platform
      // For iOS Safari, we need to use specific settings that work with their MediaRecorder implementation
      const options: RecordRTC.Options = {
        type: 'video',
        // Let RecordRTC auto-detect the best recorder type for the platform
        // iOS Safari will use MediaStreamRecorder which supports H.264
        recorderType: isIOS() || isSafari() ? RecordRTC.MediaStreamRecorder : undefined,
        // For iOS, use H.264 codec in WebM container (Safari supports this via MediaStreamRecorder)
        // or fall back to standard MP4
        mimeType: isIOS() || isSafari() ? 'video/mp4' : 'video/webm;codecs=vp9',
        // Use lower bitrate on iOS for better compatibility
        videoBitsPerSecond: isIOS() ? 256000 : 512000,
        // Disable audio since we only need video
        disableLogs: false,
      };

      recorderRef.current = new RecordRTC(stream, options);
      recorderRef.current.startRecording();
      setIsRecording(true);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Error starting recording'));
      console.error('Recording error:', err);
    }
  }, [stream]);

  const stopRecording = useCallback(() => {
    if (recorderRef.current && isRecording) {
      recorderRef.current.stopRecording(() => {
        const blob = recorderRef.current?.getBlob();
        if (blob) {
          const url = URL.createObjectURL(blob);
          setRecordedVideoUrl(url);
        }
        setIsRecording(false);
      });
    }
  }, [isRecording]);

  // Clean up URLs to prevent memory leaks
  useEffect(() => {
    return () => {
      if (recordedVideoUrl) {
        URL.revokeObjectURL(recordedVideoUrl);
      }
      if (recorderRef.current) {
        recorderRef.current.destroy();
        recorderRef.current = null;
      }
    };
  }, [recordedVideoUrl]);

  return {
    isRecording,
    recordedVideoUrl,
    startRecording,
    stopRecording,
    error,
    isSupported,
    warning
  };
};

export default useVideoRecorder;
