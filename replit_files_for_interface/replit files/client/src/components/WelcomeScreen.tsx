import { <PERSON> } from "react";
import { But<PERSON> } from "@/components/ui/button";
import Logo from "@/components/Logo";
import { ArrowRight } from "lucide-react";
import PatientImage from "@/components/PatientImage";

interface WelcomeScreenProps {
  onEnterApp: () => void;
}

const WelcomeScreen: FC<WelcomeScreenProps> = ({ onEnterApp }) => {
  return (
    <div className="flex flex-col items-center min-h-screen px-6 pt-14 pb-8 bg-white">
      <div className="w-full text-center">
        <h1 className="text-4xl font-bold text-primary mb-3"><PERSON>HS Lipreader</h1>
        <p className="text-gray-600 mb-4">Assistive communication technology</p>
        
        <div className="mb-2 rounded-lg overflow-hidden shadow-lg bg-white">
          <div className="p-4">
            <div className="flex justify-center mb-2">
              {/* SVG of the older patient with phone concept */}
              <PatientImage />
            </div>
            <p className="text-center text-gray-700">
              SMHS Lipreader is designed to help patients communicate through lip movement recognition, enhancing healthcare communication.
            </p>
          </div>
        </div>
      </div>
      
      <div className="w-full mt-2">
        <div className="bg-gray-100 p-4 rounded-lg mb-6 text-sm">
          <h3 className="font-semibold text-primary mb-2 text-center">Disclaimer</h3>
          <p className="text-gray-700 text-center">
            This application is designed to assist with communication by reading lip movements. It is not intended to replace professional medical advice or interpretation. This tool is designed to assist with communication, but should always be used alongside professional clinical decision-making.
          </p>
        </div>
        
        <Button 
          onClick={onEnterApp} 
          className="w-full bg-primary hover:bg-primary/90 text-white font-bold py-4 px-6 rounded-lg transition duration-300 flex items-center justify-center cursor-pointer"
          style={{ cursor: 'pointer', pointerEvents: 'auto' }}
          tabIndex={0}
          onKeyPress={(e) => e.key === 'Enter' && onEnterApp()}
        >
          Enter Application
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default WelcomeScreen;
