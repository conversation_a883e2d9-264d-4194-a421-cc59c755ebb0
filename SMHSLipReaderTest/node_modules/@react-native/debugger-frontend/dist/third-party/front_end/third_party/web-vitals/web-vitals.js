let e=-1;const t=()=>e,n=t=>{addEventListener("pageshow",(n=>{n.persisted&&(e=n.timeStamp,t(n))}),!0)},r=()=>{const e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},a=()=>{const e=r();return e&&e.activationStart||0},i=(e,n)=>{const i=r();let s="navigate";t()>=0?s="back-forward-cache":i&&(document.prerendering||a()>0?s="prerender":document.wasDiscarded?s="restore":i.type&&(s=i.type.replace(/_/g,"-")));return{name:e,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:s}},s=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const r=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},o=(e,t,n,r)=>{let a,i;return s=>{t.value>=0&&(s||r)&&(i=t.value-(a||0),(i||void 0===a)&&(a=t.value,t.delta=i,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,n),e(t)))}},c=e=>{requestAnimationFrame((()=>requestAnimationFrame((()=>e()))))},l=e=>{document.addEventListener("visibilitychange",(()=>{"hidden"===document.visibilityState&&e()}))},d=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let m=-1;const u=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,p=e=>{"hidden"===document.visibilityState&&m>-1&&(m="visibilitychange"===e.type?e.timeStamp:0,g())},h=()=>{addEventListener("visibilitychange",p,!0),addEventListener("prerenderingchange",p,!0)},g=()=>{removeEventListener("visibilitychange",p,!0),removeEventListener("prerenderingchange",p,!0)},f=()=>(m<0&&(m=u(),h(),n((()=>{setTimeout((()=>{m=u(),h()}),0)}))),{get firstHiddenTime(){return m}}),T=e=>{document.prerendering?addEventListener("prerenderingchange",(()=>e()),!0):e()},v=[1800,3e3],y=(e,t)=>{t=t||{},T((()=>{const r=f();let l,d=i("FCP");const m=s("paint",(e=>{e.forEach((e=>{"first-contentful-paint"===e.name&&(m.disconnect(),e.startTime<r.firstHiddenTime&&(d.value=Math.max(e.startTime-a(),0),d.entries.push(e),l(!0)))}))}));m&&(l=o(e,d,v,t.reportAllChanges),n((n=>{d=i("FCP"),l=o(e,d,v,t.reportAllChanges),c((()=>{d.value=performance.now()-n.timeStamp,l(!0)}))})))}))},E=[.1,.25],S=(e,t)=>{t=t||{},y(d((()=>{let r,a=i("CLS",0),d=0,m=[];const u=e=>{e.forEach((e=>{if(!e.hadRecentInput){const t=m[0],n=m[m.length-1];d&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(d+=e.value,m.push(e)):(d=e.value,m=[e])}})),d>a.value&&(a.value=d,a.entries=m,r())},p=s("layout-shift",u);p&&(r=o(e,a,E,t.reportAllChanges),l((()=>{u(p.takeRecords()),r(!0)})),n((()=>{d=0,a=i("CLS",0),r=o(e,a,E,t.reportAllChanges),c((()=>r()))})),setTimeout(r,0))})))};let b=0,C=1/0,L=0;const D=e=>{e.forEach((e=>{e.interactionId&&(C=Math.min(C,e.interactionId),L=Math.max(L,e.interactionId),b=L?(L-C)/7+1:0)}))};let M;const w=()=>M?b:performance.interactionCount||0,F=()=>{"interactionCount"in performance||M||(M=s("event",D,{type:"event",buffered:!0,durationThreshold:0}))},P=[],I=new Map;let x=0;const k=()=>{const e=Math.min(P.length-1,Math.floor((w()-x)/50));return P[e]},A=[],B=e=>{if(A.forEach((t=>t(e))),!e.interactionId&&"first-input"!==e.entryType)return;const t=P[P.length-1],n=I.get(e.interactionId);if(n||P.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{const t={id:e.interactionId,latency:e.duration,entries:[e]};I.set(t.id,t),P.push(t)}P.sort(((e,t)=>t.latency-e.latency)),P.length>10&&P.splice(10).forEach((e=>I.delete(e.id)))}},O=e=>{const t=self.requestIdleCallback||self.setTimeout;let n=-1;return e=d(e),"hidden"===document.visibilityState?e():(n=t(e),l(e)),n},j=[200,500],R=(e,t)=>{"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},T((()=>{F();let r,a=i("INP");const c=e=>{O((()=>{e.forEach(B);const t=k();t&&t.latency!==a.value&&(a.value=t.latency,a.entries=t.entries,r())}))},d=s("event",c,{durationThreshold:t.durationThreshold??40});r=o(e,a,j,t.reportAllChanges),d&&(d.observe({type:"first-input",buffered:!0}),l((()=>{c(d.takeRecords()),r(!0)})),n((()=>{x=w(),P.length=0,I.clear(),a=i("INP"),r=o(e,a,j,t.reportAllChanges)})))})))},N=[2500,4e3],q={},_=(e,t)=>{t=t||{},T((()=>{const r=f();let m,u=i("LCP");const p=e=>{t.reportAllChanges||(e=e.slice(-1)),e.forEach((e=>{e.startTime<r.firstHiddenTime&&(u.value=Math.max(e.startTime-a(),0),u.entries=[e],m())}))},h=s("largest-contentful-paint",p);if(h){m=o(e,u,N,t.reportAllChanges);const r=d((()=>{q[u.id]||(p(h.takeRecords()),h.disconnect(),q[u.id]=!0,m(!0))}));["keydown","click"].forEach((e=>{addEventListener(e,(()=>O(r)),{once:!0,capture:!0})})),l(r),n((n=>{u=i("LCP"),m=o(e,u,N,t.reportAllChanges),c((()=>{u.value=performance.now()-n.timeStamp,q[u.id]=!0,m(!0)}))}))}}))},H=[800,1800],z=e=>{document.prerendering?T((()=>z(e))):"complete"!==document.readyState?addEventListener("load",(()=>z(e)),!0):setTimeout(e,0)},W=(e,t)=>{t=t||{};let s=i("TTFB"),c=o(e,s,H,t.reportAllChanges);z((()=>{const l=r();l&&(s.value=Math.max(l.responseStart-a(),0),s.entries=[l],c(!0),n((()=>{s=i("TTFB",0),c=o(e,s,H,t.reportAllChanges),c(!0)})))}))};let $,U,V,G;const J={passive:!0,capture:!0},K=new Date,Q=(e,t)=>{$||($=t,U=e,V=new Date,Z(removeEventListener),X())},X=()=>{if(U>=0&&U<V-K){const e={entryType:"first-input",name:$.type,target:$.target,cancelable:$.cancelable,startTime:$.timeStamp,processingStart:$.timeStamp+U};G.forEach((function(t){t(e)})),G=[]}},Y=e=>{if(e.cancelable){const t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?((e,t)=>{const n=()=>{Q(e,t),a()},r=()=>{a()},a=()=>{removeEventListener("pointerup",n,J),removeEventListener("pointercancel",r,J)};addEventListener("pointerup",n,J),addEventListener("pointercancel",r,J)})(t,e):Q(t,e)}},Z=e=>{["mousedown","keydown","touchstart","pointerdown"].forEach((t=>e(t,Y,J)))},ee=[100,300],te=(e,t)=>{t=t||{},T((()=>{const r=f();let a,c=i("FID");const m=e=>{e.startTime<r.firstHiddenTime&&(c.value=e.processingStart-e.startTime,c.entries.push(e),a(!0))},u=e=>{e.forEach(m)},p=s("first-input",u);a=o(e,c,ee,t.reportAllChanges),p&&(l(d((()=>{u(p.takeRecords()),p.disconnect()}))),n((()=>{var n;c=i("FID"),a=o(e,c,ee,t.reportAllChanges),G=[],U=-1,$=null,Z(addEventListener),n=m,G.push(n),X()})))}))},ne=e=>{if("loading"===document.readyState)return"loading";{const t=r();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}}return"complete"},re=e=>{const t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},ae=(e,t)=>{let n="";try{for(;e&&9!==e.nodeType;){const r=e,a=r.id?"#"+r.id:re(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?"."+r.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+a.length>(t||100)-1)return n||a;if(n=n?a+">"+n:a,r.id)break;e=r.parentNode}}catch(e){}return n};let ie,se=[],oe=[],ce=0;const le=new WeakMap,de=new Map;let me=-1;const ue=e=>{se=se.concat(e),pe()},pe=()=>{me<0&&(me=O(he))},he=()=>{de.size>10&&de.forEach(((e,t)=>{I.has(t)||de.delete(t)}));const e=P.map((e=>le.get(e.entries[0]))),t=oe.length-50;oe=oe.filter(((n,r)=>r>=t||e.includes(n)));const n=new Set;for(let e=0;e<oe.length;e++){const t=oe[e];ge(t.startTime,t.processingEnd).forEach((e=>{n.add(e)}))}const r=se.length-1-50;se=se.filter(((e,t)=>e.startTime>ce&&t>r||n.has(e))),me=-1};A.push((e=>{}),(e=>{const t=e.startTime+e.duration;let n;ce=Math.max(ce,e.processingEnd);for(let r=oe.length-1;r>=0;r--){const a=oe[r];if(Math.abs(t-a.renderTime)<=8){n=a,n.startTime=Math.min(e.startTime,n.startTime),n.processingStart=Math.min(e.processingStart,n.processingStart),n.processingEnd=Math.max(e.processingEnd,n.processingEnd),n.entries.push(e);break}}n||(n={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:t,entries:[e]},oe.push(n)),(e.interactionId||"first-input"===e.entryType)&&le.set(e,n),pe()}));const ge=(e,t)=>{const n=[];for(let r,a=0;r=se[a];a++)if(!(r.startTime+r.duration<e)){if(r.startTime>t)break;n.push(r)}return n},fe=e=>{const t=e.entries[0],n=le.get(t),r=t.processingStart,a=n.processingEnd,i=n.entries.sort(((e,t)=>e.processingStart-t.processingStart)),s=ge(t.startTime,a),o=e.entries.find((e=>e.target)),c=o&&o.target||de.get(t.interactionId),l=[t.startTime+t.duration,a].concat(s.map((e=>e.startTime+e.duration))),d=Math.max.apply(Math,l),m={interactionTarget:ae(c),interactionTargetElement:c,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:d,processedEventEntries:i,longAnimationFrameEntries:s,inputDelay:r-t.startTime,processingDuration:a-r,presentationDelay:Math.max(d-a,0),loadState:ne(t.startTime)};return Object.assign(e,{attribution:m})};var Te=Object.freeze({__proto__:null,CLSThresholds:E,FCPThresholds:v,FIDThresholds:ee,INPThresholds:j,LCPThresholds:N,TTFBThresholds:H,onCLS:(e,t)=>{S((t=>{const n=(e=>{let t={};if(e.entries.length){const r=e.entries.reduce(((e,t)=>e&&e.value>t.value?e:t));if(r&&r.sources&&r.sources.length){const e=(n=r.sources).find((e=>e.node&&1===e.node.nodeType))||n[0];e&&(t={largestShiftTarget:ae(e.node),largestShiftTime:r.startTime,largestShiftValue:r.value,largestShiftSource:e,largestShiftEntry:r,loadState:ne(r.startTime)})}}var n;return Object.assign(e,{attribution:t})})(t);e(n)}),t)},onFCP:(e,n)=>{y((n=>{const a=(e=>{let n={timeToFirstByte:0,firstByteToFCP:e.value,loadState:ne(t())};if(e.entries.length){const t=r(),a=e.entries[e.entries.length-1];if(t){const r=t.activationStart||0,i=Math.max(0,t.responseStart-r);n={timeToFirstByte:i,firstByteToFCP:e.value-i,loadState:ne(e.entries[0].startTime),navigationEntry:t,fcpEntry:a}}}return Object.assign(e,{attribution:n})})(n);e(a)}),n)},onFID:(e,t)=>{te((t=>{const n=(e=>{const t=e.entries[0],n={eventTarget:ae(t.target),eventType:t.name,eventTime:t.startTime,eventEntry:t,loadState:ne(t.startTime)};return Object.assign(e,{attribution:n})})(t);e(n)}),t)},onINP:(e,t)=>{ie||(ie=s("long-animation-frame",ue)),R((t=>{const n=fe(t);e(n)}),t)},onLCP:(e,t)=>{_((t=>{const n=(e=>{let t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){const n=r();if(n){const r=n.activationStart||0,a=e.entries[e.entries.length-1],i=a.url&&performance.getEntriesByType("resource").filter((e=>e.name===a.url))[0],s=Math.max(0,n.responseStart-r),o=Math.max(s,i?(i.requestStart||i.startTime)-r:0),c=Math.max(o,i?i.responseEnd-r:0),l=Math.max(c,a.startTime-r);t={element:ae(a.element),timeToFirstByte:s,resourceLoadDelay:o-s,resourceLoadDuration:c-o,elementRenderDelay:l-c,navigationEntry:n,lcpEntry:a},a.url&&(t.url=a.url),i&&(t.lcpResourceEntry=i)}}return Object.assign(e,{attribution:t})})(t);e(n)}),t)},onTTFB:(e,t)=>{W((t=>{const n=(e=>{let t={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(e.entries.length){const n=e.entries[0],r=n.activationStart||0,a=Math.max((n.workerStart||n.fetchStart)-r,0),i=Math.max(n.domainLookupStart-r,0),s=Math.max(n.connectStart-r,0),o=Math.max(n.connectEnd-r,0);t={waitingDuration:a,cacheDuration:i-a,dnsDuration:s-i,connectionDuration:o-s,requestDuration:e.value-o,navigationEntry:n}}return Object.assign(e,{attribution:t})})(t);e(n)}),t)}});export{Te as Attribution,E as CLSThresholds,v as FCPThresholds,ee as FIDThresholds,j as INPThresholds,N as LCPThresholds,H as TTFBThresholds,fe as attributeINP,A as entryPreProcessingCallbacks,n as onBFCacheRestore,S as onCLS,y as onFCP,te as onFID,R as onINP,_ as onLCP,W as onTTFB};
