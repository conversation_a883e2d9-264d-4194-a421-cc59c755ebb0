import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as n from"../../core/platform/platform.js";import*as r from"../../core/i18n/i18n.js";import*as o from"../text_utils/text_utils.js";let s;class i extends e.ObjectWrapper.ObjectWrapper{saveCallbacks;constructor(){super(),this.saveCallbacks=new Map,t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.SavedURL,this.savedURL,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.CanceledSaveURL,this.canceledSavedURL,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.AppendedToURL,this.appendedToURL,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return s&&!t||(s=new i),s}save(e,n,r,o){const s=new Promise((t=>this.saveCallbacks.set(e,t)));return t.InspectorFrontendHost.InspectorFrontendHostInstance.save(e,n,r,o),s}savedURL(e){const{url:t,fileSystemPath:n}=e.data,r=this.saveCallbacks.get(t);this.saveCallbacks.delete(t),r&&r({fileSystemPath:n})}canceledSavedURL({data:e}){const t=this.saveCallbacks.get(e);this.saveCallbacks.delete(e),t&&t(null)}append(e,n){t.InspectorFrontendHost.InspectorFrontendHostInstance.append(e,n)}close(e){t.InspectorFrontendHost.InspectorFrontendHostInstance.close(e)}appendedToURL({data:e}){this.dispatchEventToListeners("AppendedToURL",e)}}var a=Object.freeze({__proto__:null,FileManager:i});class c{#e;#t;#n;#r;#o;constructor(e,t,n){this.#e=e,this.#t=t,this.#n=n;const{queries:r,fileRegexQueries:o}=c.#s(e,t,n);this.#r=r,this.#o=o}static fromPlainObject(e){return new c(e.query,e.ignoreCase,e.isRegex)}filePathMatchesFileQuery(e){return this.#o.every((({regex:t,shouldMatch:n})=>Boolean(e.match(t))===n))}queries(){return this.#r}query(){return this.#e}ignoreCase(){return this.#t}isRegex(){return this.#n}toPlainObject(){return{query:this.query(),ignoreCase:this.ignoreCase(),isRegex:this.isRegex()}}static#s(e,t,n){const r=/(\s*(?!-?f(ile)?:)[^\\ ]|\\.)+/,o=r.source+"(\\s+"+r.source+")*",s=["(\\s*"+u.source+"\\s*)","("+/"([^\\"]|\\.)+"/.source+")","("+o+")"].join("|"),i=new RegExp(s,"g"),a=e.match(i)||[],h=[],l=[];for(const e of a){if(!e)continue;const r=c.#i(e);if(r){const e=new RegExp(r.text,t?"i":"");l.push({regex:e,shouldMatch:r.shouldMatch})}else n?h.push(e):e.startsWith('"')&&e.endsWith('"')?h.push(c.#a(e)):h.push(c.#c(e))}return{queries:h,fileRegexQueries:l}}static#c(e){return e.replace(/\\(.)/g,"$1")}static#a(e){return e.substring(1,e.length-1).replace(/\\(.)/g,"$1")}static#i(e){const t=e.match(u);if(!t)return null;e=t[3];let r="";for(let t=0;t<e.length;++t){const o=e[t];if("*"===o)r+=".*";else if("\\"===o){++t;" "===e[t]&&(r+=" ")}else-1!==n.StringUtilities.regexSpecialCharacters().indexOf(e.charAt(t))&&(r+="\\"),r+=e.charAt(t)}return{text:r,shouldMatch:!Boolean(t[1])}}}const u=/(-)?f(ile)?:((?:[^\\ ]|\\.)+)/;var h,l=Object.freeze({__proto__:null,SearchConfig:c});!function(e){e.Debugger="debugger",e.Formatter="formatter",e.Inspector="inspector",e.Network="network",e.FileSystem="filesystem",e.ContentScripts="contentscripts",e.Service="service"}(h||(h={}));let d;class p extends e.ObjectWrapper.ObjectWrapper{projectsInternal;hasResourceContentTrackingExtensionsInternal;constructor(){super(),this.projectsInternal=new Map,this.hasResourceContentTrackingExtensionsInternal=!1}static instance(e={forceNew:null}){const{forceNew:t}=e;return d&&!t||(d=new p),d}static removeInstance(){d=void 0}uiSourceCode(e,t){const n=this.projectsInternal.get(e);return n?n.uiSourceCodeForURL(t):null}uiSourceCodeForURL(e){for(const t of this.projectsInternal.values()){const n=t.uiSourceCodeForURL(e);if(n)return n}return null}findCompatibleUISourceCodes(e){const t=e.url(),n=e.contentType(),r=[];for(const o of this.projectsInternal.values()){if(e.project().type()!==o.type())continue;const s=o.uiSourceCodeForURL(t);s&&s.url()===t&&s.contentType()===n&&r.push(s)}return r}uiSourceCodesForProjectType(e){const t=[];for(const n of this.projectsInternal.values())if(n.type()===e)for(const e of n.uiSourceCodes())t.push(e);return t}addProject(e){console.assert(!this.projectsInternal.has(e.id()),`A project with id ${e.id()} already exists!`),this.projectsInternal.set(e.id(),e),this.dispatchEventToListeners(C.ProjectAdded,e)}removeProject(e){this.projectsInternal.delete(e.id()),this.dispatchEventToListeners(C.ProjectRemoved,e)}project(e){return this.projectsInternal.get(e)||null}projects(){return[...this.projectsInternal.values()]}projectsForType(e){return this.projects().filter((function(t){return t.type()===e}))}uiSourceCodes(){const e=[];for(const t of this.projectsInternal.values())for(const n of t.uiSourceCodes())e.push(n);return e}setHasResourceContentTrackingExtensions(e){this.hasResourceContentTrackingExtensionsInternal=e}hasResourceContentTrackingExtensions(){return this.hasResourceContentTrackingExtensionsInternal}}var C;!function(e){e.UISourceCodeAdded="UISourceCodeAdded",e.UISourceCodeRemoved="UISourceCodeRemoved",e.UISourceCodeRenamed="UISourceCodeRenamed",e.WorkingCopyChanged="WorkingCopyChanged",e.WorkingCopyCommitted="WorkingCopyCommitted",e.WorkingCopyCommittedByUser="WorkingCopyCommittedByUser",e.ProjectAdded="ProjectAdded",e.ProjectRemoved="ProjectRemoved"}(C||(C={}));var m=Object.freeze({__proto__:null,get Events(){return C},ProjectStore:class{workspaceInternal;idInternal;typeInternal;displayNameInternal;#u;constructor(e,t,n,r){this.workspaceInternal=e,this.idInternal=t,this.typeInternal=n,this.displayNameInternal=r,this.#u=new Map}id(){return this.idInternal}type(){return this.typeInternal}displayName(){return this.displayNameInternal}workspace(){return this.workspaceInternal}createUISourceCode(e,t){return new I(this,e,t)}addUISourceCode(e){const t=e.url();return!this.uiSourceCodeForURL(t)&&(this.#u.set(t,e),this.workspaceInternal.dispatchEventToListeners(C.UISourceCodeAdded,e),!0)}removeUISourceCode(e){const t=this.#u.get(e);void 0!==t&&(this.#u.delete(e),this.workspaceInternal.dispatchEventToListeners(C.UISourceCodeRemoved,t))}removeProject(){this.workspaceInternal.removeProject(this),this.#u.clear()}uiSourceCodeForURL(e){return this.#u.get(e)??null}uiSourceCodes(){return this.#u.values()}renameUISourceCode(t,n){const r=t.url(),o=t.parentURL()?e.ParsedURL.ParsedURL.urlFromParentUrlAndName(t.parentURL(),n):e.ParsedURL.ParsedURL.preEncodeSpecialCharactersInPath(n);this.#u.set(o,t),this.#u.delete(r)}rename(e,t,n){}excludeFolder(e){}deleteFile(e){}deleteDirectoryRecursively(e){return Promise.resolve(!1)}remove(){}indexContent(e){}},WorkspaceImpl:p,get projectTypes(){return h}});const g={index:"(index)",thisFileWasChangedExternally:"This file was changed externally. Would you like to reload it?"},y=r.i18n.registerUIStrings("models/workspace/UISourceCode.ts",g),v=r.i18n.getLocalizedString.bind(void 0,y);class I extends e.ObjectWrapper.ObjectWrapper{#h;#l;#d;#p;#C;#m;#g=null;#y=new Map;#v=!1;#I=null;#k=null;#f=!1;#w=!1;#S=null;#R=null;#T=null;#U=!1;#L;#j=!1;#b=!1;#x=!1;constructor(t,r,o){super(),this.#d=t,this.#p=r;const s=e.ParsedURL.ParsedURL.fromString(r);s?(this.#h=s.securityOrigin(),this.#l=e.ParsedURL.ParsedURL.concatenate(this.#h,s.folderPathComponents),!s.queryParams||s.lastPathComponent&&o.isFromSourceMap()?this.#C=decodeURIComponent(s.lastPathComponent):this.#C=s.lastPathComponent+"?"+s.queryParams):(this.#h=n.DevToolsPath.EmptyUrlString,this.#l=n.DevToolsPath.EmptyUrlString,this.#C=r),this.#m=o}requestMetadata(){return this.#d.requestMetadata(this)}name(){return this.#C}mimeType(){return this.#d.mimeType(this)}url(){return this.#p}canonicalScriptId(){return`${this.#m.name()},${this.#p}`}parentURL(){return this.#l}origin(){return this.#h}fullDisplayName(){return this.#d.fullDisplayName(this)}displayName(e){if(!this.#C)return v(g.index);const t=this.#C;return e?t:n.StringUtilities.trimEndWithMaxLength(t,100)}canRename(){return this.#d.canRename()}rename(e){const{resolve:t,promise:n}=Promise.withResolvers();return this.#d.rename(this,e,function(e,n,r,o){e&&this.#P(n,r,o);t(e)}.bind(this)),n}remove(){this.#d.deleteFile(this)}#P(t,n,r){const o=this.#p;this.#C=t,this.#p=n||e.ParsedURL.ParsedURL.relativePathToUrlString(t,o),r&&(this.#m=r),this.dispatchEventToListeners(k.TitleChanged,this),this.project().workspace().dispatchEventToListeners(C.UISourceCodeRenamed,{oldURL:o,uiSourceCode:this})}contentURL(){return this.url()}contentType(){return this.#m}project(){return this.#d}requestContentData({cachedWasmOnly:e}={}){return this.#g?this.#g:this.#k?Promise.resolve(this.#k):e&&"application/wasm"===this.mimeType()?Promise.resolve(new o.WasmDisassembly.WasmDisassembly([],[],[])):(this.#g=this.#E(),this.#g)}async requestContent(e={}){return o.ContentData.ContentData.asDeferredContent(await this.requestContentData(e))}async#E(){if(this.#k)throw new Error("Called UISourceCode#requestContentImpl even though content is available for "+this.#p);try{this.#k=await this.#d.requestFileContent(this)}catch(e){this.#k={error:e?String(e):""}}return this.#k}#F(e){return e?e.isEncoded&&e.content?window.atob(e.content):e.content:null}#D(e){return!e||o.ContentData.ContentData.isError(e)?null:e.createdFromBase64?window.atob(e.base64):e.text}async checkContentUpdated(){if(!this.#k&&!this.#f)return;if(!this.#d.canSetFileContent()||this.#w)return;this.#w=!0;const t=o.ContentData.ContentData.asDeferredContent(await this.#d.requestFileContent(this));if("error"in t)return;if(this.#w=!1,null===t.content){const e=this.workingCopy();return this.#N("",!1),void this.setWorkingCopy(e)}if(this.#S===t.content)return;if(this.#D(this.#k)===this.#F(t))return void(this.#S=null);if(!this.isDirty()||this.#R===t.content)return void this.#N(t.content,!1);await e.Revealer.reveal(this),await new Promise((e=>window.setTimeout(e,0)));window.confirm(v(g.thisFileWasChangedExternally))?this.#N(t.content,!1):this.#S=t.content}forceLoadOnCheckContent(){this.#f=!0}#W(e){this.#d.canSetFileContent()&&this.#d.setFileContent(this,e,!1),this.#N(e,!0)}#N(e,t){this.#S=null,this.#k=new o.ContentData.ContentData(e,Boolean(this.#L),this.mimeType()),this.#g=null,this.#v=!0,this.#A();const n={uiSourceCode:this,content:e,encoded:this.#L};this.dispatchEventToListeners(k.WorkingCopyCommitted,n),this.#d.workspace().dispatchEventToListeners(C.WorkingCopyCommitted,n),t&&this.#d.workspace().dispatchEventToListeners(C.WorkingCopyCommittedByUser,n)}addRevision(e){this.#W(e)}hasCommits(){return this.#v}workingCopy(){return this.workingCopyContent().content||""}workingCopyContent(){return this.workingCopyContentData().asDeferedContent()}workingCopyContentData(){this.#T&&(this.#R=this.#T(),this.#T=null);const e=this.#k?o.ContentData.ContentData.contentDataOrEmpty(this.#k):o.ContentData.EMPTY_TEXT_CONTENT_DATA;return null!==this.#R?new o.ContentData.ContentData(this.#R,!1,e.mimeType):e}resetWorkingCopy(){this.#A(),this.#q()}#A(){this.#R=null,this.#T=null,this.setContainsAiChanges(!1)}setWorkingCopy(e){this.#R=e,this.#T=null,this.#q()}setContainsAiChanges(e){this.#x=e}containsAiChanges(){return this.#x}setContent(e,t){this.#L=t,this.#d.canSetFileContent()&&this.#d.setFileContent(this,e,t),this.#N(e,!0)}setWorkingCopyGetter(e){this.#T=e,this.#q()}#q(){this.#M(),this.dispatchEventToListeners(k.WorkingCopyChanged,this),this.#d.workspace().dispatchEventToListeners(C.WorkingCopyChanged,{uiSourceCode:this})}removeWorkingCopyGetter(){this.#T&&(this.#R=this.#T(),this.#T=null)}commitWorkingCopy(){this.isDirty()&&this.#W(this.workingCopy())}isDirty(){return null!==this.#R||null!==this.#T}isKnownThirdParty(){return this.#j}markKnownThirdParty(){this.#j=!0}isUnconditionallyIgnoreListed(){return this.#b}isFetchXHR(){return[e.ResourceType.resourceTypes.XHR,e.ResourceType.resourceTypes.Fetch].includes(this.contentType())}markAsUnconditionallyIgnoreListed(){this.#b=!0}extension(){return e.ParsedURL.ParsedURL.extractExtension(this.#C)}content(){return!this.#k||"error"in this.#k?"":this.#k.text}loadError(){return this.#k&&"error"in this.#k&&this.#k.error||null}searchInContent(e,t,n){return!this.#k||"error"in this.#k?this.#d.searchInFileContent(this,e,t,n):Promise.resolve(o.TextUtils.performSearchInContentData(this.#k,e,t,n))}contentLoaded(){return Boolean(this.#k)}uiLocation(e,t){return new f(this,e,t)}messages(){return this.#I?new Set(this.#I):new Set}addLineMessage(e,t,n,r,s){const i=o.TextRange.TextRange.createFromLocation(n,r||0),a=new w(e,t,s,i);return this.addMessage(a),a}addMessage(e){this.#I||(this.#I=new Set),this.#I.add(e),this.dispatchEventToListeners(k.MessageAdded,e)}removeMessage(e){this.#I?.delete(e)&&this.dispatchEventToListeners(k.MessageRemoved,e)}#M(){if(this.#I){for(const e of this.#I)this.dispatchEventToListeners(k.MessageRemoved,e);this.#I=null}}setDecorationData(e,t){t!==this.#y.get(e)&&(this.#y.set(e,t),this.dispatchEventToListeners(k.DecorationChanged,e))}getDecorationData(e){return this.#y.get(e)}disableEdit(){this.#U=!0}editDisabled(){return this.#U}}var k;!function(e){e.WorkingCopyChanged="WorkingCopyChanged",e.WorkingCopyCommitted="WorkingCopyCommitted",e.TitleChanged="TitleChanged",e.MessageAdded="MessageAdded",e.MessageRemoved="MessageRemoved",e.DecorationChanged="DecorationChanged"}(k||(k={}));class f{uiSourceCode;lineNumber;columnNumber;constructor(e,t,n){this.uiSourceCode=e,this.lineNumber=t,this.columnNumber=n}linkText(e=!1,t=!1){const n=this.uiSourceCode.displayName(e),r=this.lineAndColumnText(t);let o=r?n+":"+r:n;return this.uiSourceCode.isDirty()&&(o="*"+o),o}lineAndColumnText(e=!1){let t;return"application/wasm"===this.uiSourceCode.mimeType()?"number"==typeof this.columnNumber&&(t=`0x${this.columnNumber.toString(16)}`):(t=`${this.lineNumber+1}`,e&&"number"==typeof this.columnNumber&&(t+=":"+(this.columnNumber+1))),t}id(){return"number"==typeof this.columnNumber?this.uiSourceCode.project().id()+":"+this.uiSourceCode.url()+":"+this.lineNumber+":"+this.columnNumber:this.lineId()}lineId(){return this.uiSourceCode.project().id()+":"+this.uiSourceCode.url()+":"+this.lineNumber}static comparator(e,t){return e.compareTo(t)}compareTo(e){return this.uiSourceCode.url()!==e.uiSourceCode.url()?this.uiSourceCode.url()>e.uiSourceCode.url()?1:-1:this.lineNumber!==e.lineNumber?this.lineNumber-e.lineNumber:this.columnNumber===e.columnNumber?0:"number"!=typeof this.columnNumber?-1:"number"!=typeof e.columnNumber?1:this.columnNumber-e.columnNumber}}class w{levelInternal;textInternal;range;clickHandlerInternal;constructor(e,t,n,r){this.levelInternal=e,this.textInternal=t,this.range=r??new o.TextRange.TextRange(0,0,0,0),this.clickHandlerInternal=n}level(){return this.levelInternal}text(){return this.textInternal}clickHandler(){return this.clickHandlerInternal}lineNumber(){return this.range.startLine}columnNumber(){return this.range.startColumn}isEqual(e){return this.text()===e.text()&&this.level()===e.level()&&this.range.equal(e.range)}}var S=Object.freeze({__proto__:null,get Events(){return k},Message:w,UILocation:f,UILocationRange:class{uiSourceCode;range;constructor(e,t){this.uiSourceCode=e,this.range=t}},UISourceCode:I,UISourceCodeMetadata:class{modificationTime;contentSize;constructor(e,t){this.modificationTime=e,this.contentSize=t}}});export{a as FileManager,l as SearchConfig,S as UISourceCode,m as Workspace};
