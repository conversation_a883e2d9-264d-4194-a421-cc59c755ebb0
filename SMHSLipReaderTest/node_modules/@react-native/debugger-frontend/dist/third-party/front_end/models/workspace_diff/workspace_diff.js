import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as i from"../../third_party/diff/diff.js";import*as o from"../formatter/formatter.js";import*as s from"../persistence/persistence.js";import*as r from"../text_utils/text_utils.js";import*as n from"../workspace/workspace.js";class d extends e.ObjectWrapper.ObjectWrapper{#e=s.Persistence.PersistenceImpl.instance();#t=new WeakMap;loadingUISourceCodes=new Map;#i=new Set;constructor(e){super(),e.addEventListener(n.Workspace.Events.WorkingCopyChanged,this.uiSourceCodeChanged,this),e.addEventListener(n.Workspace.Events.WorkingCopyCommitted,this.uiSourceCodeChanged,this),e.addEventListener(n.Workspace.Events.UISourceCodeAdded,this.uiSourceCodeAdded,this),e.addEventListener(n.Workspace.Events.UISourceCodeRemoved,this.uiSourceCodeRemoved,this),e.addEventListener(n.Workspace.Events.ProjectRemoved,this.projectRemoved,this),e.uiSourceCodes().forEach(this.updateModifiedState.bind(this))}requestDiff(e){return this.uiSourceCodeDiff(e).requestDiff()}subscribeToDiffChange(e,t,i){this.uiSourceCodeDiff(e).addEventListener("DiffChanged",t,i)}unsubscribeFromDiffChange(e,t,i){this.uiSourceCodeDiff(e).removeEventListener("DiffChanged",t,i)}modifiedUISourceCodes(){return Array.from(this.#i)}uiSourceCodeDiff(e){let t=this.#t.get(e);return t||(t=new u(e),this.#t.set(e,t)),t}uiSourceCodeChanged(e){const t=e.data.uiSourceCode;this.updateModifiedState(t)}uiSourceCodeAdded(e){const t=e.data;this.updateModifiedState(t)}uiSourceCodeRemoved(e){const t=e.data;this.removeUISourceCode(t)}projectRemoved(e){const t=e.data;for(const e of t.uiSourceCodes())this.removeUISourceCode(e)}removeUISourceCode(e){this.loadingUISourceCodes.delete(e);const t=this.#t.get(e);t&&(t.dispose=!0),this.markAsUnmodified(e)}markAsUnmodified(e){this.uiSourceCodeProcessedForTest(),this.#i.delete(e)&&this.dispatchEventToListeners("ModifiedStatusChanged",{uiSourceCode:e,isModified:!1})}markAsModified(e){this.uiSourceCodeProcessedForTest(),this.#i.has(e)||(this.#i.add(e),this.dispatchEventToListeners("ModifiedStatusChanged",{uiSourceCode:e,isModified:!0}))}uiSourceCodeProcessedForTest(){}#o(e){switch(e.project().type()){case n.Workspace.projectTypes.Network:return!0;case n.Workspace.projectTypes.FileSystem:return null===this.#e.binding(e);case n.Workspace.projectTypes.Inspector:return!0;default:return!1}}async updateModifiedState(e){if(this.loadingUISourceCodes.delete(e),!this.#o(e))return void this.markAsUnmodified(e);if(e.isDirty())return void this.markAsModified(e);if(!e.hasCommits())return void this.markAsUnmodified(e);const t=Promise.all([this.requestOriginalContentForUISourceCode(e),e.requestContent().then((e=>e.content))]);this.loadingUISourceCodes.set(e,t);const i=await t;this.loadingUISourceCodes.get(e)===t&&(this.loadingUISourceCodes.delete(e),null!==i[0]&&null!==i[1]&&i[0]!==i[1]?this.markAsModified(e):this.markAsUnmodified(e))}requestOriginalContentForUISourceCode(e){return this.uiSourceCodeDiff(e).originalContent()}revertToOriginal(e){return t.userMetrics.actionTaken(t.UserMetrics.Action.RevisionApplied),this.requestOriginalContentForUISourceCode(e).then((function(t){"string"==typeof t&&e.addRevision(t)}))}}class u extends e.ObjectWrapper.ObjectWrapper{uiSourceCode;requestDiffPromise;pendingChanges;dispose;constructor(e){super(),this.uiSourceCode=e,e.addEventListener(n.UISourceCode.Events.WorkingCopyChanged,this.uiSourceCodeChanged,this),e.addEventListener(n.UISourceCode.Events.WorkingCopyCommitted,this.uiSourceCodeChanged,this),this.requestDiffPromise=null,this.pendingChanges=null,this.dispose=!1}uiSourceCodeChanged(){this.pendingChanges&&(clearTimeout(this.pendingChanges),this.pendingChanges=null),this.requestDiffPromise=null;const e=this.uiSourceCode.content(),t=!e||e.length<65536?0:c;this.pendingChanges=window.setTimeout(function(){if(this.dispose)return;this.dispatchEventToListeners("DiffChanged"),this.pendingChanges=null}.bind(this),t)}requestDiff(){return this.requestDiffPromise||(this.requestDiffPromise=this.innerRequestDiff()),this.requestDiffPromise}async originalContent(){const e=s.NetworkPersistenceManager.NetworkPersistenceManager.instance().originalContentForUISourceCode(this.uiSourceCode);if(e)return await e;const t=await this.uiSourceCode.project().requestFileContent(this.uiSourceCode);return r.ContentData.ContentData.isError(t)?t.error:t.asDeferedContent().content}async innerRequestDiff(){if(this.dispose)return null;let e=await this.originalContent();if(null===e)return null;if(e.length>1048576)return null;if(this.dispose)return null;let t=this.uiSourceCode.workingCopy();if(t||this.uiSourceCode.contentLoaded()||(t=(await this.uiSourceCode.requestContent()).content),t.length>1048576)return null;if(this.dispose)return null;e=(await o.ScriptFormatter.format(this.uiSourceCode.contentType(),this.uiSourceCode.mimeType(),e)).formattedContent;const s=await o.ScriptFormatter.format(this.uiSourceCode.contentType(),this.uiSourceCode.mimeType(),t);t=s.formattedContent;const r=s.formattedMapping,n=/\r\n?|\n/;return{diff:i.Diff.DiffWrapper.lineDiff(e.split(n),t.split(n)),formattedCurrentMapping:r}}}let a=null;const c=200;var f=Object.freeze({__proto__:null,UISourceCodeDiff:u,UpdateTimeout:c,WorkspaceDiffImpl:d,workspaceDiff:function({forceNew:e}={}){return a&&!e||(a=new d(n.Workspace.WorkspaceImpl.instance())),a}});export{f as WorkspaceDiff};
