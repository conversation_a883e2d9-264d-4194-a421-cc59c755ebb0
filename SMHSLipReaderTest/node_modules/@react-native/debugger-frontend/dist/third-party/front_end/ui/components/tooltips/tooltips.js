import*as t from"../../lit/lit.js";import*as e from"../../visual_logging/visual_logging.js";var o=`:host{--tooltip-viewport-distance:var(--sys-size-5);margin:0;background:none;border:none;padding:0;overflow:visible;position-area:bottom;position-visibility:anchors-visible;justify-self:anchor-center;position-try-fallbacks:flip-block;& .container{width:max-content;margin:var(--sys-size-2);font:var(--sys-typescale-body4-regular);color:var(--sys-color-inverse-on-surface);background-color:var(--sys-color-inverse-surface);box-shadow:var(--sys-elevation-level2);border-radius:var(--sys-shape-corner-extra-small);padding:var(--sys-size-4) var(--sys-size-5)}}:host([variant='rich']){justify-self:unset;margin:0 var(--tooltip-viewport-distance) var(--tooltip-viewport-distance) 0;position-area:bottom span-right;position-try-fallbacks:--bottom-left,--bottom-center,--top-right,--top-left,--top-center;& .container{max-width:calc(100vw - 2 * (var(--tooltip-viewport-distance) + var(--sys-size-8) + var(--sys-size-3)));margin-inline:0;margin-block:var(--sys-size-3);color:var(--sys-color-on-surface);background-color:var(--sys-color-base-container-elevated);border-radius:var(--sys-shape-corner-small);padding:var(--sys-size-6) var(--sys-size-8);overflow:auto}}@position-try --bottom-left{position-area:bottom span-left;margin:0 0 var(--tooltip-viewport-distance) var(--tooltip-viewport-distance)}@position-try --top-right{position-area:top span-right;margin:var(--tooltip-viewport-distance) var(--tooltip-viewport-distance) 0 0}@position-try --top-left{position-area:top span-left;margin:var(--tooltip-viewport-distance) 0 0 var(--tooltip-viewport-distance)}@position-try --bottom-center{justify-self:anchor-center;position-area:bottom;margin:0 var(--tooltip-viewport-distance) var(--tooltip-viewport-distance) var(--tooltip-viewport-distance)}@position-try --top-center{justify-self:anchor-center;position-area:top;margin:var(--tooltip-viewport-distance) var(--tooltip-viewport-distance) 0 var(--tooltip-viewport-distance)}\n/*# sourceURL=${import.meta.resolve("./tooltip.css")} */\n`;const{html:i}=t;class s extends HTMLElement{static observedAttributes=["id","variant","jslogcontext"];static lastOpenedTooltipId=null;#t=this.attachShadow({mode:"open"});#e=null;#o=null;#i=!1;#s=null;get open(){return this.matches(":popover-open")}get useHotkey(){return this.hasAttribute("use-hotkey")??!1}set useHotkey(t){t?this.setAttribute("use-hotkey",""):this.removeAttribute("use-hotkey")}get useClick(){return this.hasAttribute("use-click")??!1}set useClick(t){t?this.setAttribute("use-click",""):this.removeAttribute("use-click")}get hoverDelay(){return this.hasAttribute("hover-delay")?Number(this.getAttribute("hover-delay")):200}set hoverDelay(t){this.setAttribute("hover-delay",t.toString())}get variant(){return"rich"===this.getAttribute("variant")?"rich":"simple"}set variant(t){this.setAttribute("variant",t)}get jslogContext(){return this.getAttribute("jslogcontext")}set jslogContext(t){this.setAttribute("jslogcontext",t),this.#r()}get anchor(){return this.#e}constructor(t){if(super(),t&&(this.id=t.id),t?.variant&&(this.variant=t.variant),t?.jslogContext&&(this.jslogContext=t.jslogContext),t?.anchor){if((t.anchor.getAttribute("aria-details")??t.anchor.getAttribute("aria-describedby"))!==t.id)throw new Error("aria-details or aria-describedby must be set on the anchor");this.#e=t.anchor}}attributeChangedCallback(t,e,o){this.isConnected&&("id"===t?(this.#n(),this.#a(),s.lastOpenedTooltipId===e&&(s.lastOpenedTooltipId=o)):"jslogcontext"===t&&this.#r())}connectedCallback(){this.#a(),this.#h(),this.#l(),t.render(i`
      <style>${o}</style>
      <!-- Wrapping it into a container, so that the tooltip doesn't disappear when the mouse moves from the anchor to the tooltip. -->
      <div class="container">
        <slot></slot>
      </div>
    `,this.#t,{host:this}),s.lastOpenedTooltipId===this.id&&this.showPopover()}disconnectedCallback(){this.#n(),this.#s?.disconnect()}showTooltip=t=>{t&&"buttons"in t&&t.buttons||(this.#o&&window.clearTimeout(this.#o),this.#o=window.setTimeout((()=>{this.showPopover(),s.lastOpenedTooltipId=this.id}),this.hoverDelay))};hideTooltip=t=>{this.#o&&window.clearTimeout(this.#o),t&&"rich"===this.variant&&t.target===this.#e&&t.relatedTarget instanceof Node&&this.contains(t.relatedTarget)||(!t||"rich"!==this.variant||t.relatedTarget!==this&&t.relatedTarget?.parentElement!==this)&&(this.open&&s.lastOpenedTooltipId===this.id&&(s.lastOpenedTooltipId=null),this.#o=window.setTimeout((()=>{this.hidePopover()}),this.hoverDelay))};toggle=()=>{this.#i||this.togglePopover()};#r(){this.jslogContext&&this.#e?(e.setMappedParent(this,this.#e),this.setAttribute("jslog",e.popover(this.jslogContext).parent("mapped").toString())):this.removeAttribute("jslog")}#l(){this.hasAttribute("role")||this.setAttribute("role","tooltip"),this.setAttribute("popover",this.useClick?"auto":"manual"),this.#r()}#c(t){t.stopPropagation()}#p=t=>{"closed"===t.newState&&(this.#i=!0)};#d=t=>{"closed"===t.newState&&(this.#i=!1)};#v=t=>{(t.altKey&&"ArrowDown"===t.key||"Escape"===t.key&&this.open)&&(this.toggle(),t.consume(!0))};#h(){this.#e&&(this.useClick?this.#e.addEventListener("click",this.toggle):(this.#e.addEventListener("mouseenter",this.showTooltip),this.useHotkey?this.#e.addEventListener("keydown",this.#v):this.#e.addEventListener("focus",this.showTooltip),this.#e.addEventListener("blur",this.hideTooltip),this.#e.addEventListener("mouseleave",this.hideTooltip),this.addEventListener("mouseleave",this.hideTooltip))),this.addEventListener("click",this.#c),this.addEventListener("mouseup",this.#c),this.addEventListener("beforetoggle",this.#p),this.addEventListener("toggle",this.#d)}#n(){this.#o&&window.clearTimeout(this.#o),this.#e&&(this.#e.removeEventListener("click",this.toggle),this.#e.removeEventListener("mouseenter",this.showTooltip),this.#e.removeEventListener("focus",this.showTooltip),this.#e.removeEventListener("blur",this.hideTooltip),this.#e.removeEventListener("keydown",this.#v),this.#e.removeEventListener("mouseleave",this.hideTooltip)),this.removeEventListener("mouseleave",this.hideTooltip),this.removeEventListener("click",this.#c),this.removeEventListener("mouseup",this.#c),this.removeEventListener("beforetoggle",this.#p),this.removeEventListener("toggle",this.#d)}#a(){if(!this.#e){const t=this.getAttribute("id");if(!t)throw new Error("<devtools-tooltip> must have an id.");const e=this.getRootNode();if(e.querySelectorAll(`#${t}`)?.length>1)throw new Error("Duplicate <devtools-tooltip> ids found.");const o=e.querySelector(`[aria-describedby="${t}"]`),i=e.querySelector(`[aria-details="${t}"]`),s=o??i;if(!s)throw new Error(`No anchor for tooltip with id ${t} found.`);if(!(s instanceof HTMLElement))throw new Error("Anchor must be an HTMLElement.");this.#e=s,"rich"===this.variant&&o&&console.warn(`The anchor for tooltip ${t} was defined with "aria-describedby". For rich tooltips "aria-details" is more appropriate.`)}const t=`--devtools-tooltip-${this.id}-anchor`;this.#e.style.anchorName=t,this.style.positionAnchor=t,this.#u(this.#e),this.#r()}#u(t){null!==t.parentElement&&(this.#s&&this.#s.disconnect(),this.#s=new MutationObserver((e=>{for(const o of e)"childList"===o.type&&[...o.removedNodes].includes(t)&&(this.#o&&window.clearTimeout(this.#o),this.hidePopover())})),this.#s.observe(t.parentElement,{childList:!0}))}}customElements.define("devtools-tooltip",s);var r=Object.freeze({__proto__:null,Tooltip:s});export{r as Tooltip};
