import*as e from"../../../core/common/common.js";import*as t from"../render_coordinator/render_coordinator.js";import{render as o,html as r}from"../../lit/lit.js";var s={cssText:`:host{position:absolute;left:0;top:0;width:100%;height:100%}.gamut-line{stroke:color-mix(in srgb,var(--ref-palette-neutral100) 50%,transparent);fill:none}.label{position:absolute;bottom:3px;margin-right:5px;color:color-mix(in srgb,var(--ref-palette-neutral100) 50%,transparent)}\n/*# sourceURL=${import.meta.resolve("./srgbOverlay.css")} */\n`};const n=new CSSStyleSheet;n.replaceSync(s.cssText);function i(t){const o=e.Color.hsva2rgba([...t,1]),r=e.ColorConverter.ColorConverter.displayP3ToXyzd50(o[0],o[1],o[2]);return e.ColorConverter.ColorConverter.xyzd50ToSrgb(r[0],r[1],r[2]).every((e=>e+.001>=0&&e-.001<=1))}class l extends HTMLElement{#e=this.attachShadow({mode:"open"});constructor(){super(),this.#e.adoptedStyleSheets=[n]}#t({hue:e,width:t,height:o}){if(0===t||0===o)return null;const r=1/window.devicePixelRatio,s=[];let n=0;for(let l=0;l<o;l+=r){const a=1-l/o;for(;n<t;n+=r){if(!i([e,n/t,a])){s.push({x:n,y:l});break}}}if(0===s.length)return null;const l=s[s.length-1];return l.x<t&&s.push({y:l.y,x:t}),s}#o(e,t){let o=1/0,r=null;for(const s of e)Math.abs(t-s.y)<=o&&(o=Math.abs(t-s.y),r=s);return r}render({hue:e,width:s,height:n}){return t.write("Srgb Overlay render",(()=>{const t=this.#t({hue:e,width:s,height:n});if(!t||0===t.length)return;const i=this.#o(t,n-13);i&&o(r`
          <span class="label" style="right: ${s-i.x}px">sRGB</span>
          <svg>
            <polyline points=${t.map((e=>`${e.x.toFixed(2)},${e.y.toFixed(2)}`)).join(" ")} class="gamut-line" />
          </svg>
        `,this.#e,{host:this})}))}}customElements.define("devtools-spectrum-srgb-overlay",l);var a=Object.freeze({__proto__:null,SrgbOverlay:l});export{a as SrgbOverlay};
