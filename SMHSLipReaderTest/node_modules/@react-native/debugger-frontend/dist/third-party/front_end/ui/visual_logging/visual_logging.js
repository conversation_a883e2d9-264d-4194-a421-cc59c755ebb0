import*as e from"../../core/host/host.js";import*as t from"../../core/root/root.js";import*as i from"../../core/common/common.js";import*as o from"../components/render_coordinator/render_coordinator.js";import{assertNotNullOrUndefined as r}from"../../core/platform/platform.js";const n=new Set(["%","*","-alternative-animation-with-timeline","-alternative-position-try","-epub-caption-side","-epub-text-combine","-epub-text-emphasis","-epub-text-emphasis-color","-epub-text-emphasis-style","-epub-text-orientation","-epub-text-transform","-epub-word-break","-epub-writing-mode","-moz-box-direction","-moz-box-orient","-moz-box-sizing","-moz-osx-font-smoothing","-moz-text-decoration","-moz-text-size-adjust","-ms-flex-direction","-ms-text-size-adjust","-ms-touch-action","-webkit-FONT-smoothing","-webkit-align-content","-webkit-align-items","-webkit-align-self","-webkit-alternative-animation-with-timeline","-webkit-animation","-webkit-animation-delay","-webkit-animation-direction","-webkit-animation-duration","-webkit-animation-fill-mode","-webkit-animation-iteration-count","-webkit-animation-name","-webkit-animation-play-state","-webkit-animation-timing-function","-webkit-app-region","-webkit-appearance","-webkit-backface-visibility","-webkit-background-clip","-webkit-background-origin","-webkit-background-size","-webkit-border-after","-webkit-border-after-color","-webkit-border-after-style","-webkit-border-after-width","-webkit-border-before","-webkit-border-before-color","-webkit-border-before-style","-webkit-border-before-width","-webkit-border-bottom-left-radius","-webkit-border-bottom-right-radius","-webkit-border-end","-webkit-border-end-color","-webkit-border-end-style","-webkit-border-end-width","-webkit-border-horizontal-spacing","-webkit-border-image","-webkit-border-radius","-webkit-border-start","-webkit-border-start-color","-webkit-border-start-style","-webkit-border-start-width","-webkit-border-top-left-radius","-webkit-border-top-right-radius","-webkit-border-vertical-spacing","-webkit-box-align","-webkit-box-decoration-break","-webkit-box-direction","-webkit-box-flex","-webkit-box-ordinal-group","-webkit-box-orient","-webkit-box-pack","-webkit-box-reflect","-webkit-box-shadow","-webkit-box-sizing","-webkit-clip-path","-webkit-column-break-after","-webkit-column-break-before","-webkit-column-break-inside","-webkit-column-count","-webkit-column-gap","-webkit-column-rule","-webkit-column-rule-color","-webkit-column-rule-style","-webkit-column-rule-width","-webkit-column-span","-webkit-column-width","-webkit-columns","-webkit-filter","-webkit-flex","-webkit-flex-basis","-webkit-flex-direction","-webkit-flex-flow","-webkit-flex-grow","-webkit-flex-shrink","-webkit-flex-wrap","-webkit-flow-into","-webkit-font-feature-settings","-webkit-font-smoothing","-webkit-hyphenate-character","-webkit-justify-content","-webkit-line-break","-webkit-line-clamp","-webkit-locale","-webkit-logical-height","-webkit-logical-width","-webkit-margin-after","-webkit-margin-before","-webkit-margin-end","-webkit-margin-start","-webkit-mask","-webkit-mask-box-image","-webkit-mask-box-image-outset","-webkit-mask-box-image-repeat","-webkit-mask-box-image-slice","-webkit-mask-box-image-source","-webkit-mask-box-image-width","-webkit-mask-clip","-webkit-mask-composite","-webkit-mask-image","-webkit-mask-origin","-webkit-mask-position","-webkit-mask-position-x","-webkit-mask-position-y","-webkit-mask-repeat","-webkit-mask-size","-webkit-max-logical-height","-webkit-max-logical-width","-webkit-min-logical-height","-webkit-min-logical-width","-webkit-opacity","-webkit-order","-webkit-padding-after","-webkit-padding-before","-webkit-padding-end","-webkit-padding-start","-webkit-perspective","-webkit-perspective-origin","-webkit-perspective-origin-x","-webkit-perspective-origin-y","-webkit-print-color-adjust","-webkit-rtl-ordering","-webkit-ruby-position","-webkit-shape-image-threshold","-webkit-shape-margin","-webkit-shape-outside","-webkit-tap-highlight-color","-webkit-text-combine","-webkit-text-decoration","-webkit-text-decorations-in-effect","-webkit-text-emphasis","-webkit-text-emphasis-color","-webkit-text-emphasis-position","-webkit-text-emphasis-style","-webkit-text-fill-color","-webkit-text-orientation","-webkit-text-security","-webkit-text-size-adjust","-webkit-text-stroke","-webkit-text-stroke-color","-webkit-text-stroke-width","-webkit-transform","-webkit-transform-origin","-webkit-transform-origin-x","-webkit-transform-origin-y","-webkit-transform-origin-z","-webkit-transform-style","-webkit-transition","-webkit-transition-delay","-webkit-transition-duration","-webkit-transition-property","-webkit-transition-timing-function","-webkit-user-drag","-webkit-user-modify","-webkit-user-select","-webkit-writing-mode","-x","0","1","10","100%","11","12","125%","13","14","15","150%","16","17","18","19","2","20","200%","3","3g","4","42","44","5","50%","6","7","75%","8","9","ArrowDown","ArrowLeft","ArrowRight","ArrowUp","Backspace","CSS","CoLoR","Delete","Document","Documents","Enter","Escape","Fetch and XHR","FetchandXHR","Font","Frames","ID","Id","Image","InspectorView.panelOrder","JSEventListeners","JSHeapTotalSize","JavaScript","LayoutCount","Manifest","Media","NAME","Nodes","Other","PROPERTY","PageDown","PageUp","PrefixedStorageInfo","RecalcStyleCount","Space","Tab","TaskDuration","WebAssembly","WebSocket","a","aa","aaa","abort","abort-replay","aborted","about-gpu","accent-color","accept","accept-execute-code","access-control-allow-origin","accessibility-tree","accessibility.view","achromatopsia","action","action-button","actions","activation-value","active","active-keybind-set","activity","ad","ad-script","ad-status","add","add-3p-scripts-to-ignorelist","add-anonymous-scripts-to-ignorelist","add-asserted-events","add-assertion","add-attribute","add-attribute-assertion","add-attributes","add-breakpoint","add-button","add-cnd-breakpoint","add-color","add-content-scripts-to-ignorelist","add-count","add-custom-device","add-debug-info-url","add-device-type","add-directory-to-ignore-list","add-duration","add-folder","add-frame","add-header","add-logpoint","add-new","add-operator","add-origin-mapping","add-properties","add-property-path-to-watch","add-script-to-ignorelist","add-selector","add-selector-part","add-shortcut","add-source-map","add-source-map-url","add-step-after","add-step-before","add-target","add-timeout","add-to-ignore-list","add-visible","add-wasm-debug-info","add-watch-expression","add-x","add-y","added-count","added-size","addedSize","additive-symbols","adorner-settings","af","affected-cookies","affected-documents","affected-elements","affected-raw-cookies","affected-requests","affected-sources","ai-annotations-enabled","ai-assistance-enabled","ai-assistance-history-entries","ai-assistance-history-images","ai-assistance-patching-selected-project-id","ai_assistance","align-content","align-content-center","align-content-flex-end","align-content-flex-start","align-content-space-around","align-content-space-between","align-content-space-evenly","align-content-stretch","align-items","align-items-baseline","align-items-center","align-items-end","align-items-flex-end","align-items-flex-start","align-items-start","align-items-stretch","align-self","alignment-baseline","all","allocation-stack","allow-pasting","allow-scroll-past-eof","allow-scroll-past-eof-false","allowed","allowed-by-exception","alpha","alt-!","alt-:","alt->","alt-?","alt-@","alt-arrowdown","alt-arrowleft","alt-arrowright","alt-arrowup","alt-backspace","alt-delete","alt-end","alt-enter","alt-escape","alt-home","alt-meta-!","alt-meta-:","alt-meta->","alt-meta-?","alt-meta-@","alt-meta-arrowdown","alt-meta-arrowleft","alt-meta-arrowright","alt-meta-arrowup","alt-meta-backspace","alt-meta-delete","alt-meta-end","alt-meta-enter","alt-meta-escape","alt-meta-home","alt-meta-pagedown","alt-meta-pageup","alt-meta-tab","alt-pagedown","alt-pageup","alt-tab","am","anchor-link","anchor-name","anchor-scope","android-2.3-browser-nexus-s","android-4.0.2-browser-galaxy-nexus","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","animation-trigger","animation-trigger-exit-range","animation-trigger-exit-range-end","animation-trigger-exit-range-start","animation-trigger-range","animation-trigger-range-end","animation-trigger-range-start","animation-trigger-timeline","animation-trigger-type","animations","animations.buffer-preview","animations.clear","animations.grid-header","animations.keyframe","animations.pause-resume-all","animations.play-replay-pause-animation-group","animations.playback-rate-10","animations.playback-rate-100","animations.playback-rate-25","animations.remove-preview","annotations-hidden","another_id","answer","apca","apca-documentation","app-region","appearance","application","apply-to-page-tree","ar","architecture","aria-attributes","arial","as","ascent-override","aspect-ratio","asserted-events","asus-zenbook-fold","attribute","attribute-modified","attributes","attribution-reporting-details","auction-worklet","audio-context","audio-context-closed","audio-context-created","audio-context-resumed","audio-context-suspended","authenticator","authored","authored-deployed-grouping","authored-deployed-grouping-documentation","authored-deployed-grouping-feedback","auto","auto-adjust-zoom","auto-attach-to-created-pages","auto-attach-to-created-pages-true","auto-focus-on-debugger-paused-enabled","auto-focus-on-debugger-paused-enabled-false","auto-open-autofill-view-on-event","auto-pretty-print-minified","auto-pretty-print-minified-false","auto-reveal-in-navigator","auto-reveal-in-navigator-false","autofill","autofill-empty","autofill-type","autofill-view","autofill-view-documentation","autofill-view-feedback","automatic-workspace-folders.connect","automatically-ignore-list-known-third-party-scripts","auxclick","avif-format-disabled","avif-format-disabled-true","az","back","back-forward-cache","back-forward-cache.run-test","backdrop-filter","backface-visibility","background /* color: red */","background","background-attachment","background-blend-mode","background-clip","background-color","background-fetch","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-service.clear","background-service.save-events","background-service.toggle-recording","background-services","background-size","background-sync","badproperty","bars","base-64","base-palette","baseline-shift","baseline-source","be","before-bidder-worklet-bidding-start","before-bidder-worklet-reporting-start","before-seller-worklet-reporting-start","before-seller-worklet-scoring-start","beforecopy","beforecut","beforepaste","beforeunload","beta","bezier","bezier.control-circle","bezier.linear-control-circle","bezier.next-preset","bezier.prev-preset","bezierEditor","bfcache","bg","big-endian","binary-view-type","black-berry-9900","black-berry-bb-10","black-berry-play-book-2.1","blackbox","ble","block-request-domain","block-request-url","block-size","blocked","blocked-by-response-details","blocking","blur","blurred-vision","bn","body","bold","bolder","bookman","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","bottom-up","bounce-tracking-mitigations","box-decoration-break","box-shadow","box-sizing","br","brand-name","brand-version","breadcrumbs","break-after","break-before","break-inside","break-on","breakpoint","breakpoint-group","breakpoints-active","browser-debugger.refresh-global-event-listeners","browser-language","bs","buffered-rendering","button","bypass-service-worker","ca","cache-control","cache-disabled","cache-disabled-true","cache-storage","cache-storage-data","cache-storage-instance","cache-storage-view-tab","cache-storage.delete-selected","cache-storage.refresh","calibrated-cpu-throttling","call-tree","cancel","cancel-animation-frame","cancel-cross-origin-context-chat","candara","canplay","canplaythrough","canvas","canvas-context-created","caption-side","capture-node-creation-stacks","caret-animation","caret-color","category","change","change-workspace","change-workspace-dialog","changes","changes.changes","changes.copy","changes.reveal-source","changes.revert","checked","chevron-left","chevron-right","chrome-ai","chrome-android-mobile","chrome-android-mobile-high-end","chrome-android-tablet","chrome-chrome-os","chrome-extension://fmkadmapgofadopljbjfkapdkoienihi⚛️Components","chrome-extension://fmkadmapgofadopljbjfkapdkoienihi⚛️Profiler","chrome-extension://ienfalfjdbdpebioblfackkekamfmbnhAngular","chrome-extensions","chrome-flags-bounce-tracking-mitigations","chrome-flags-tpcd-heuristics-grants","chrome-flags-tpcd-metadata-grants","chrome-i-pad","chrome-i-phone","chrome-mac","chrome-recorder","chrome-recorder.create-recording","chrome-recorder.replay-recording","chrome-recorder.start-recording","chrome-recorder.toggle-code-view","chrome-settings","chrome-settings-performance","chrome-settings-sync-setup","chrome-settings-sync-setup-advanced","chrome-theme-colors","chrome-theme-colors-documentation","chrome-theme-colors-false","chrome-windows","city","classic","clear","clear-all","clear-browser-cache","clear-browser-cookies","clear-filter","clear-input","clear-interval","clear-palette","clear-replace-input","clear-search-input","clear-storage","clear-storage-cache-storage","clear-storage-cookies","clear-storage-include-third-party-cookies","clear-storage-indexeddb","clear-storage-local-storage","clear-storage-service-workers","clear-storage-websql","clear-timeout","click","client-focus","clip","clip-path","clip-rule","clipboard","clipped-color","close","close-all","close-dev-tools","close-others","close-search","close-tabs-to-the-right","closeable-tabs","closeableTabs","code","code-disclaimer","code-format","code-snippets-explainer.console-insights","code-snippets-explainer.freestyler","code-snippets-explainer.patch-widget","collapse","collapse-children","collapsed-files","color","color-eye-dropper","color-format","color-interpolation","color-interpolation-filters","color-rendering","color-scheme","color/* color: red */","colorPicker","colorz","column-count","column-fill","column-gap","column-height","column-rule","column-rule-break","column-rule-color","column-rule-outset","column-rule-style","column-rule-width","column-span","column-width","column-wrap","columns","combined-diff-view.copy","comic-sans-ms","command-editor","command-input","commit","compatibility-lookup-link","components.collect-garbage","components.request-app-banner","computed","computed-properties","computed-styles","condition","conditional-breakpoint","configure","confirm","confirm-import-recording-dialog","confirm-import-recording-input","connection","connection-id","consent-onboarding","consent-reminder","console","console-autocomplete-on-enter","console-autocomplete-on-enter-true","console-eager-eval","console-eager-eval-false","console-eager-eval-true","console-group-similar","console-group-similar-false","console-history-autocomplete","console-history-autocomplete-false","console-insights","console-insights-enabled","console-insights-setting","console-insights-skip-reminder","console-insights.accordion","console-message","console-pins","console-prompt","console-settings","console-show-settings-toolbar","console-shows-cors-errors","console-shows-cors-errors-false","console-sidebar","console-timestamps-enabled","console-timestamps-enabled-true","console-trace-expand","console-trace-expand-false","console-user-activation-eval","console-user-activation-eval-false","console-view","console.clear","console.clear.history","console.create-pin","console.sidebar-selected-filter","console.text-filter","console.textFilter","console.toggle","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-query","container-type","contains-ai-content-warning","content","content-encoding","content-length","content-type","content-visibility","context","context3","contextmenu","continue","continue-replay","continue-to-here","continuous","contrast","contrast-issues","contrast-issues-documentation","contrast-ratio","control","converter-@puppeteer/replay","converter-JSON","converter-Puppeteer (for Firefox)","converter-Puppeteer (including Lighthouse analysis)","converter-Puppeteer","converter-Puppeteer(includingLighthouseanalysis)","converter-TestExtension","converter-extension","converter-json","converter-lighthouse","converter-puppeteer","converter-puppeteer-firefox","converter-puppeteer-replay","cookie-control-override-enabled","cookie-flag-controls","cookie-preview","cookie-report","cookie-report-search-query","cookie-view-show-decoded","cookies","cookies-data","cookies-for-frame","copy","copy-all-as-curl","copy-all-as-curl-bash","copy-all-as-curl-cmd","copy-all-as-fetch","copy-all-as-har","copy-all-as-har-with-sensitive-data","copy-all-as-nodejs-fetch","copy-all-as-powershell","copy-all-css-changes","copy-all-css-declarations-as-js","copy-all-declarations","copy-all-urls","copy-as-base","copy-as-curl","copy-as-curl-bash","copy-as-curl-cmd","copy-as-fetch","copy-as-hex","copy-as-nodejs-fetch","copy-as-powershell","copy-as-utf","copy-color","copy-console","copy-css-declaration-as-js","copy-declaration","copy-element","copy-file-name","copy-full-xpath","copy-initiator-url","copy-js-path","copy-link-address","copy-object","copy-outer-html","copy-payload","copy-primitive","copy-property","copy-property-path","copy-request-headers","copy-response","copy-response-headers","copy-rule","copy-selector","copy-stack-trace","copy-stacktrace","copy-step-as-json","copy-step-as-lighthouse","copy-step-as-puppeteer","copy-step-as-puppeteer-replay","copy-string-as-js-literal","copy-string-as-json-literal","copy-string-contents","copy-url","copy-value","copy-visible-styled-selection","copy-watch-expression-value","copy-xpath","corner-bottom-left-shape","corner-bottom-right-shape","corner-end-end-shape","corner-end-start-shape","corner-shape","corner-start-end-shape","corner-start-start-shape","corner-top-left-shape","corner-top-right-shape","cors-details","count","count-delta","counter-increment","counter-reset","counter-set","country","courier-new","coverage","coverage-by-type","coverage-type","coverage-view-coverage-type","coverage.clear","coverage.export","coverage.start-with-reload","coverage.toggle-recording","cpu-no-throttling","cpu-throttled-20","cpu-throttled-4","cpu-throttled-6","cpu-throttled-calibrated-low-tier-mobile","cpu-throttled-calibrated-mid-tier-mobile","cpu-throttling","cpu-throttling-selector","cpu-throttling-selector-calibrate","create-new-snippet","create-recording","create-recording-view","credential-id","credentialId","critical","cs","css","css-angle","css-animation-name","css-font-palette","css-layers","css-location","css-overview","css-overview.cancel-processing","css-overview.capture-overview","css-overview.clear-overview","css-overview.color","css-overview.colors","css-overview.contrast","css-overview.font-info","css-overview.media-queries","css-overview.quick-start","css-overview.summary","css-overview.unused-declarations","css-position-try","css-property-doc","css-shadow","css-source-maps-enabled","css-source-maps-enabled-false","css-text-node","css-variable","css-wide-keyword-link","cssAngleEditor","cssShadowEditor","cssoverview","ctap2","ctrl-!","ctrl-:","ctrl->","ctrl-?","ctrl-@","ctrl-alt-!","ctrl-alt-:","ctrl-alt->","ctrl-alt-?","ctrl-alt-@","ctrl-alt-arrowdown","ctrl-alt-arrowleft","ctrl-alt-arrowright","ctrl-alt-arrowup","ctrl-alt-backspace","ctrl-alt-delete","ctrl-alt-end","ctrl-alt-enter","ctrl-alt-escape","ctrl-alt-home","ctrl-alt-meta-:","ctrl-alt-meta->","ctrl-alt-meta-?","ctrl-alt-meta-@","ctrl-alt-meta-arrowdown","ctrl-alt-meta-arrowleft","ctrl-alt-meta-arrowright","ctrl-alt-meta-arrowup","ctrl-alt-meta-backspace","ctrl-alt-meta-delete","ctrl-alt-meta-end","ctrl-alt-meta-enter","ctrl-alt-meta-escape","ctrl-alt-meta-home","ctrl-alt-meta-pagedown","ctrl-alt-meta-pageup","ctrl-alt-meta-tab","ctrl-alt-pagedown","ctrl-alt-pageup","ctrl-alt-tab","ctrl-arrowdown","ctrl-arrowleft","ctrl-arrowright","ctrl-arrowup","ctrl-backspace","ctrl-delete","ctrl-end","ctrl-enter","ctrl-escape","ctrl-home","ctrl-pagedown","ctrl-pageup","ctrl-tab","cumulative-layout-shifts","current-dock-state-bottom","current-dock-state-left","current-dock-state-right","current-dock-state-undock","current-dock-state-undocked","current-url","currentDockState","currentchange","cursive","cursor","custom","custom-accepted-encodings","custom-color-palette","custom-emulated-device-list","custom-formatters","custom-header-for-test","custom-item","custom-network-conditions","custom-network-throttling-item","custom-property","custom-user-agent","custom-user-agent-metadata","customize-pwa-tittle-bar","cut","cx","cy","d","da","dark","data","data-grid-foo-column-weights","dblclick","de","debug","debugger","debugger-paused","debugger.breakpoint-input-window","debugger.evaluate-selection","debugger.next-call-frame","debugger.previous-call-frame","debugger.run-snippet","debugger.show-coverage","debugger.step","debugger.step-into","debugger.step-out","debugger.step-over","debugger.toggle-breakpoint","debugger.toggle-breakpoint-enabled","debugger.toggle-breakpoints-active","debugger.toggle-pause","dec","declaration","decline-execute-code","decode-encode","decoder-properties","default","deflate","delete","delete-all","delete-all-watch-expressions","delete-bucket-confirmation","delete-button","delete-database","delete-database-confirmation","delete-element","delete-event-listener","delete-file-confirmation","delete-folder-confirmation","delete-origin-mapping","delete-recording","delete-selected","delete-watch-expression","deleteByCut","deleteByDrag","deleteContent","deleteContentBackward","deleteContentForward","deleteEntireSoftLine","deleteHardLineBackward","deleteHardLineForward","deleteSoftLineBackward","deleteSoftLineForward","deleteWordBackward","deleteWordForward","deployed","descent-override","description","desktop","desktop-touch","destination","detached-node","detached-node-count","details","details-general","details-response-headers","deuteranopia","dev-tools-default","developer-resources","development-origin","device","device-fold","device-frame-enable","device-mode","device-mode-preset-1024px","device-mode-preset-1440px","device-mode-preset-2560px","device-mode-preset-320px","device-mode-preset-375px","device-mode-preset-425px","device-mode-preset-768px","device-mode-resizer","device-orientation","device-pixel-ratio","device-pixel-ratio-enable","device-posture","device-scale-factor","device-type","device-type-enable","devicemotion","deviceorientation","devices","devtools","devtools-override","direction","directives-details","disable","disable-all-breakpoints","disable-async-stack-traces","disable-async-stack-traces-true","disable-breakpoint","disable-file-breakpoints","disable-locale-info-bar","disable-paused-state-overlay","disable-recorder-import-warning","disable-self-xss-warning","disabled","disallowed-select-descendants-details","disconnect-from-network","dismiss","display","display-override","display-timestamp","display-up","displayUp-down","dispose","distance","dock-side","document","document.write","documentation","documents","dom-activate","dom-attr-modified","dom-character-data-modified","dom-content-loaded","dom-focus-in","dom-focus-out","dom-mutation","dom-node-inserted","dom-node-inserted-into-document","dom-node-removed","dom-node-removed-from-document","dom-subtree-modified","dom-window.close","dom-word-wrap","dom-word-wrap-false","domain","dominant-baseline","download","drag","drag-drop","dragend","dragenter","dragleave","dragover","dragstart","drawer","drawer-view-closeableTabs","drjones.network-floating-button","drjones.network-panel-context","drjones.performance-insight-context","drjones.performance-panel-context","drjones.sources-floating-button","drjones.sources-panel-context","drop","duration","durationchange","dynamic-local-setting","dynamic-range-limit","dynamic-synced-setting","early-hints-headers","edit","edit-and-resend","edit-attribute","edit-breakpoint","edit-item","edit-name","edit-text","edit-title","editor","el","elapsed-time","elapsed-us","element-class","element-properties","element-states","element.set-inner-html","elements","elements-classes","elements-panel","elements.capture-area-screenshot","elements.color-mix-popover","elements.copy-styles","elements.css-color-mix","elements.css-hint","elements.css-property-doc","elements.css-selector-specificity","elements.css-value-trace","elements.css-var","elements.dom-breakpoints","elements.dom-creation","elements.dom-properties","elements.duplicate-element","elements.edit-as-html","elements.event-listeners","elements.generic-sidebar-popover","elements.hide-element","elements.image-preview","elements.invalid-property-decl-popover","elements.issue","elements.jump-to-style","elements.layout","elements.length-popover","elements.new-style-rule","elements.property-documentation-popover","elements.redo","elements.refresh-event-listeners","elements.reveal-node","elements.select-element","elements.show-all-style-properties","elements.show-computed","elements.show-styles","elements.toggle-element-search","elements.toggle-eye-dropper","elements.undo","elementsTreeOutline","em","emoji","emptied","empty-cells","empty-view","emulate-auto-dark-mode","emulate-page-focus","emulate-page-focus-false","emulate-page-focus-true","emulated-css-media","emulated-css-media-feature-color-gamut","emulated-css-media-feature-forced-colors","emulated-css-media-feature-forced-colors-active","emulated-css-media-feature-forced-colors-none","emulated-css-media-feature-prefers-color-scheme","emulated-css-media-feature-prefers-color-scheme-dark","emulated-css-media-feature-prefers-color-scheme-light","emulated-css-media-feature-prefers-contrast","emulated-css-media-feature-prefers-reduced-data","emulated-css-media-feature-prefers-reduced-motion","emulated-css-media-feature-prefers-reduced-motion-reduce","emulated-css-media-feature-prefers-reduced-transparency","emulated-css-media-print","emulated-css-media-screen","emulated-vision-deficiency","emulated-vision-deficiency-achromatopsia","emulated-vision-deficiency-blurred-vision","emulated-vision-deficiency-deuteranopia","emulated-vision-deficiency-protanopia","emulated-vision-deficiency-reduced-contrast","emulated-vision-deficiency-tritanopia","emulation-locations","emulation.add-location","emulation.auto-adjust-scale","emulation.capture-full-height-screenshot","emulation.capture-node-screenshot","emulation.capture-screenshot","emulation.cpu-pressure","emulation.device-height","emulation.device-mode-value","emulation.device-orientation-override","emulation.device-scale","emulation.device-scale-factor","emulation.device-ua","emulation.device-width","emulation.idle-detection","emulation.location-override","emulation.locations","emulation.show-device-mode","emulation.show-device-outline","emulation.show-device-outline-true","emulation.show-device-scale-factor","emulation.show-rulers","emulation.show-rulers-true","emulation.show-user-agent-type","emulation.toggle-device-mode","emulation.toolbar-controls-enabled","emulation.touch","en-gb","en-us","en-xl","enable-all-breakpoints","enable-breakpoint","enable-file-breakpoints","enable-header-overrides","enable-ignore-listing","enabled","end-time","ended","endpoints","enter","enterpictureinpicture","enterprise-enabled","error","error-message","errors-and-warnings","es","es-419","et","etag","eu","event","event-group-name","event-group-owner","event-listener-dispatch-filter-type","event-log","event-main-frame-id","event-name","event-owner-origin","event-params","event-stream","event-time","event-title","event-type","event-url","eventSource","events","events-table","evolved-cls","exclude-folder","exclude-folder-confirmation","expand","expand-recursively","experimental-cookie-features","experiments","experiments-filter","expires","explain.console-message.context.error","explain.console-message.context.other","explain.console-message.context.warning","explain.console-message.hover","explanation","export-har","export-har-menu","export-har-with-sensitive-data","export-recording","expose-internals","expression","extend-grid-lines","extend-grid-lines-false","extend-grid-lines-true","extension-storage","extension-storage-data","extension-storage-for-domain","extension-storage-viewer","extension-view","extremely-slow","fa","fair","fallback","fallback-font-family","fangsong","fantasy","fast-4g","feedback","fetch","fetch-and-xhr","fi","field-data","field-sizing","field-url-override-enabled","fil","fileMappingEntries","fileSystemMapping","files-used-in-patching","fill","fill-opacity","fill-rule","filled-field-index","film-strip","filter","filter-bar","filter-bitset","filter-by-rule-set","finish","firefox-android-mobile","firefox-android-tablet","firefox-i-pad","firefox-i-phone","firefox-mac","firefox-windows","first","fit-to-window","fix-this-issue","flame","flamechart-selected-navigation","flamechart-selected-navigation-modern","flex","flex-basis","flex-direction","flex-direction-column","flex-direction-column-reverse","flex-direction-row","flex-direction-row-reverse","flex-flow","flex-grow","flex-shrink","flex-wrap","flex-wrap-nowrap","flex-wrap-wrap","flexbox-overlays","float","float-32-bit","float-64-bit","flood-color","flood-opacity","focus","focus-visible","focus-within","fold","folded","folder","font","font-display","font-editor","font-editor-documentation","font-family","font-feature-settings","font-kerning","font-optical-sizing","font-palette","font-size","font-size-adjust","font-size-unit","font-size-value-type","font-stretch","font-style","font-synthesis","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-view","font-weight","font-weight-unit","font-weight-value-type","foo","foobar","footer","force","force-run","force-state","forced-color-adjust","forced-reflow","form-data","fourth","fr","fr-ca","frame","frame-creation-stack-trace","frame-resource","frame-viewer-hide-chrome-window","frame-viewer-show-paints","frame-viewer-show-slow-scroll-rects","frames","frames-per-issue","fre-disclaimer","fre-disclaimer.cancel","fre-disclaimer.continue","fre-disclaimer.learn-more","freestyler","freestyler.accordion","freestyler.delete","freestyler.dogfood-info","freestyler.element-panel-context","freestyler.elements-floating-button","freestyler.feedback","freestyler.help","freestyler.history","freestyler.new-chat","freestyler.send-feedback","freestyler.settings","freestyler.style-tab-context","full-accessibility","full-accessibility-tree","full-accessibility-tree-documentation","full-accessibility-tree-feedback","full-version","function","galaxy-z-fold-5","gamma","gap","gap-rule-paint-order","garamond","gen-ai-settings-panel","general","generative-ai-terms-of-service","generic-details","geolocation","geolocation.get-current-position","geolocation.watch-position","georgia","gl","googlebot","googlebot-desktop","googlebot-smartphone","gotpointercapture","grace-period-link","grace-period-mitigation-disabled","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-overlays","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-settings","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","group-computed-styles","group-issues-by-category","group-issues-by-kind","gu","gutter","gzip","hardware-concurrency","hardware-concurrency-reset","hardware-concurrency-selector","hardware-concurrency-toggle","has-cross-site-ancestor","has-overrides","has-touch","hashchange","he","header-editor","header-name","header-name-1","header-options","headers","headers-component","headers-editor-row-parent","headers-view","headers-view.add-header","headers-view.add-override-rule","headers-view.remove-apply-to-section","headers-view.remove-header","heap-profiler","heap-snapshot.constructors-view","heap-snapshot.retaining-paths-view","heap-tracking-overview","heavy","heavy-ad-details","height","help","help.release-notes","help.report-issue","help.show-release-note","help.show-release-note-false","helvetica","heuristic-link","heuristic-mitigation-disabled","hex","hi","hide-all-others","hide-children","hide-data-urls","hide-disabled-features-details","hide-extension-urls","hide-function","hide-issue-by-code-setting-experiment-2021","hide-issues","hide-messages-from","hide-network-messages","hide-network-messages-true","hide-player","hide-repeating-children","highlight-errors-elements-panel","highlight-node-on-hover-in-overlay","history","historyRedo","historyUndo","hover","hr","http-only","http://devtools-extensions.oopif.testTestPanel","http://localhostTest","https","https://i0.devtools-frontend.test:38881TestPanel","https://i0.devtools-frontend.test:38881extension-tab-title","https://i0.devtools-frontend.testTestPanel","https://i0.devtools-frontend.testextension-tab-title","hu","hue","hy","hyphenate-character","hyphenate-limit-chars","hyphens","i-pad-air","i-pad-mini","i-pad-pro","i-phone-12-pro","i-phone-14-pro-max","i-phone-se","i-phone-xr","icons","id","identity","ignore-this-retainer","image","image-orientation","image-rendering","image-url","image-view","image-view.copy-image-as-data-url","image-view.copy-image-url","image-view.open-in-new-tab","image-view.save-image","impact","import-har","import-recording","important","in-range","indeterminate","indexed-db","indexed-db-data-view","indexed-db-database","indexed-db-object-store","indexeddb-data","info","inherit","inherited","inherited-pseudotype","inherits","initator-request","initial","initial-letter","initial-value","initiator","initiator-address-space","initiator-tree","inline","inline-citation","inline-size","inline-variable-values","inline-variable-values-false","input","insertCompositionText","insertFromDrop","insertFromPaste","insertFromPasteAsQuotation","insertFromYank","insertHorizontalRule","insertLineBreak","insertLink","insertOrderedList","insertParagraph","insertReplacementText","insertText","insertTranspose","insertUnorderedList","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","insight","insights-deprecation-learn-more","insights-deprecation-open-performance-panel","insights-deprecation-send-feedback","inspect","inspect-prerendered-page","inspector-main.focus-debuggee","inspector-main.hard-reload","inspector-main.reload","inspector-stylesheet","installability","installing-entry-inspect","instance-id","instrumentation-breakpoints","integer-16-bit","integer-32-bit","integer-64-bit","integer-8-bit","interactions","interactivity","interest-groups","interest-target-delay","interest-target-hide-delay","interest-target-show-delay","internal","internet-explorer-10","internet-explorer-11","internet-explorer-7","internet-explorer-8","internet-explorer-9","interpolate-size","invalid","invert-filter","is","is-landscape","is-mobile","is-resident-credential","is-user-active-false-is-screen-unlocked-false","is-user-active-false-is-screen-unlocked-true","is-user-active-true-is-screen-unlocked-false","is-user-active-true-is-screen-unlocked-true","isResidentCredential","isUnderTest","isolates","isolation","issue","issuer","issues","issues-pane","issues.filter-network-requests-by-cookie","issues.filter-network-requests-by-raw-cookie","issues.unhide-all-hiddes","it","item-1","item-2","ja","java-script","java-script-disabled","java-script-disabled-true","javascript","javascript-context","jpg-header","js-event-listeners","js-heap-total-size","js-profiler","js-source-maps-enabled","js-source-maps-enabled-false","json-view","jump-to-anchor-node","jump-to-breakpoint","just-my-code","justify-content","justify-content-center","justify-content-end","justify-content-flex-end","justify-content-flex-start","justify-content-space-around","justify-content-space-between","justify-content-space-evenly","justify-content-start","justify-items","justify-items-center","justify-items-end","justify-items-start","justify-items-stretch","justify-self","ka","keep-alive","key","keybinds","keyboard","keydown","keyframes","keypress","keyup","kk","km","kn","ko","ky","landscape-left","landscape-right","language","language-af","language-am","language-ar","language-as","language-az","language-be","language-bg","language-bn","language-bs","language-ca","language-cs","language-cy","language-da","language-de","language-el","language-en-gb","language-en-us","language-en-xl","language-es","language-es-419","language-et","language-eu","language-fa","language-fi","language-fil","language-fr","language-fr-ca","language-gl","language-gu","language-he","language-hi","language-hr","language-hu","language-hy","language-id","language-is","language-it","language-ja","language-ka","language-kk","language-km","language-kn","language-ko","language-ky","language-lo","language-lt","language-lv","language-mismatch","language-mk","language-ml","language-mn","language-mr","language-ms","language-my","language-ne","language-nl","language-no","language-or","language-pa","language-pl","language-pt","language-pt-pt","language-ro","language-ru","language-si","language-sk","language-sl","language-sq","language-sr","language-sr-latn","language-sv","language-sw","language-ta","language-te","language-th","language-tr","language-uk","language-ur","language-uz","language-vi","language-zh","language-zh-hk","language-zh-tw","language-zu","large","large-blob","larger","last-dock-state","last-modified","lat","latency","latitude","layer","layers","layers-3d-view","layers-details","layers-show-internal-layers","layers.3d-center","layers.3d-pan","layers.3d-rotate","layers.down","layers.left","layers.paint-profiler","layers.pan-mode","layers.reset-view","layers.right","layers.rotate-mode","layers.select-object","layers.up","layers.zoom-in","layers.zoom-out","layout","layout-count","layout-shifts","learn-more","learn-more.ai-annotations","learn-more.ai-assistance","learn-more.console-insights","learn-more.coop-coep","learn-more.csp-report-only","learn-more.eligibility","learn-more.monitor-memory-usage","learn-more.never-use-unload","learn-more.origin-trials","leavepictureinpicture","left","legend","length","length-popover","less","letter-spacing","letter-spacing-unit","letter-spacing-value-type","light","lighter","lighthouse","lighthouse-show-settings-toolbar","lighthouse.audit-summary.average","lighthouse.audit-summary.fail","lighthouse.audit-summary.informative","lighthouse.audit-summary.pass","lighthouse.audit.accesskeys","lighthouse.audit.aria-allowed-attr","lighthouse.audit.aria-allowed-role","lighthouse.audit.aria-command-name","lighthouse.audit.aria-conditional-attr","lighthouse.audit.aria-deprecated-role","lighthouse.audit.aria-dialog-name","lighthouse.audit.aria-hidden-body","lighthouse.audit.aria-hidden-focus","lighthouse.audit.aria-input-field-name","lighthouse.audit.aria-meter-name","lighthouse.audit.aria-progressbar-name","lighthouse.audit.aria-prohibited-attr","lighthouse.audit.aria-required-attr","lighthouse.audit.aria-required-children","lighthouse.audit.aria-required-parent","lighthouse.audit.aria-roles","lighthouse.audit.aria-text","lighthouse.audit.aria-toggle-field-name","lighthouse.audit.aria-tooltip-name","lighthouse.audit.aria-treeitem-name","lighthouse.audit.aria-valid-attr","lighthouse.audit.aria-valid-attr-value","lighthouse.audit.bf-cache","lighthouse.audit.bootup-time","lighthouse.audit.button-name","lighthouse.audit.bypass","lighthouse.audit.canonical","lighthouse.audit.charset","lighthouse.audit.clickjacking-mitigation","lighthouse.audit.color-contrast","lighthouse.audit.crawlable-anchors","lighthouse.audit.critical-request-chains","lighthouse.audit.csp-xss","lighthouse.audit.cumulative-layout-shift","lighthouse.audit.custom-controls-labels","lighthouse.audit.custom-controls-roles","lighthouse.audit.definition-list","lighthouse.audit.deprecations","lighthouse.audit.diagnostics","lighthouse.audit.dlitem","lighthouse.audit.doctype","lighthouse.audit.document-title","lighthouse.audit.dom-size","lighthouse.audit.duplicate-id-aria","lighthouse.audit.duplicated-javascript","lighthouse.audit.efficient-animated-content","lighthouse.audit.empty-heading","lighthouse.audit.errors-in-console","lighthouse.audit.final-screenshot","lighthouse.audit.first-contentful-paint","lighthouse.audit.first-meaningful-paint","lighthouse.audit.focus-traps","lighthouse.audit.focusable-controls","lighthouse.audit.font-display","lighthouse.audit.font-size","lighthouse.audit.form-field-multiple-labels","lighthouse.audit.frame-title","lighthouse.audit.geolocation-on-start","lighthouse.audit.has-hsts","lighthouse.audit.heading-order","lighthouse.audit.hreflang","lighthouse.audit.html-has-lang","lighthouse.audit.html-lang-valid","lighthouse.audit.html-xml-lang-mismatch","lighthouse.audit.http-status-code","lighthouse.audit.identical-links-same-purpose","lighthouse.audit.image-alt","lighthouse.audit.image-aspect-ratio","lighthouse.audit.image-redundant-alt","lighthouse.audit.image-size-responsive","lighthouse.audit.input-button-name","lighthouse.audit.input-image-alt","lighthouse.audit.inspector-issues","lighthouse.audit.interaction-to-next-paint","lighthouse.audit.interactive","lighthouse.audit.interactive-element-affordance","lighthouse.audit.is-crawlable","lighthouse.audit.is-on-https","lighthouse.audit.js-libraries","lighthouse.audit.label","lighthouse.audit.label-content-name-mismatch","lighthouse.audit.landmark-one-main","lighthouse.audit.largest-contentful-paint","lighthouse.audit.largest-contentful-paint-element","lighthouse.audit.layout-shifts","lighthouse.audit.lcp-lazy-loaded","lighthouse.audit.legacy-javascript","lighthouse.audit.link-in-text-block","lighthouse.audit.link-name","lighthouse.audit.link-text","lighthouse.audit.list","lighthouse.audit.listitem","lighthouse.audit.logical-tab-order","lighthouse.audit.long-tasks","lighthouse.audit.main-thread-tasks","lighthouse.audit.mainthread-work-breakdown","lighthouse.audit.managed-focus","lighthouse.audit.max-potential-fid","lighthouse.audit.meta-description","lighthouse.audit.meta-refresh","lighthouse.audit.meta-viewport","lighthouse.audit.metrics","lighthouse.audit.modern-image-formats","lighthouse.audit.network-requests","lighthouse.audit.network-rtt","lighthouse.audit.network-server-latency","lighthouse.audit.no-document-write","lighthouse.audit.non-composited-animations","lighthouse.audit.notification-on-start","lighthouse.audit.object-alt","lighthouse.audit.offscreen-content-hidden","lighthouse.audit.offscreen-images","lighthouse.audit.origin-isolation","lighthouse.audit.paste-preventing-inputs","lighthouse.audit.prioritize-lcp-image","lighthouse.audit.redirects","lighthouse.audit.redirects-http","lighthouse.audit.render-blocking-resources","lighthouse.audit.resource-summary","lighthouse.audit.robots-txt","lighthouse.audit.screenshot-thumbnails","lighthouse.audit.script-treemap-data","lighthouse.audit.select-name","lighthouse.audit.server-response-time","lighthouse.audit.skip-link","lighthouse.audit.speed-index","lighthouse.audit.structured-data","lighthouse.audit.tabindex","lighthouse.audit.table-duplicate-name","lighthouse.audit.table-fake-caption","lighthouse.audit.target-size","lighthouse.audit.td-has-header","lighthouse.audit.td-headers-attr","lighthouse.audit.th-has-data-cells","lighthouse.audit.third-party-cookies","lighthouse.audit.third-party-facades","lighthouse.audit.third-party-summary","lighthouse.audit.total-blocking-time","lighthouse.audit.total-byte-weight","lighthouse.audit.unminified-css","lighthouse.audit.unminified-javascript","lighthouse.audit.unsized-images","lighthouse.audit.unused-css-rules","lighthouse.audit.unused-javascript","lighthouse.audit.use-landmarks","lighthouse.audit.user-timings","lighthouse.audit.uses-http2","lighthouse.audit.uses-long-cache-ttl","lighthouse.audit.uses-optimized-images","lighthouse.audit.uses-passive-event-listeners","lighthouse.audit.uses-rel-preconnect","lighthouse.audit.uses-responsive-images","lighthouse.audit.uses-responsive-images-snapshot","lighthouse.audit.uses-text-compression","lighthouse.audit.valid-lang","lighthouse.audit.valid-source-maps","lighthouse.audit.video-caption","lighthouse.audit.viewport","lighthouse.audit.visual-order-follows-dom","lighthouse.audit.work-during-interaction","lighthouse.cancel","lighthouse.cat-a11y","lighthouse.cat-best-practices","lighthouse.cat-perf","lighthouse.cat-seo","lighthouse.clear-storage","lighthouse.device-type","lighthouse.enable-sampling","lighthouse.end-time-span","lighthouse.mode","lighthouse.start","lighthouse.throttling","lighting-color","line-break","line-clamp","line-gap-override","line-height","line-height-unit","line-height-value-type","line-names","line-numbers","linear-memory-inspector","linear-memory-inspector.address","linear-memory-inspector.byte-cell","linear-memory-inspector.delete-highlight","linear-memory-inspector.endianess","linear-memory-inspector.history-back","linear-memory-inspector.history-forward","linear-memory-inspector.jump-to-address","linear-memory-inspector.jump-to-highlight","linear-memory-inspector.next-page","linear-memory-inspector.previous-page","linear-memory-inspector.refresh","linear-memory-inspector.text-cell","linear-memory-inspector.toggle-value-settings","linear-memory-inspector.value-type-mode","linear-memory-inspector.viewer","link","link-in-explanation","linux","list-style","list-style-image","list-style-position","list-style-type","little-endian","live-count","live-heap-profile","live-heap-profile.start-with-reload","live-heap-profile.toggle-recording","live-size","lmi-interpreter-settings","lo","load","load-through-target","loadeddata","loadedmetadata","loadend","loading","loadstart","local-fonts-disabled","local-fonts-disabled-true","local-storage","local-storage-data","local-storage-for-domain","locale","location","log-level","logpoint","long","long-interaction","long-tasks","longitude","lostpointercapture","low-contrast-details","low-end-mobile","lt","lv","macos","main","main-menu","main-selected-tab","main-tab-order","main.debug-reload","main.next-tab","main.previous-tab","main.search-in-panel.cancel","main.search-in-panel.find","main.search-in-panel.find-next","main.search-in-panel.find-previous","main.toggle-dock","main.toggle-drawer","main.zoom-in","main.zoom-out","main.zoom-reset","make-a-copy","manage-header-columns","manager-custom-headers","manifest","manifest.copy-id","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","marker","marker-end","marker-mid","marker-start","mask","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-tracks","masonry-direction","masonry-fill","masonry-flow","masonry-slack","masonry-template-tracks","masonry-track","masonry-track-end","masonry-track-start","match-attempts","match-case","match-count","match-whole-word","matched-address-item","math","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","measure-performance","media","media-flame-chart-group-expansion","media-queries-enable","media-query","medias","medium","memory-live-heap-profile","message","message-level-filters","messageURLFilters","messageerror","messages","messaging","meta-!","meta-:","meta->","meta-?","meta-@","meta-arrowdown","meta-arrowleft","meta-arrowright","meta-arrowup","meta-backspace","meta-delete","meta-end","meta-enter","meta-escape","meta-home","meta-pagedown","meta-pageup","meta-tab","metadata","metadata-allowed-sites-details","method","microsoft-edge-android-mobile","microsoft-edge-android-tablet","microsoft-edge-chromium-mac","microsoft-edge-chromium-windows","microsoft-edge-edge-html-windows","microsoft-edge-edge-html-x-box","microsoft-edge-i-pad","microsoft-edge-i-phone","mid-tier-mobile","min-block-size","min-height","min-inline-size","min-width","missing-debug-info","mix-blend-mode","mixed-content-details","mk","ml","mn","mobile","mobile-no-touch","mobile-throttling","model","modern","monitoring-xhr-enabled","monspace","more","more-filters","more-options","more-tabs","more-tools","mouse","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","mousewheel","move-tab-backward","move-tab-forward","move-to-bottom","move-to-top","mr","ms","my","name","navigate","navigate-to-selector-source","navigate-to-style","navigateerror","navigatefrom","navigatesuccess","navigateto","navigation","navigator","navigator-content-scripts","navigator-files","navigator-group-by-authored","navigator-group-by-folder","navigator-network","navigator-overrides","navigator-snippets","ne","negative","nest-hub","nest-hub-max","network","network-blocked-patterns","network-color-code-resource-types","network-color-code-resource-types-true","network-conditions","network-conditions.network-low-end-mobile","network-conditions.network-mid-tier-mobile","network-conditions.network-offline","network-conditions.network-online","network-event-source-message-filter","network-film-strip","network-hide-chrome-extensions","network-hide-data-url","network-invert-filter","network-item-preview","network-log-columns","network-log-large-rows","network-log-show-overview","network-log.preserve-log","network-log.preserve-log-true","network-log.record-log","network-main","network-only-blocked-requests","network-only-third-party-setting","network-overview","network-panel-filter-bar-redesign","network-panel-filter-bar-redesign-documentation","network-panel-filter-bar-redesign-feedback","network-record-film-strip-setting","network-request","network-resource-type-filters","network-settings","network-show-blocked-cookies-only-setting","network-show-settings-toolbar","network-text-filter","network-web-socket-message-filter","network.ad-blocking-enabled","network.ad-blocking-enabled-true","network.add-conditions","network.add-custom-header","network.add-network-request-blocking-pattern","network.blocked-urls","network.clear","network.config","network.enable-remote-file-loading","network.enable-request-blocking","network.group-by-frame","network.group-by-frame-false","network.group-by-frame-true","network.hide-request-details","network.initiator-stacktrace","network.remove-all-network-request-blocking-patterns","network.search","network.search-network-tab","network.show-options-to-generate-har-with-sensitive-data","network.show-options-to-generate-har-with-sensitive-data-documentation","network.show-options-to-generate-har-with-sensitive-data-false","network.show-options-to-generate-har-with-sensitive-data-true","network.toggle-recording","networkConditions","networkConditionsCustomProfiles","networkLogColumns","networkLogColumnsVisibility","never-pause-here","new-attribute","new-authenticator","new-file","next","next-page","nfc","nl","no","no-agent-entrypoint","no-override","no-throttling","node","node-connection","node-id","node-removed","nodes","nominal","none","normal","not-logged-in","notification","notification.request-permission","notifications","number","numeric-column","object","object-column","object-fit","object-position","object-view-box","oct","off","offline","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","offset-x","offset-y","on","on-shown-without-set-expression","only-3rd-party-requests","only-blocked-requests","only-blocked-response-cookies","only-show-blocked-cookies","only-show-blocked-requests","only-show-cookies-with-issues","only-show-third-party","opacity","open","open-ai-settings","open-elements-panel","open-folder","open-in-animations-panel","open-in-containing-folder","open-in-new-tab","open-link-handler","open-memory-inspector","open-network-panel","open-performance-panel","open-sources-panel","open-using","opened-windows","opera-mac","opera-mini-i-os","opera-mobile-android-mobile","opera-presto-mac","opera-presto-windows","opera-windows","operator","optional","or","order","origin","origin-trial-test-property","original-script-location","orphans","other","other-origin","out-of-range","outermost-target-selector","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","override-colors","override-content","override-headers","override-source-mapped-file-warning","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","p3","pa","packetLoss","packetQueueLength","packetReordering","pad","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-orientation","paint-order","painting","palatino","palette-panel","palette-switcher","panel-closeableTabs","panel-deprecated","panel-tabOrder","parameters","parentTreeItem","parse","partition-key-site","partitioning-blob-url-details","passive","paste","patch-widget.discard","patch-widget.save-to-workspace","path","pattern","pause","pause-on-caught-exception","pause-on-exception-enabled","pause-on-exception-enabled-true","pause-on-uncaught-exception","pause-uncaught","payload","payment-handler","perf-panel-annotations","perfmon-active-indicators2","performance-insights","performance.history-item","performance.monitor","performance.sidebar-insights-category-select","performance.sidebar-toggle","periodic-background-sync","periodic-sync-tag","persistence-automatic-workspace-folders","persistence-network-overrides-enabled","persistence-network-overrides-enabled-true","perspective","perspective-origin","picture-in-picture","ping","pixel-7","pl","place-content","place-items","place-self","placeholder-shown","platform","platform-version","play","play-recording","player","playing","pointer","pointer-32-bit","pointer-64-bit","pointer-events","pointercancel","pointerdown","pointerenter","pointerleave","pointermove","pointerout","pointerover","pointerrawupdate","pointerup","popover-hide-delay","popover-show-delay","popstate","population","portrait","portrait-upside-down","position","position-anchor","position-area","position-fallback","position-try","position-try-fallbacks","position-try-options","position-try-order","position-visibility","prefer-dark-color-scheme","prefer-light-color-scheme","preferences","preferred-network-condition","prefix","preflight-request","preloaded-urls","preloading","preloading-details","preloading-disabled","preloading-rules","preloading-speculations","preloading-status-panel","preloading-status-panel-pretty-print","presentation","preserve-console-log","preserve-console-log-true","pretty-print","prev-page","preview","previouslyViewedFiles","primary-font-family","primary-key","prime-numbers","print","print-color-adjust","priority","privacy","privacy-notice","privacy-policy.console-insights","private-state-tokens","production-origin","profile-loading-failed","profile-options","profile-view","profile-view.exclude-selected-function","profile-view.focus-selected-function","profile-view.restore-all-functions","profile-view.selected-view","profiler.clear-all","profiler.delete-profile","profiler.heap-snapshot-base","profiler.heap-snapshot-filter","profiler.heap-snapshot-object","profiler.heap-snapshot-perspective","profiler.heap-snapshot-statistics-view","profiler.heap-toggle-recording","profiler.js-toggle-recording","profiler.load-from-file","profiler.profile-type","profiler.save-to-file","profiles-sidebar","progress","prop","prop1","prop2","properties","property","protanopia","protocol","protocol-handlers","protocol-monitor","protocol-monitor-documentation","protocol-monitor.add-custom-property","protocol-monitor.add-parameter","protocol-monitor.clear-all","protocol-monitor.copy-command","protocol-monitor.delete-parameter","protocol-monitor.hint","protocol-monitor.reset-to-default-value","protocol-monitor.save","protocol-monitor.send-command","protocol-monitor.toggle-command-editor","protocol-monitor.toggle-recording","pseudo-property","pseudotype","pt","pt-pt","push-message","push-messaging","px","query","query-string","question","quick-open","quick-open.show","quick-open.show-command-menu","quickOpen.show","quota-override","quotes","r","range","ratechange","raw-headers","raw-headers-show-more","react-devtools-components","react-devtools-profiler","read-only","read-write","readiness-list-link","reading-flow","reading-order","readystatechange","rec-2020","recalc-style-count","receive","recommendation","reconnect","record-allocation-stacks","recorder-panel-replay-extension","recorder-panel-replay-speed","recorder-preferred-copy-format","recorder-recordings-ng","recorder-screenshots","recorder-selector-attribute","recorder-selector-help","recorder_recordings","recording","recordings","redirect-source-request","redirect-source-request-url","reduce","reduced-contrast","references","references.console-insights","refresh","refresh-caches","refresh-database","refresh-indexeddb","refresh-watch-expressions","regular-breakpoint","regular-expression","reject-percentage","release-note","release-notes","reload-required","rem","remote-address","remote-address-space","remote-debnugging-terminated","remote-devices","remove","remove-all-breakpoints","remove-all-dom-breakpoints","remove-all-expressions","remove-all-to-the-right","remove-attribute-assertion","remove-breakpoint","remove-color","remove-expression","remove-file-breakpoints","remove-folder-from-workspace","remove-folder-from-workspace-confirmation","remove-frame","remove-from-ignore-list","remove-header-override","remove-item","remove-other-breakpoints","remove-script-from-ignorelist","remove-selector","remove-selector-part","remove-step","removed-count","removed-size","rename","rendering","rendering-emulations","rendering.toggle-prefers-color-scheme","replace","replace-all","replay-settings","replay-xhr","report","report-status","reporting-api","reports","request","request-animation-frame","request-animation-frame.callback","request-blocking-enabled","request-blocking-enabled-true","request-details","request-header","request-headers","request-payload","request-types","required","reset","reset-children","reset-columns","reset-entropy-budget","reset-to-defaults","reset-trace","resident-key","resize","resource-view-tab","resources","resources-last-selected-element-path","resources-shared-storage-expanded","resources.clear","resources.clear-incl-third-party-cookies","response","response-header","response-headers","response-time","response-type","responsive","restart-frame","restore-default-shortcuts","result","retained-size","retainedSize","retainers","reveal","reveal-header-overrides","reveal-in-elements","reveal-in-memory-inspector","reveal-in-network","reveal-in-source","reveal-in-sources","reveal-in-sources-panel","reveal-in-summary","reveal-preloads","reveal-rule-set","right","rn-welcome","ro","roboto","rotate","row-gap","row-rule-break","row-rule-color","row-rule-outset","row-rule-style","row-rule-width","rp-id","rpId","ru","ruby-align","ruby-position","rule-set","rule-set-details","rulers-enable","run","rx","ry","sab-details","safari-i-pad-i-os-13.2","safari-i-phone-i-os-13.2","safari-mac","same-site","sampling-heap-profiler-timeline","samsung-galaxy-a51-71","samsung-galaxy-s20-ultra","samsung-galaxy-s8","sans-serif","save-as","save-image","save-name","save-player-info","scale","scheme","sci","scope","screen","screen-rotation","screencast-enabled","script","script-blocked-by-csp","script-first-statement","script-id","script-location","script-on-ignore-list","script-snippets","script-snippets-last-identifier","script-source-url","script-text-node","scripting","scroll","scroll-behavior","scroll-initial-target","scroll-into-view","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-marker-contain","scroll-marker-group","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-start","scroll-start-block","scroll-start-inline","scroll-start-target","scroll-start-target-block","scroll-start-target-inline","scroll-start-target-x","scroll-start-target-y","scroll-start-x","scroll-start-y","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","scrollend","scrollsnapchange","scrollsnapchanging","search","search-as-you-type","search-as-you-type-false","search-in-all-files","search-in-anonymous-and-content-scripts","search-in-anonymous-and-content-scripts-true","search-in-folder","search-match","search.clear","search.refresh","searchInAnonymousAndContentScripts","searchInContentScripts","second","second-col","secure","security","security-last-selected-element-path","security.main-view","security.origin-view","security.toggle-san-truncation","security.toggle-scts-details","security.view-certificate","security.view-certificate-for-origin","seeked","seeking","select","select-element","select-folder","select-next","select-override-folder","select-previous","select-total","select-workspace","selected-color-palette","selected-context-filter-enabled","selected-context-filter-enabled-true","selected-profile-type","selector","selector-aria","selector-attribute","selector-css","selector-picker","selector-pierce","selector-stats","selector-text","selector-xpath","selectors","self","self-xss-warning","send","sendFeedback","sensors","sensors.manage-locations","sensors.reset-device-orientiation","serif","serious","server","server-timing-api","service-worker-update-on-reload","service-workers","session","session-storage","session-storage-data","session-storage-for-domain","set-cookies","set-interval","set-interval.callback","set-timeout","set-timeout.callback","set-to-browser-language","set-to-specific-language","setting","setting-is-not-true","setting1","setting2","settings","settings.add-excluded-folder","settings.add-ignore-list-pattern","settings.documentation","settings.ignore-list-pattern","settings.remove-file-system","settings.restore-defaults-and-reload","settings.shortcuts","settings.show","shallow-size","shallowSize","shape-image-threshold","shape-margin","shape-outside","shape-rendering","shared-storage","shared-storage-data","shared-storage-events","shared-storage-instance","shared-storage-worklet","shared-storage-worklet-script-first-statement","shift-!","shift-?","shift-arrowdown","shift-arrowleft","shift-arrowright","shift-arrowup","shift-backspace","shift-ctrl-alt-arrowdown","shift-ctrl-alt-arrowleft","shift-ctrl-alt-arrowright","shift-ctrl-alt-arrowup","shift-ctrl-alt-backspace","shift-ctrl-alt-delete","shift-ctrl-alt-end","shift-ctrl-alt-enter","shift-ctrl-alt-escape","shift-ctrl-alt-home","shift-ctrl-alt-meta-arrowdown","shift-ctrl-alt-meta-arrowleft","shift-ctrl-alt-meta-arrowright","shift-ctrl-alt-meta-arrowup","shift-ctrl-alt-meta-backspace","shift-ctrl-alt-meta-delete","shift-ctrl-alt-meta-end","shift-ctrl-alt-meta-enter","shift-ctrl-alt-meta-escape","shift-ctrl-alt-meta-home","shift-ctrl-alt-meta-pagedown","shift-ctrl-alt-meta-pageup","shift-ctrl-alt-meta-tab","shift-ctrl-alt-pagedown","shift-ctrl-alt-pageup","shift-ctrl-alt-tab","shift-ctrl-arrowdown","shift-ctrl-arrowleft","shift-ctrl-arrowright","shift-ctrl-arrowup","shift-ctrl-backspace","shift-ctrl-delete","shift-ctrl-end","shift-ctrl-enter","shift-ctrl-escape","shift-ctrl-home","shift-ctrl-pagedown","shift-ctrl-pageup","shift-ctrl-tab","shift-delete","shift-end","shift-enter","shift-escape","shift-home","shift-pagedown","shift-pageup","shift-tab","shortcut","shortcut-panel-switch","shortcuts","show-ad-highlights","show-ad-highlights-true","show-adorner-settings","show-all-properties","show-as-javascript-object","show-content-scripts","show-css-property-documentation-on-hover","show-debug-borders","show-debug-borders-true","show-detailed-inspect-tooltip","show-disabled-features-details","show-event-listeners-for-ancestors","show-events-from-other-domains","show-events-from-other-partitions","show-filtered-out-request-cookies","show-fps-counter","show-fps-counter-true","show-frame-details","show-frameowkr-listeners","show-function-definition","show-grid-areas","show-grid-areas-true","show-grid-line-labels","show-grid-line-labels-line-names","show-grid-line-labels-none","show-grid-track-sizes","show-grid-track-sizes-true","show-html-comments","show-html-comments-false","show-inherited-computed-style-properties","show-issue-associated-with-this","show-layout-shift-regions","show-layout-shift-regions-true","show-media-query-inspector","show-media-query-inspector-true","show-metrics-rulers","show-metrics-rulers-true","show-minimal-safe-area-for-maskable-icons","show-more","show-network-requests","show-option-tp-expose-internals-in-heap-snapshot","show-overrides","show-paint-rects","show-paint-rects-true","show-request","show-requests-with-this-cookie","show-scroll-bottleneck-rects","show-scroll-bottleneck-rects-true","show-shortcuts","show-test-addresses-in-autofill-menu-on-event","show-third-party-issues","show-ua-shadow-dom","show-url-decoded","show-whitespaces-in-editor","show-whitespaces-in-editor-all","show-whitespaces-in-editor-none","show-whitespaces-in-editor-trailing","showHeaSnapshotObjectsHiddenProperties","showThirdPartyIssues","si","side-effect-confirmation","sidebar","sidebar-position","sidebar-position-bottom","sidebar-position-right","sidebar-test-replace-page-with-object","sign-count","signCount","simulate","simulate-custom-quota","site","sites","size","size-adjust","size-delta","sk","skip-anonymous-scripts","skip-content-scripts","skip-stack-frames-pattern","skip-waiting","sl","slot","slow","slow-4g","sm-script","sm-stylesheet","small","smaller","snippet","socket","some_id","sort-by","source-code","source-file","source-line","source-location","source-map-failed","source-map-infobar-disabled","source-map-loaded","source-map-skipped","source-map-skipped-infobar-disabled","source-message","source-networkRequest","source-order-viewer","source-parse","source-relatedCode","source-stacktrace","source-symbol","source-url","source.xhr-breakpoints","sources","sources.add-folder-to-workspace","sources.add-to-watch","sources.add-xhr-fetch-breakpoint","sources.callstack","sources.close-all","sources.close-editor-tab","sources.create-snippet","sources.csp-violation-breakpoints","sources.decrement-css","sources.decrement-css-by-ten","sources.dom-breakpoints","sources.error","sources.event-listener-breakpoints","sources.global-listeners","sources.go-to-line","sources.go-to-member","sources.increment-css","sources.increment-css-by-ten","sources.js-breakpoints","sources.jump-to-breakpoint","sources.jump-to-next-location","sources.jump-to-previous-location","sources.new-snippet","sources.next-editor-tab","sources.object-properties","sources.previous-editor-tab","sources.quick","sources.remove-all-xhr-fetch-breakpoints","sources.remove-xhr-fetch-breakpoint","sources.rename","sources.reveal-in-navigator-sidebar","sources.save","sources.save-all","sources.scope-chain","sources.search","sources.search-sources-tab","sources.switch-file","sources.threads","sources.toggle-debugger-sidebar","sources.toggle-navigator-sidebar","sources.watch","sources.xhr-breakpoints","speak","speak-as","speculative-loads","spread","sq","sr","sr-latn","src","srgb","stack-trace","stalled","standard-emulated-device-list","start","start-new-chat","start-recording","start-time","start-url","start-view","static-global-setting","static-synced-setting","status","step","step-actions","step-editor","step-over","step-view","stop","stop-color","stop-ignoring-this-retainer","stop-opacity","storage","storage-bucket","storage-buckets","storage-items-view.clear-all","storage-items-view.delete-selected","storage-items-view.refresh","storage-key","storage.clear-site-data","store-as-global-variable","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","style-attribute","style-properties","style-sheet-header","style-sheet-id","styles","styles-metrics","stylesheet","subdomain","subgrid","submit","subtree-modified","suffix","suggestion","supports","surface-duo","surface-pro-7","suspend","sv","sw","sw-scope","symbols","sync-preferences","sync-tag","syntax","system","system-preferred","system-ui","ta","tab-0","tab-1","tab-2","tab-3","tab-4","tab-5","tab-size","table-layout","tag-name","take-screenshot","target","target-crashed","target-selector","targets","task-duration","te","terms-of-service","terms-of-service-accepted","terms-of-service.console-insights","test","test-action","test-device","test-font","test-setting","test-setting-true","test-sidebar","text-align","text-align-last","text-anchor","text-autospace","text-box","text-box-edge","text-box-trim","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-editor-auto-detect-indent","text-editor-auto-detect-indent-false","text-editor-autocompletion","text-editor-bracket-closing","text-editor-bracket-closing-false","text-editor-bracket-matching","text-editor-bracket-matching-false","text-editor-code-folding","text-editor-code-folding-false","text-editor-indent","text-editor-tab-moves-focus","text-editor-tab-moves-focus-true","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-node","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-spacing","text-spacing-trim","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","th","third","third-parties","third-party-tree","third-property","this-origin","throttling-conditions","throttling.calibrate","throttling.calibrate-cancel","thumbs-down","thumbs-up","time","timeline","timeline-alternative-navigation","timeline-capture-layers-and-pictures","timeline-capture-selector-stats","timeline-compiled-sources","timeline-counters-graph-documents","timeline-counters-graph-gpu-memory-used-kb","timeline-counters-graph-js-event-listeners","timeline-counters-graph-js-heap-size-used","timeline-counters-graph-nodes","timeline-debug-mode","timeline-dim-third-parties","timeline-dim-unrelated-events","timeline-disable-js-sampling","timeline-enhanced-traces","timeline-experimental-insights","timeline-flamechart-main-view-group-expansion","timeline-flamechart-network-view-group-expansion","timeline-invalidation-tracking","timeline-layout-shift-details","timeline-overview","timeline-scope","timeline-settings-pane","timeline-settings-toggle","timeline-show-all-events","timeline-show-extension-data","timeline-show-memory","timeline-show-postmessage-events","timeline-show-screenshots","timeline-show-settings-toolbar","timeline-status","timeline-tree-current-thread","timeline-tree-group-by","timeline-user-has-opened-sidebar-once","timeline-v8-runtime-call-stats","timeline.animations","timeline.annotation-sidebar.annotation-entries-link","timeline.annotation-sidebar.annotation-entry-label","timeline.annotation-sidebar.annotation-time-range","timeline.annotation-sidebar.delete","timeline.annotations-tab","timeline.annotations.","timeline.annotations.create-entries-link","timeline.annotations.create-entry-label","timeline.annotations.create-entry-link","timeline.annotations.delete-entry-annotations","timeline.annotations.entry-annotation-create","timeline.annotations.entry-label-input","timeline.annotations.time-range-label-input","timeline.back-to-live-metrics","timeline.breadcrumb-select","timeline.clear","timeline.configure","timeline.create-breadcrumb","timeline.disable","timeline.download-after-error","timeline.enable","timeline.extension","timeline.field-data.configure","timeline.field-data.disable","timeline.field-data.enable","timeline.field-data.settings","timeline.field-data.setup","timeline.field-metric-value","timeline.flame-chart-view","timeline.gpu","timeline.history-item","timeline.ignore-list","timeline.ignore-list-new-regex.checkbox","timeline.ignore-list-new-regex.text","timeline.ignore-list-pattern","timeline.ignore-list-pattern.remove","timeline.insight-ask-ai.cls-culprits","timeline.insight-ask-ai.dismiss-field-mismatch","timeline.insight-ask-ai.document-latency","timeline.insight-ask-ai.dom-size","timeline.insight-ask-ai.field-mismatch","timeline.insight-ask-ai.font-display","timeline.insight-ask-ai.forced-reflow","timeline.insight-ask-ai.image-delivery","timeline.insight-ask-ai.inp","timeline.insight-ask-ai.lcp-by-phase","timeline.insight-ask-ai.lcp-discovery","timeline.insight-ask-ai.long-critical-network-tree","timeline.insight-ask-ai.render-blocking-requests","timeline.insight-ask-ai.slow-css-selector","timeline.insight-ask-ai.third-parties","timeline.insight-ask-ai.viewport","timeline.insights-tab","timeline.insights.cache","timeline.insights.cls-culprits","timeline.insights.dismiss-field-mismatch","timeline.insights.document-latency","timeline.insights.dom-size","timeline.insights.duplicated-javascript","timeline.insights.field-mismatch","timeline.insights.font-display","timeline.insights.forced-reflow","timeline.insights.image-delivery","timeline.insights.inp","timeline.insights.lcp-by-phase","timeline.insights.lcp-discovery","timeline.insights.legacy-javascript","timeline.insights.long-critical-network-tree","timeline.insights.modern-http","timeline.insights.render-blocking-requests","timeline.insights.slow-css-selector","timeline.insights.third-parties","timeline.insights.viewport","timeline.interactions","timeline.jump-to-next-frame","timeline.jump-to-previous-frame","timeline.landing.clear-log","timeline.landing.field-cls","timeline.landing.field-inp","timeline.landing.field-lcp","timeline.landing.interaction-event-timing","timeline.landing.interactions-log","timeline.landing.layout-shift-event-score","timeline.landing.layout-shifts-log","timeline.landing.local-cls","timeline.landing.local-inp","timeline.landing.local-lcp","timeline.landing.show-cls-cluster","timeline.landing.show-inp-interaction","timeline.layout-shifts","timeline.load-from-file","timeline.network","timeline.next-recording","timeline.overlays.candy-striped-time-range","timeline.overlays.cursor-timestamp-marker","timeline.overlays.entries-link","timeline.overlays.entry-label","timeline.overlays.entry-outline-error","timeline.overlays.entry-outline-info","timeline.overlays.time-range","timeline.overlays.timespan-breakdown","timeline.overlays.timings-marker","timeline.previous-recording","timeline.record-reload","timeline.reveal-in-network","timeline.save-to-file","timeline.save-to-file-more-options","timeline.save-to-file-with-annotations","timeline.save-to-file-without-annotations","timeline.select-classic-navigation","timeline.select-modern-navigation","timeline.settings","timeline.setup","timeline.shortcuts-dialog-toggle","timeline.show-history","timeline.sidebar","timeline.sidebar-insights-category-select","timeline.sidebar-open","timeline.stop-recording","timeline.thread.auction-worklet","timeline.thread.cpu-profile","timeline.thread.frame","timeline.thread.main","timeline.thread.other","timeline.thread.pool","timeline.thread.rasterizer","timeline.thread.worker","timeline.timings","timeline.toggle-insight.cache","timeline.toggle-insight.cls-culprits","timeline.toggle-insight.document-latency","timeline.toggle-insight.dom-size","timeline.toggle-insight.duplicated-javascript","timeline.toggle-insight.font-display","timeline.toggle-insight.forced-reflow","timeline.toggle-insight.image-delivery","timeline.toggle-insight.inp","timeline.toggle-insight.lcp-by-phase","timeline.toggle-insight.lcp-discovery","timeline.toggle-insight.legacy-javascript","timeline.toggle-insight.long-critical-network-tree","timeline.toggle-insight.modern-http","timeline.toggle-insight.render-blocking-requests","timeline.toggle-insight.slow-css-selector","timeline.toggle-insight.third-parties","timeline.toggle-insight.viewport","timeline.toggle-recording","timeline.treemap.duplicated-javascript-insight","timeline.user-had-shortcuts-dialog-opened-once","timelineOverviewMode","timeout","timer","times","times-new-roman","timestamp","timeupdate","timezone","timezone-id","timing","timing-info","title","toggle-accessibility-tree","toggle-property-and-continue-editing","toggle-recording","toggle-similar-issues","toggle-url-decoding","top","top-layer","total","total-duration","touch","touch-action","touchcancel","touchend","touchmove","touchstart","tr","track-configuration-enter","track-configuration-exit","tracking-sites-details","trailing","transfer-size","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","transport","tree","tritanopia","trust-tokens","trusted-type-violation","trustedtype-policy-violation","trustedtype-sink-violation","type","types","u2f","ua-type","uc-browser-android-mobile","uc-browser-i-os","uc-browser-windows-phone","ui-monospace","ui-rounded","ui-sans-serif","ui-serif","ui-theme","ui-theme-dark","ui-theme-default","ui-theme-system-preferred","uiTheme","uk","unavailable","unblock","undefined","undo","unicode-bidi","unicode-range","unit","unload","unregister","unset","unused-size","update","update-settings","update-timeline","update-timing-table","upload","ur","url","usage","usb","use-code-with-caution","use-custom-accepted-encodings","use-source-map-scopes","user-agent","user-flow-name","user-handle","user-invalid","user-select","user-shortcuts","user-valid","user-verification","userHandle","utf-8","uz","valid","value","value-1","value-2","value-3","vary","vary-header","vector-effect","verbose","verdana","vertical-align","very-slow","vh","vi","view-all","view-all-rules","view-all-speculations","view-computed-value","view-details","view-parsed","view-source","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-capture-mode","view-transition-class","view-transition-group","view-transition-name","views-location-override","virtual-authenticators","visibility","visited","volumechange","vs-code","vw","waiting","waiting-entry-inspect","warning","wasm","wasm-auto-stepping","wasm-auto-stepping-false","watch-test-expression","watch-test-object","waterfall","web+coffee","web+pwinter","web-audio","web-socket-frames","web-socket-messages","web-workers","webassembly","webauthn","webauthn-authenticators","webauthn-pane","webauthn.active-authenticator","webauthn.add-authenticator","webauthn.export-credential","webauthn.remove-authenticator","webauthn.remove-credential","webbundle","webbundle-explainer","webbundle-request","webgl-error-fired","webgl-warning-fired","webp-format-disabled","webp-format-disabled-true","websocket","whats-new","wheel","white-space","white-space-collapse","widows","width","will-change","window","window-controls-overlay","windows","word-break","word-spacing","word-wrap","worker","workspace","workspace-folder-exclude-pattern","writing-mode","x","x-large","x-offset","x-small","xhr","xx-large","xx-small","xy","y","y-offset","z-index","zh","zh-hk","zh-tw","zoo","zoom","zstd","zu"]),a="jslog";function s(e){return e.hasAttribute(a)}function l(e){return p(e.getAttribute(a)||"")}var c;!function(e){e[e.TreeItem=1]="TreeItem",e[e.Close=2]="Close",e[e.Counter=3]="Counter",e[e.Drawer=4]="Drawer",e[e.Resizer=5]="Resizer",e[e.Toggle=6]="Toggle",e[e.Tree=7]="Tree",e[e.TextField=8]="TextField",e[e.AnimationClip=9]="AnimationClip",e[e.Section=10]="Section",e[e.SectionHeader=11]="SectionHeader",e[e.Timeline=12]="Timeline",e[e.CSSRuleHeader=13]="CSSRuleHeader",e[e.Expand=14]="Expand",e[e.ToggleSubpane=15]="ToggleSubpane",e[e.ControlPoint=16]="ControlPoint",e[e.Toolbar=17]="Toolbar",e[e.Popover=18]="Popover",e[e.BreakpointMarker=19]="BreakpointMarker",e[e.DropDown=20]="DropDown",e[e.Adorner=21]="Adorner",e[e.Gutter=22]="Gutter",e[e.MetricsBox=23]="MetricsBox",e[e.MetricsBoxPart=24]="MetricsBoxPart",e[e.DOMBreakpoint=26]="DOMBreakpoint",e[e.Action=29]="Action",e[e.FilterDropdown=30]="FilterDropdown",e[e.Dialog=31]="Dialog",e[e.BezierCurveEditor=32]="BezierCurveEditor",e[e.BezierPresetCategory=34]="BezierPresetCategory",e[e.Preview=35]="Preview",e[e.Canvas=36]="Canvas",e[e.ColorEyeDropper=37]="ColorEyeDropper",e[e.Link=44]="Link",e[e.Item=46]="Item",e[e.PaletteColorShades=47]="PaletteColorShades",e[e.Panel=48]="Panel",e[e.ShowStyleEditor=50]="ShowStyleEditor",e[e.Slider=51]="Slider",e[e.CssColorMix=52]="CssColorMix",e[e.Value=53]="Value",e[e.Key=54]="Key",e[e.PieChart=59]="PieChart",e[e.PieChartSlice=60]="PieChartSlice",e[e.PieChartTotal=61]="PieChartTotal",e[e.ElementsBreadcrumbs=62]="ElementsBreadcrumbs",e[e.PanelTabHeader=66]="PanelTabHeader",e[e.Menu=67]="Menu",e[e.TableRow=68]="TableRow",e[e.TableHeader=69]="TableHeader",e[e.TableCell=70]="TableCell",e[e.Pane=72]="Pane",e[e.ResponsivePresets=73]="ResponsivePresets",e[e.DeviceModeRuler=74]="DeviceModeRuler",e[e.MediaInspectorView=75]="MediaInspectorView"}(c||(c={}));const d=new Set;function u(i){if("string"==typeof i&&i.length&&!n.has(i)&&!d.has(i)){if(t.Runtime.Runtime.queryParam("debugFrontend")||e.InspectorFrontendHost.isUnderTest()||"Test"===localStorage.getItem("veDebugLoggingEnabled")){const e=((new Error).stack||"").split("\n").slice(3).join("\n");console.error(`Unknown VE context: ${i}${e}`)}d.add(i)}}function p(e){const t=e.replace(/ /g,"").split(";"),i=e=>t.find((t=>t.startsWith(e)))?.substr(e.length),o=function(e){return c[e]??0}(t[0]);if(0===o)throw new Error("Unkown VE: "+e);const r={ve:o},n=i("context:");n?.trim().length&&(u(n),r.context=n);const a=i("parent:");a&&(r.parent=a);const s=i("track:");if(s){r.track={};for(const e of s.split(","))e.startsWith("keydown:")?r.track.keydown=e.substr(8):r.track[e]=!0}return r}function g(e,t){const i=[e];return"string"==typeof t&&t.trim().length&&(i.push(`context: ${t}`),u(t)),{context:function(e){return("number"==typeof e||"string"==typeof e&&e.length)&&i.push(`context: ${e}`),u(t),this},parent:function(e){return i.push(`parent: ${e}`),this},track:function(e){return i.push(`track: ${Object.entries(e).map((([e,t])=>!0!==t?`${e}: ${t}`:e)).join(", ")}`),this},toString:function(){return i.join("; ")}}}const m=new WeakMap;function h(){const e=new BigInt64Array(1);return crypto.getRandomValues(e),Number(e[0]>>11n)}function b(e,t,i){if(t.parent&&w.has(t.parent)&&e instanceof Element)for(i=w.get(t.parent)?.(e);i instanceof Element&&!s(i);)i=i.parentElementOrShadowHost()??void 0;if(m.has(e)){const t=m.get(e);return i&&t.parent!==f(i)&&(t.parent=f(i)),t}const o={impressionLogged:!1,processed:!1,config:t,veid:h(),parent:i?f(i):null,size:new DOMRect(0,0,0,0)};return m.set(e,o),o}function f(e){return m.get(e)||null}const w=new Map;function v(e,t){if(w.has(e))throw new Error(`Parent provider with the name '${e} is already registered'`);w.set(e,t)}const k=new WeakMap;function y(e,t){k.set(e,t)}v("mapped",(e=>k.get(e)));let x=!1,z=null,j=null;const C=new WeakMap;let E;function T(e,t){x=e,e&&!z&&(z=document.createElement("div"),z.classList.add("ve-debug"),z.style.position="absolute",z.style.background="var(--sys-color-cdt-base-container)",z.style.borderRadius="2px",z.style.padding="8px",z.style.boxShadow="var(--drop-shadow)",z.style.zIndex="100000",document.body.appendChild(z)),E=t,!e&&j&&(j.style.backgroundColor="",j.style.outline="")}function S(e){const t=f(e);x&&t&&!t.processedForDebugging&&(e instanceof HTMLElement?function(e,t){"OPTION"===e.tagName?t.parent?.selectOpen&&z&&(z.innerHTML+="<br>"+P(t.config),t.processedForDebugging=!0):(e.addEventListener("mousedown",(e=>{e.currentTarget===j&&E&&z&&x&&(E(z.textContent||""),e.stopImmediatePropagation(),e.preventDefault())}),{capture:!0}),e.addEventListener("mouseenter",(()=>{if(!x)return;j&&(j.style.backgroundColor="",j.style.outline=""),e.style.backgroundColor="#A7C3E4",e.style.outline="dashed 1px #7327C6",j=e,r(z);const i=[t];let o=t.parent;for(;o;)i.unshift(o),o=o.parent;!function(e,t){if(!z)return;if(z.style.display="block",z.textContent=e,t){const e=z.getBoundingClientRect();window.innerHeight<t.bottom+e.height+8?z.style.top=t.top-e.height-8+"px":z.style.top=`${t.bottom+8}px`,window.innerWidth<t.left+e.width?(z.style.right="0px",z.style.left=""):(z.style.right="",z.style.left=`${t.left}px`)}}(i.map((e=>{return t=e.config,`${c[t.ve]}${t.context?`: ${t.context}`:""}`;var t})).join(" > "),e.getBoundingClientRect())}),{capture:!0}),e.addEventListener("mouseleave",(()=>{e.style.backgroundColor="",e.style.outline="",r(z),z.style.display="none"}),{capture:!0}),t.processedForDebugging=!0)}(e,t):function(e,t){let i=C.get(e);i||(i=document.createElement("div"),i.classList.add("ve-debug"),i.style.background="black",i.style.color="white",i.style.zIndex="100000",i.textContent=P(t.config),C.set(e,i),setTimeout((()=>{t.size?.width&&t.size?.height||(i?.parentElement?.removeChild(i),C.delete(e))}),1e4));const o=parent instanceof HTMLElement?parent:C.get(parent)||z;r(o),o.classList.contains("ve-debug")?(i.style.marginLeft="10px",o.appendChild(i)):(i.style.position="absolute",o.insertBefore(i,o.firstChild))}(e,t))}function I(e,t,i){const o=localStorage.getItem("veDebugLoggingEnabled");if(o)switch(o){case"Intuitive":!function(e,t,i){const o={event:e,ve:t?c[t?.config.ve]:void 0,veid:t?.veid,context:t?.config.context,time:Date.now()-N,...i};L(o),R(o)}(e,t,i);break;case"Test":!function(e,t){"SettingAccess"!==e&&(F=null);R({interaction:`${e}: ${M.get(t?.veid||0)||(t?.veid?"<UNKNOWN>":"")}`}),G()}(e,t);break;case"AdHocAnalysis":!function(e,t,i){const o=t?q.get(t.veid):null;if(o){const t={time:Date.now()-N,type:e,...i};L(t),o.interactions.push(t)}}(e,t,i)}}function L(e){for(const t in e){const i=t;void 0===e[i]&&delete e[i]}}function D(e){switch(localStorage.getItem("veDebugLoggingEnabled")){case"Intuitive":!function(e){const t=new Map;for(const i of e){const e={event:"Impression",ve:c[i.config.ve],context:i?.config.context,width:i.size.width,height:i.size.height,veid:i.veid};if(L(e),t.set(i.veid,e),i.parent&&t.has(i.parent?.veid)){const o=t.get(i.parent?.veid);o.children=o.children||[],o.children.push(e)}else e.parent=i.parent?.veid}const i=[...t.values()].filter((e=>"parent"in e));1===i.length?(i[0].time=Date.now()-N,R(i[0])):R({event:"Impression",children:i,time:Date.now()-N})}(e);break;case"Test":!function(e){F||(F={impressions:[]},A.push(F));for(const t of e){let e="";t.parent&&(e=(M.get(t.parent.veid)||"<UNKNOWN>")+" > "),e+=c[t.config.ve],t.config.context&&(e+=": "+t.config.context),M.set(t.veid,e),F.impressions.push(e)}G()}(e);break;case"AdHocAnalysis":!function(e){for(const t of e){const e=t=>{const i={ve:c[t.config.ve],veid:t.veid,width:t.size?.width,height:t.size?.height,context:t.config.context};return L(i),t.parent&&(i.parent=e(t.parent)),i},i={...e(t),interactions:[],time:Date.now()-N};q.set(t.veid,i),R(i)}}(e)}}globalThis.setVeDebuggingEnabled=T;const M=new Map;let F=null;const q=new Map;function P(e){const t=[c[e.ve]];return e.context&&t.push(`context: ${e.context}`),e.parent&&t.push(`parent: ${e.parent}`),e.track&&t.push(`track: ${Object.entries(e.track).map((([e,t])=>`${e}${"string"==typeof t?`: ${t}`:""}`)).join(", ")}`),t.join("; ")}const A=[];function R(e){const t=localStorage.getItem("veDebugLoggingEnabled");t&&(A.push(e),"Intuitive"===t&&console.info("VE Debug:",e))}function $(e,t="Intuitive"){e?localStorage.setItem("veDebugLoggingEnabled",t):localStorage.removeItem("veDebugLoggingEnabled")}function H(e,t){return[...t.strings.map((t=>e[t]?`"${e[t]}"`:"$NullString")),...t.numerics.map((t=>e[t]??"null")),...t.booleans.map((t=>e[t]??"$NullBool"))].join(", ")}class O{#e=new Map;add(e){this.#e.set(e.veid,e)}get(e){return this.#e.get(e)}getArea(e){let t=(e.width||0)*(e.height||0);const i=e.parent?this.#e.get(e.parent?.veid):null;if(!i)return t;const o=this.getArea(i);return t>o&&(t=o),t}get data(){return[...this.#e.values()].filter((e=>this.getArea(e))).sort(((e,t)=>this.getArea(t)-this.getArea(e)))}}function B(e,t,i,o){let r=e;for(const e of t.data){if(!("children"in r))return;let t=r.children[r.children.length-1];("Impression"===t?.type?t.veid:null)!==e.veid&&(r.children.push(...o),o.length=0,t={type:"Impression",ve:e.ve,veid:e.veid,context:e.context,time:i,children:[]},r.children.push(t)),r=t}}function V(e){if("Impression"===e.type){for(;1===e.children.length;)"Impression"===e.children[0].type&&(e.children=e.children[0].children);for(const t of e.children)V(t)}}let N=Date.now();function U(e,t){if("interaction"in t&&"interaction"in e)return t.interaction===e.interaction;if("impressions"in t&&"impressions"in e){const i=new Set(e.impressions),o=[...new Set(t.impressions)].filter((e=>!i.has(e)));return!Boolean(o.length)}return!1}let W=null;function K(e){const t=[];let i="";for(const o of e.sort())if(o!==i){for(;!o.startsWith(i);)i=i.substr(0,i.lastIndexOf(" > "));t.push(" ".repeat(i.length)+o.substr(i.length)),i=o}return t.join("\n")}let _=0;function G(){if(!W)return;const e=[...A];for(let t=0;t<W.expectedEvents.length;++t){const i=W.expectedEvents[t];for(;;){if(e.length<=t)return void(W.missingEvents=W.expectedEvents.slice(t));if(U(e[t],i))break;e.splice(t,1)}}_=A.length-e.length+W.expectedEvents.length,W.success(),W=null}globalThis.setVeDebugLoggingEnabled=$,globalThis.getUnmatchedVeEvents=function(){return console.error(_),A.slice(_).map((e=>"interaction"in e?e.interaction:K(e.impressions))).join("\n")},globalThis.veDebugEventsLog=A,globalThis.findVeDebugImpression=function(e,t){const i=o=>{if("Impression"===o.event&&o.veid===e)return o;let r=0;for(const e of o.children||[]){const n=i(e);if(n){if(t){const e=[];return e[r]=n,{...o,children:e}}return n}++r}};return i({children:A})},globalThis.exportAdHocAnalysisLogForSql=function(){const e={strings:["ve","context"],numerics:["veid","width","height"],booleans:[]},t={strings:["type","context"],numerics:["width","height","mouseButton","time"],booleans:["width","height","mouseButton","time"]},i=e=>e.map(((e,t)=>`$${t+1} as ${e}`)).join(", "),o=t=>`$VeFields(${H(t,e)}, ${t.parent?`STRUCT(${o(t.parent)})`:null})`,r=e=>`$Interaction(${H(e,t)})`,n=A;console.log(`\nDEFINE MACRO NullString CAST(null AS STRING);\nDEFINE MACRO NullBool CAST(null AS BOOL);\nDEFINE MACRO VeFields ${i([...e.strings,...e.numerics,"parent"])};\nDEFINE MACRO Interaction STRUCT(${i([...t.strings,...t.numerics,...t.booleans])});\nDEFINE MACRO Entry STRUCT($1, $2 AS interactions, $3 AS time);\n\n// This fake entry put first fixes nested struct fields names being lost\nDEFINE MACRO FakeVeFields $VeFields("", $NullString, 0, 0, 0, $1);\nDEFINE MACRO FakeVe STRUCT($FakeVeFields($1));\nDEFINE MACRO FakeEntry $Entry($FakeVeFields($FakeVe($FakeVe($FakeVe($FakeVe($FakeVe($FakeVe($FakeVe(null)))))))), ([]), 0);\n\nWITH\n  processed_logs AS (\n      SELECT * FROM UNNEST([\n        $FakeEntry,\n        ${n.map((e=>`$Entry(${o(e)}, ([${e.interactions.map(r).join(", ")}]), ${e.time})`)).join(", \n")}\n      ])\n    )\n\n\n\nSELECT * FROM processed_logs;`)},globalThis.buildStateFlow=function(){const e=function(){const e=[];for(const t of A){e.push(t);const i=t.veid;for(const o of t.interactions)e.push({...o,veid:i})}return e.sort(((e,t)=>e.time-t.time)),e}(),t=new O,i={type:"Session",children:[]};let o=e[0].time;const r=[];for(const n of e){if(n.time>o+1e3&&(B(i,t,o,r),r.length=0),"type"in n)if("Resize"===n.type){const e=t.get(n.veid);if(!e)continue;const i=t.getArea(e);e.width=n.width,e.height=n.height,0!==t.getArea(e)&&0!==i&&r.push(n)}else r.push(n);else t.add(n);o=n.time}return B(i,t,o,r),V(i),i},globalThis.expectVeEvents=async function(e){if(W)throw new Error("VE events expectation already set. Cannot set another one until the previous is resolved");const{promise:t,resolve:i,reject:o}=Promise.withResolvers();W={expectedEvents:e,success:i,fail:o},G();const r=setTimeout((()=>{W?.missingEvents&&W.fail(new Error("Missing VE Events: "+W.missingEvents.map((e=>"interaction"in e?e.interaction:K(e.impressions))).join("\n")))}),5e3);return await t.finally((()=>{clearTimeout(r)}))};function J(e,t){const i=e.getBoundingClientRect(),o=function(e,t){const i=Math.max(e.left,t.left),o=Math.min(e.left+e.width,t.left+t.width);if(i<=o){const r=Math.max(e.top,t.top),n=Math.min(e.top+e.height,t.top+t.height);if(r<=n)return new DOMRect(i,r,o-i,n-r)}return null}(t,i),r=Math.max(Math.min(10,i.width,i.height),1);return!o||o.width<r||o.height<r?null:o}async function X(t){const i=await Promise.all(t.map((async e=>{const t=f(e);r(t);const i={id:t.veid,type:t.config.ve};return void 0!==t.config.context&&(i.context=await ie(t.config.context)),t.parent&&(i.parent=t.parent.veid),t.size&&(i.width=Math.round(t.size.width),i.height=Math.round(t.size.height)),i})));i.length&&(e.InspectorFrontendHost.InspectorFrontendHostInstance.recordImpression({impressions:i}),D(t.map((e=>f(e)))))}const Y=(t,i)=>{const o=f(t);if(!o)return;o.size=i;const r={veid:o.veid,width:o.size.width,height:o.size.height};e.InspectorFrontendHost.InspectorFrontendHostInstance.recordResize(r),I("Resize",o,{width:Math.round(i.width),height:Math.round(i.height)})},Q=t=>(i,o,r)=>{const n=f(i);if(!n)return;const a={veid:n.veid,doubleClick:Boolean(r?.doubleClick)};o instanceof MouseEvent&&"sourceCapabilities"in o&&o.sourceCapabilities&&(a.mouseButton=o.button),t.schedule((async()=>{e.InspectorFrontendHost.InspectorFrontendHostInstance.recordClick(a),I("Click",n,{mouseButton:a.mouseButton,doubleClick:a.doubleClick})}))},Z=t=>async i=>{const o=f(i.currentTarget);r(o);const n={veid:o.veid};t.schedule((async()=>{e.InspectorFrontendHost.InspectorFrontendHostInstance.recordHover(n),I("Hover",o)}),"Delayed")};let ee=null;const te=t=>async(i,o,r)=>{if(!(o instanceof KeyboardEvent))return;const n=i?f(i):null,a="string"==typeof n?.config.track?.keydown?n.config.track.keydown:"";if(a.length&&!a.split("|").includes(o.code)&&!a.split("|").includes(o.key))return;const s={veid:n?.veid};!r&&a?.length&&(r=function(e){if(!(e instanceof KeyboardEvent))return;const t=e.key,i=t.toLowerCase(),o=[];e.shiftKey&&t!==i&&o.push("shift");e.ctrlKey&&o.push("ctrl");e.altKey&&o.push("alt");e.metaKey&&o.push("meta");return o.push(i),o.join("-")}(o)),ee&&r&&ee!==r&&t.process?.(),ee=r||null,t.schedule((async()=>{r&&(s.context=await ie(r)),e.InspectorFrontendHost.InspectorFrontendHostInstance.recordKeyDown(s),I("KeyDown",n,{context:r}),ee=null}))};async function ie(e){if(void 0===e)return;const t=parseInt(e,10);if(!isNaN(t))return t;if(!crypto.subtle)return 3735928559;const i=(new TextEncoder).encode(e),o=await crypto.subtle.digest("SHA-1",i);return new DataView(o).getInt32(0,!0)}async function oe(t,i){let o,r;"string"==typeof i?r=i:"number"!=typeof i&&"boolean"!=typeof i||(o=Number(i));const n={name:t,numericValue:o,stringValue:r};e.InspectorFrontendHost.InspectorFrontendHostInstance.recordSettingAccess(n),I("SettingAccess",null,{name:t,numericValue:o,stringValue:r})}let re=new WeakMap;function ne(e){return re.get(e||ce)||[]}function ae(e){return re.has(e||ce)}function se(e){return[...ne(e)]}function le(e){re.delete(e||ce)}const ce={},de={schedule:async()=>{}};let ue=de,pe=de,ge=de,me=de,he=de,be=de;const fe=new MutationObserver(Le),we=new ResizeObserver(Be),ve=new IntersectionObserver(Be),ke=[],ye=new Map,xe=new Set;function ze(e){for(const t of e)fe.observe(t,{attributes:!0,childList:!0,subtree:!0}),t.querySelectorAll("[popover]")?.forEach((e=>e.addEventListener("toggle",Le)))}let je=!1;async function Ce(e){je=!0,ue=e?.processingThrottler||new i.Throttler.Throttler(500),pe=e?.keyboardLogThrottler||new i.Throttler.Throttler(3e3),ge=e?.hoverLogThrottler||new i.Throttler.Throttler(1e3),me=e?.dragLogThrottler||new i.Throttler.Throttler(1250),he=e?.clickLogThrottler||new i.Throttler.Throttler(500),be=e?.resizeLogThrottler||new i.Throttler.Throttler(200),N=Date.now(),"Intuitive"===localStorage.getItem("veDebugLoggingEnabled")&&R({event:"SessionStart"}),await Ee(document)}async function Ee(e){ke.push(e),["interactive","complete"].includes(e.readyState)&&await Fe(),e.addEventListener("visibilitychange",Le),e.addEventListener("scroll",Le),ze([e.body])}async function Te(){await pe.schedule((async()=>{}),"AsSoonAsPossible"),je=!1,re=new WeakMap;for(const e of ke)e.removeEventListener("visibilitychange",Le),e.removeEventListener("scroll",Le);fe.disconnect(),we.disconnect(),ve.disconnect(),ke.length=0,De.clear(),ue=de,ye.clear(),xe.clear()}async function Se(){for(;he.process;)await he.processCompleted;for(;pe.process;)await pe.processCompleted}function Ie(){for(const e of xe)qe(e)}function Le(){ue&&ue.schedule((()=>o.read("processForLogging",Fe)))}const De=new Map,Me=e=>{const t=e.ownerDocument,i=De.get(t)||new DOMRect(0,0,t.defaultView?.innerWidth||0,t.defaultView?.innerHeight||0);return De.set(t,i),i};async function Fe(){if(document.hidden)return;const t=performance.now(),{loggables:i,shadowRoots:o}=function(e){const t=[],i=[],o=[],r=(e,t)=>{for(const i of e)o.push({element:i,parent:t})};for(const t of e)r(t.body.children);let n=0;for(;;){const e=o[n++];if(!e)break;const{element:a}=e;if("template"===a.localName)continue;let{parent:l}=e;s(a)&&(t.push({element:a,parent:l}),l=a),"slot"===a.localName&&a.assignedElements().length?r(a.assignedElements(),l):a.shadowRoot?(i.push(a.shadowRoot),r(a.shadowRoot.children,l)):r(a.children,l)}return{loggables:t,shadowRoots:i}}(ke),r=[];ze(o);const n=[void 0];for(const{element:t,parent:o}of i){const i=b(t,l(t),o);if(!i.impressionLogged){const e=J(t,Me(t)),n="OPTION"===t.tagName&&i.parent?.selectOpen;(e&&(!o||i.parent?.impressionLogged)||n)&&(e&&(i.size=e),r.push(t),i.impressionLogged=!0)}if(i.impressionLogged&&ae(t)&&n.push(t),!i.processed){const o=e=>t=>{const i=t.currentTarget;He(t),Q(he)(i,t,{doubleClick:e})};i.config.track?.click&&(t.addEventListener("click",o(!1),{capture:!0}),t.addEventListener("auxclick",o(!1),{capture:!0}),t.addEventListener("contextmenu",o(!1),{capture:!0})),i.config.track?.dblclick&&t.addEventListener("dblclick",o(!0),{capture:!0});const r=i.config.track?.hover;r&&(t.addEventListener("mouseover",Z(ge),{capture:!0}),t.addEventListener("mouseout",(()=>ge.schedule(Pe,"AsSoonAsPossible")),{capture:!0}));const n=i.config.track?.drag;n&&(t.addEventListener("pointerdown",$e,{capture:!0}),document.addEventListener("pointerup",He,{capture:!0}),document.addEventListener("dragend",He,{capture:!0})),i.config.track?.change&&(t.addEventListener("input",(e=>{e instanceof InputEvent&&(i.pendingChangeContext&&i.pendingChangeContext!==e.inputType&&qe(t),i.pendingChangeContext=e.inputType,xe.add(t))}),{capture:!0}),t.addEventListener("change",(e=>{const o=e?.target??t;["checkbox","radio"].includes(o.type)&&(i.pendingChangeContext=o.checked?"on":"off"),qe(t)}),{capture:!0}),t.addEventListener("focusout",(()=>{i.pendingChangeContext&&qe(t)}),{capture:!0}));const a=i.config.track?.keydown;if(a&&t.addEventListener("keydown",(e=>te(pe)(e.currentTarget,e)),{capture:!0}),i.config.track?.resize&&(we.observe(t),ve.observe(t)),"SELECT"===t.tagName){const o=e=>{Q(he)(t,e),i.selectOpen||(i.selectOpen=!0,Le())};t.addEventListener("click",o,{capture:!0}),t.addEventListener("keydown",(t=>{const i=t;(!e.Platform.isMac()&&!i.altKey||"ArrowDown"!==i.code&&"ArrowUp"!==i.code)&&(i.altKey||i.ctrlKey||"F4"!==i.code)||o(t)}),{capture:!0}),t.addEventListener("keypress",(t=>{const i=t;(" "===i.key||!e.Platform.isMac()&&"\r"===i.key)&&o(t)}),{capture:!0}),t.addEventListener("change",(e=>{for(const i of t.selectedOptions)f(i)?.config.track?.click&&Q(he)(i,e)}),{capture:!0})}i.processed=!0}S(t)}for(let e=0;e<n.length;++e){const t=n[e];for(const{loggable:e,config:i,parent:o}of se(t)){const t=b(e,i,o);S(e),r.push(e),t.impressionLogged=!0,ae(e)&&n.push(e)}le(t)}r.length&&(await Se(),await async function(){for(;be.process;)await be.processCompleted}(),Ie(),await X(r)),e.userMetrics.visualLoggingProcessingDone(performance.now()-t)}function qe(t){const i=f(t);i&&(!async function(t){const i=f(t);r(i);const o={veid:i.veid},n=i.pendingChangeContext;n&&(o.context=await ie(n)),e.InspectorFrontendHost.InspectorFrontendHostInstance.recordChange(o),I("Change",i,{context:n})}(t),delete i.pendingChangeContext,xe.delete(t))}async function Pe(){}let Ae=0,Re=0;function $e(t){var i;t instanceof MouseEvent&&(Ae=t.screenX,Re=t.screenY,(i=me,async t=>{const o=f(t.currentTarget);r(o);const n={veid:o.veid};i.schedule((async()=>{e.InspectorFrontendHost.InspectorFrontendHostInstance.recordDrag(n),I("Drag",o)}),"Delayed")})(t))}function He(e){e instanceof MouseEvent&&(Math.abs(e.screenX-Ae)>=50||Math.abs(e.screenY-Re)>=50||me.schedule(Pe,"AsSoonAsPossible"))}function Oe(e,t){for(;t;){if(t===e)return!0;t=t.parent}return!1}async function Be(e){for(const t of e){const e=t.target,i=f(e),o=J(e,Me(e))||new DOMRect(0,0,0,0);if(!i?.size)continue;let r=!1;for(const t of ye.keys()){if(t===e)continue;const o=f(t);if(Oe(o,i)){r=!0;break}Oe(i,o)&&ye.delete(t)}r||(ye.set(e,o),be.schedule((async()=>{ye.size&&(await Se(),Ie());for(const[e,t]of ye.entries()){const i=f(e);i&&((Math.abs(t.width-i.size.width)>=50||Math.abs(t.height-i.size.height)>=50)&&Y(e,t))}ye.clear()}),"Delayed"))}}const Ve=(e,t,i={})=>Q(he)(e,t,i),Ne=(e,t)=>Y(e,t),Ue=async(e,t,i)=>await te(pe)(e,t,i);function We(e,t,i){je&&(!function(e,t,i){const o=ne(i);o.push({loggable:e,config:t,parent:i}),re.set(i||ce,o)}(e,p(t),i||void 0),Le())}async function Ke(e){if(!e)return!1;const t=await ie(e);return!!t&&[431010711,-1313957874,-1093325535].includes(t)}const _e=g.bind(null,"Action"),Ge=g.bind(null,"Adorner"),Je=g.bind(null,"AnimationClip"),Xe=g.bind(null,"BezierCurveEditor"),Ye=g.bind(null,"BezierPresetCategory"),Qe=g.bind(null,"BreakpointMarker"),Ze=g.bind(null,"Canvas"),et=g.bind(null,"Close"),tt=g.bind(null,"ColorEyeDropper"),it=g.bind(null,"Counter"),ot=g.bind(null,"ControlPoint"),rt=g.bind(null,"CssColorMix"),nt=g.bind(null,"CSSRuleHeader"),at=g.bind(null,"DeviceModeRuler"),st=g.bind(null,"DOMBreakpoint"),lt=g.bind(null,"Drawer"),ct=g.bind(null,"DropDown"),dt=g.bind(null,"ElementsBreadcrumbs"),ut=g.bind(null,"Expand"),pt=g.bind(null,"FilterDropdown"),gt=g.bind(null,"Gutter"),mt=g.bind(null,"Dialog"),ht=g.bind(null,"Item"),bt=g.bind(null,"Key"),ft=g.bind(null,"Link"),wt=g.bind(null,"MediaInspectorView"),vt=g.bind(null,"Menu"),kt=g.bind(null,"MetricsBox"),yt=g.bind(null,"PaletteColorShades"),xt=g.bind(null,"Pane"),zt=g.bind(null,"Panel"),jt=g.bind(null,"PanelTabHeader"),Ct=g.bind(null,"PieChart"),Et=g.bind(null,"PieChartSlice"),Tt=g.bind(null,"PieChartTotal"),St=g.bind(null,"Popover"),It=g.bind(null,"Preview"),Lt=g.bind(null,"Resizer"),Dt=g.bind(null,"ResponsivePresets"),Mt=g.bind(null,"ShowStyleEditor"),Ft=g.bind(null,"Slider"),qt=g.bind(null,"Section"),Pt=g.bind(null,"SectionHeader"),At=g.bind(null,"TableRow"),Rt=g.bind(null,"TableCell"),$t=g.bind(null,"TableHeader"),Ht=g.bind(null,"TextField"),Ot=g.bind(null,"Timeline"),Bt=g.bind(null,"Toggle"),Vt=g.bind(null,"Toolbar"),Nt=g.bind(null,"ToggleSubpane"),Ut=g.bind(null,"Tree"),Wt=g.bind(null,"TreeItem"),Kt=g.bind(null,"Value");export{_e as action,Ee as addDocument,Ge as adorner,Je as animationClip,Xe as bezierCurveEditor,Ye as bezierPresetCategory,Qe as breakpointMarker,Ze as canvas,et as close,tt as colorEyeDropper,ot as controlPoint,it as counter,rt as cssColorMix,nt as cssRuleHeader,at as deviceModeRuler,mt as dialog,st as domBreakpoint,lt as drawer,ct as dropDown,dt as elementsBreadcrumbs,ut as expand,pt as filterDropdown,gt as gutter,Ke as isUnderInspection,ht as item,bt as key,ft as link,Ve as logClick,X as logImpressions,Ue as logKeyDown,Ne as logResize,oe as logSettingAccess,wt as mediaInspectorView,vt as menu,kt as metricsBox,yt as paletteColorShades,xt as pane,zt as panel,jt as panelTabHeader,Ct as pieChart,Et as pieChartSlice,Tt as pieChartTotal,St as popover,It as preview,We as registerLoggable,v as registerParentProvider,Lt as resizer,Dt as responsivePresets,qt as section,Pt as sectionHeader,y as setMappedParent,$ as setVeDebugLoggingEnabled,T as setVeDebuggingEnabled,Mt as showStyleEditor,Ft as slider,Ce as startLogging,Te as stopLogging,Rt as tableCell,$t as tableHeader,At as tableRow,Ht as textField,Ot as timeline,Bt as toggle,Nt as toggleSubpane,Vt as toolbar,Ut as tree,Wt as treeItem,Kt as value};
