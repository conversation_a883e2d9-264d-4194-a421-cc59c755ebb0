import"../icon_button/icon_button.js";import*as e from"../../../core/common/common.js";import*as t from"../../../core/i18n/i18n.js";import{html as i,render as n}from"../../lit/lit.js";var r=`.link-icon{vertical-align:sub;margin-right:0.5ch}.link{padding:var(--issue-link-padding,4px 0 0 0);text-decoration:var(--issue-link-text-decoration,underline);cursor:pointer;font-size:var(--issue-link-font-size,14px);color:var(--sys-color-primary);outline-offset:2px;border:none;background:none;font-family:inherit}.link:focus:not(:focus-visible){outline:none}.pending-link{opacity:75%;pointer-events:none;cursor:default;text-decoration:none}.disabled-link{pointer-events:none;cursor:default;text-decoration:none}\n/*# sourceURL=${import.meta.resolve("./surveyLink.css")} */\n`;const o={openingSurvey:"Opening survey …",thankYouForYourFeedback:"Thank you for your feedback",anErrorOccurredWithTheSurvey:"An error occurred with the survey"},s=t.i18n.registerUIStrings("ui/components/survey_link/SurveyLink.ts",o),a=t.i18n.getLocalizedString.bind(void 0,s);class h extends HTMLElement{#e=this.attachShadow({mode:"open"});#t="";#i=e.UIString.LocalizedEmptyString;#n=()=>{};#r=()=>{};#o="Checking";set data(e){this.#t=e.trigger,this.#i=e.promptText,this.#n=e.canShowSurvey,this.#r=e.showSurvey,this.#s()}#s(){this.#o="Checking",this.#n(this.#t,(({canShowSurvey:e})=>{this.#o=e?"ShowLink":"DontShowLink",this.#a()}))}#h(){this.#o="Sending",this.#a(),this.#r(this.#t,(({surveyShown:e})=>{this.#o=e?"SurveyShown":"Failed",this.#a()}))}#a(){if("Checking"===this.#o||"DontShowLink"===this.#o)return;let e=this.#i;"Sending"===this.#o?e=a(o.openingSurvey):"SurveyShown"===this.#o?e=a(o.thankYouForYourFeedback):"Failed"===this.#o&&(e=a(o.anErrorOccurredWithTheSurvey));let t="";"Sending"===this.#o?t="pending-link":"Failed"!==this.#o&&"SurveyShown"!==this.#o||(t="disabled-link");const s="ShowLink"!==this.#o,h=i`
      <style>${r}</style>
      <button class="link ${t}" tabindex=${s?"-1":"0"} .disabled=${s} aria-disabled=${s} @click=${this.#h}>
        <devtools-icon class="link-icon" .data=${{iconName:"review",color:"var(--sys-color-primary)",width:"var(--issue-link-icon-size, 16px)",height:"var(--issue-link-icon-size, 16px)"}}></devtools-icon><!--
      -->${e}
      </button>
    `;n(h,this.#e,{host:this})}}customElements.define("devtools-survey-link",h);var c=Object.freeze({__proto__:null,SurveyLink:h});export{c as SurveyLink};
