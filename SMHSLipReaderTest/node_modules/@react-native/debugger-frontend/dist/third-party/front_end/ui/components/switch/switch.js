import{render as e,html as s,nothing as o}from"../../lit/lit.js";import*as r from"../../visual_logging/visual_logging.js";var t={cssText:`:host{position:relative;display:inline-block;width:26px;height:var(--sys-size-8)}input{opacity:0%;width:0;height:0}.slider{box-sizing:border-box;position:absolute;cursor:pointer;left:0;top:0;width:100%;height:100%;background-color:var(--sys-color-surface-variant);border:1px solid var(--sys-color-outline);border-radius:var(--sys-shape-corner-full);transition:background-color 80ms linear}.slider::before{position:absolute;content:"";height:var(--sys-size-5);width:var(--sys-size-5);border-radius:var(--sys-shape-corner-full);top:calc(50% - 4px);left:3px;background-color:var(--sys-color-outline);transition:transform 80ms linear,background-color 80ms linear,width 80ms linear,height 80ms linear,top 80ms linear,left 80ms linear}input:focus-visible + .slider{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px}input:checked{& + .slider{background-color:var(--sys-color-primary);border:1px solid var(--sys-color-primary)}& + .slider::before{left:11px;height:var(--sys-size-6);width:var(--sys-size-6);top:calc(50% - 6px);background-color:var(--sys-color-on-primary)}}input:disabled:not(:checked){& + .slider{background-color:transparent;border-color:var(--sys-color-state-disabled)}& + .slider::before{background-color:var(--sys-color-state-disabled)}}input:disabled:checked{& + .slider{background-color:var(--sys-color-state-disabled-container);border-color:transparent}& + .slider::before{background-color:var(--sys-color-surface)}}@media (forced-colors: active){.slider::before,\n  input:checked + .slider::before{background-color:ButtonText}input:disabled:not(:checked) + .slider,\n  input:disabled:checked + .slider{background-color:transparent;border-color:GrayText}input:disabled:not(:checked) + .slider::before,\n  input:disabled:checked + .slider::before{background-color:GrayText}}\n/*# sourceURL=${import.meta.resolve("./switch.css")} */\n`};const i=new CSSStyleSheet;i.replaceSync(t.cssText);class c extends Event{checked;static eventName="switchchange";constructor(e){super(c.eventName),this.checked=e}}class a extends HTMLElement{#e=this.attachShadow({mode:"open"});#s=!1;#o=!1;#r="";connectedCallback(){this.#e.adoptedStyleSheets=[i],this.#t()}set checked(e){this.#s=e,this.#t()}get checked(){return this.#s}set disabled(e){this.#o=e,this.#t()}get disabled(){return this.#o}get jslogContext(){return this.#r}set jslogContext(e){this.#r=e,this.#t()}#i=e=>{this.#s=e.target.checked,this.dispatchEvent(new c(this.#s))};#t(){const t=this.#r&&r.toggle(this.#r).track({change:!0});e(s`
    <label role="button" jslog=${t||o}>
      <input type="checkbox"
        @change=${this.#i}
        ?disabled=${this.#o}
        .checked=${this.#s}
      >
      <span class="slider" @click=${e=>e.stopPropagation()}></span>
    </label>
    `,this.#e,{host:this})}}customElements.define("devtools-switch",a);var l=Object.freeze({__proto__:null,Switch:a,SwitchChangeEvent:c});export{l as Switch};
