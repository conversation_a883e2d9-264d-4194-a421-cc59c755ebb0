import*as e from"../../../core/common/common.js";import*as t from"../../../core/host/host.js";import*as n from"../../../core/root/root.js";let o;const s=new Map;class r extends EventTarget{setting;themeNameInternal="default";computedStyleOfHTML=e.Lazy.lazy((()=>window.getComputedStyle(document.documentElement)));#e=new Set([document]);#t;#n;#o=()=>this.#s();#r=()=>this.fetchColorsAndApplyHostTheme();constructor(e){super(),this.setting=e,this.#t=window.matchMedia("(prefers-color-scheme: dark)"),this.#n=window.matchMedia("(forced-colors: active)"),this.#t.addEventListener("change",this.#o),this.#n.addEventListener("change",this.#o),e.addChangeListener(this.#o),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.ColorThemeChanged,this.#r)}#h(){this.#t.removeEventListener("change",this.#o),this.#n.removeEventListener("change",this.#o),this.setting.removeChangeListener(this.#o),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(t.InspectorFrontendHostAPI.Events.ColorThemeChanged,this.#r)}static hasInstance(){return void 0!==o}static instance(e={forceNew:null,setting:null}){const{forceNew:t,setting:n}=e;if(!o||t){if(!n)throw new Error(`Unable to create theme support: setting must be provided: ${(new Error).stack}`);o&&o.#h(),o=new r(n)}return o}addDocumentToTheme(e){this.#e.add(e),this.#i(e)}getComputedValue(e,t=null){let n=s.get(t);n||(n=new Map,s.set(t,n));let o=n.get(e);if(!o){const s=t?window.getComputedStyle(t):this.computedStyleOfHTML();if("symbol"==typeof s)throw new Error(`Computed value for property (${e}) could not be found on documentElement.`);o=s.getPropertyValue(e).trim(),o&&n.set(e,o)}return o}themeName(){return this.themeNameInternal}appendStyle(e,{cssText:t}){const n=document.createElement("style");n.textContent=t,e.appendChild(n)}#s(){for(const e of this.#e)this.#a(e)}#a(e){const t=window.matchMedia("(forced-colors: active)").matches,o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"default",r="systemPreferred"===this.setting.get()||t;this.themeNameInternal=r?o:this.setting.get(),e.documentElement.classList.toggle("theme-with-dark-background","dark"===this.themeNameInternal);n.Runtime.hostConfig.isOffTheRecord;e.documentElement.classList.toggle("baseline-grayscale",!0),s.clear(),this.dispatchEvent(new h)}static clearThemeCache(){s.clear()}fetchColorsAndApplyHostTheme(){for(const e of this.#e)this.#i(e)}#i(n){const o=e.Settings.moduleSetting("chrome-theme-colors").get();if(t.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()||!o)return void this.#a(n);const s=n.querySelector("link[href*='//theme/colors.css']"),r=n.createElement("link");r.setAttribute("href",`devtools://theme/colors.css?sets=ui,chrome&version=${(new Date).getTime().toString()}`),r.setAttribute("rel","stylesheet"),r.setAttribute("type","text/css"),r.onload=()=>{s&&s.remove(),this.#a(n)},n.body.appendChild(r)}}class h extends Event{static eventName="themechange";constructor(){super(h.eventName,{bubbles:!0,composed:!0})}}export{h as ThemeChangeEvent,r as ThemeSupport};
