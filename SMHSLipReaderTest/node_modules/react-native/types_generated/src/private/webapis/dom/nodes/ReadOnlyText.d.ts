/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<f32d1d4b660a77ad6caa63b33c15ac98>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/src/private/webapis/dom/nodes/ReadOnlyText.js
 */

import ReadOnlyCharacterData from "./ReadOnlyCharacterData";
declare class ReadOnlyText extends ReadOnlyCharacterData {
  /**
   * @override
   */
  get nodeName(): string;
  /**
   * @override
   */
  get nodeType(): number;
}
export default ReadOnlyText;
