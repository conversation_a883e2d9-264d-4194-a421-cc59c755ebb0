/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<cb9320bb76b5426c47d8cc1ba7d59cc4>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/src/private/devsupport/devmenu/DevMenu.js
 */

/**
 * The DevMenu module exposes methods for interacting with the Dev Menu in development.
 */
/**
 * The DevMenu module exposes methods for interacting with the Dev Menu in development.
 */
type DevMenuStatic = {
  /**
   * Show the Dev Menu.
   */
  show(): void;
};
declare const DevMenu: DevMenuStatic;
declare const $$DevMenu: typeof DevMenu;
declare type $$DevMenu = typeof $$DevMenu;
export default $$DevMenu;
