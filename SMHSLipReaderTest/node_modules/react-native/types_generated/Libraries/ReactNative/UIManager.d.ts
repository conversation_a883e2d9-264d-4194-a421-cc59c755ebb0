/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<1cc6d2f42a5d6f80879d7366661a10d3>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/ReactNative/UIManager.js
 */

import type { UIManagerJSInterface } from "../Types/UIManagerJSInterface";
declare const UIManager: UIManagerJSInterface;
declare const $$UIManager: typeof UIManager;
declare type $$UIManager = typeof $$UIManager;
export default $$UIManager;
