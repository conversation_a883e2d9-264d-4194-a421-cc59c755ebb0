/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<0b2addb90dc4f99bb1f09394d5fa23fe>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/ReactNative/NativeI18nManager.js
 */

export * from "../../src/private/specs_DEPRECATED/modules/NativeI18nManager";
import NativeI18nManager from "../../src/private/specs_DEPRECATED/modules/NativeI18nManager";
declare const $$NativeI18nManager: typeof NativeI18nManager;
declare type $$NativeI18nManager = typeof $$NativeI18nManager;
export default $$NativeI18nManager;
