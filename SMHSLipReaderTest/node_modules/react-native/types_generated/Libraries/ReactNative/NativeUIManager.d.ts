/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<ced9c7c438314f0e1821fccea3e911fc>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/ReactNative/NativeUIManager.js
 */

export * from "../../src/private/specs_DEPRECATED/modules/NativeUIManager";
import NativeUIManager from "../../src/private/specs_DEPRECATED/modules/NativeUIManager";
declare const $$NativeUIManager: typeof NativeUIManager;
declare type $$NativeUIManager = typeof $$NativeUIManager;
export default $$NativeUIManager;
