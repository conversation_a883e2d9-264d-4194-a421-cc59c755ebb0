/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<a6d755fa2dd3fa28541d37cc32b69ad1>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/ReactNative/ReactFabricPublicInstance/ReactNativeAttributePayload.js
 */

import type { AttributeConfiguration } from "../../Renderer/shims/ReactNativeTypes";
export declare function create(props: Object, validAttributes: AttributeConfiguration): null | Object;
export declare function diff(prevProps: Object, nextProps: Object, validAttributes: AttributeConfiguration): null | Object;
