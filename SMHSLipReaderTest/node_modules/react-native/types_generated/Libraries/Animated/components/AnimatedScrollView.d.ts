/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<280b262a7f1961a247d6b99480ad376f>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/components/AnimatedScrollView.js
 */

import type { AnimatedComponentType } from "../createAnimatedComponent";
import ScrollView, { type ScrollViewProps } from "../../Components/ScrollView/ScrollView";
import * as React from "react";
type AnimatedScrollViewInstance = React.ComponentRef<typeof ScrollView>;
declare const AnimatedScrollView: AnimatedComponentType<ScrollViewProps, AnimatedScrollViewInstance>;
/**
 * @see https://github.com/facebook/react-native/commit/b8c8562
 */
declare const $$AnimatedScrollView: typeof AnimatedScrollView;
declare type $$AnimatedScrollView = typeof $$AnimatedScrollView;
export default $$AnimatedScrollView;
