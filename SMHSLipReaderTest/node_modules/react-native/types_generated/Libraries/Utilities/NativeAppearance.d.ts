/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<4dac48df64e4ba4b718b0b1b9806a680>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Utilities/NativeAppearance.js
 */

export type * from "../../src/private/specs_DEPRECATED/modules/NativeAppearance";
import NativeAppearance from "../../src/private/specs_DEPRECATED/modules/NativeAppearance";
declare const $$NativeAppearance: typeof NativeAppearance;
declare type $$NativeAppearance = typeof $$NativeAppearance;
export default $$NativeAppearance;
