/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<538e81ad34e0d9832a54f6393fe6ffcc>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Utilities/Platform.js.flow
 */

import type { PlatformType } from "./PlatformTypes";
declare const $$Platform: PlatformType;
declare type $$Platform = typeof $$Platform;
export default $$Platform;
