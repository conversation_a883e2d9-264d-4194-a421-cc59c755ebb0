/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<ef97a493885301a0cd1aedcd9d49d3bc>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/EventEmitter/RCTDeviceEventEmitter.js
 */

import type { IEventEmitter } from "../vendor/emitter/EventEmitter";
type RCTDeviceEventDefinitions = {
  [name: string]: Array<any>;
};
declare const $$RCTDeviceEventEmitter: IEventEmitter<RCTDeviceEventDefinitions>;
declare type $$RCTDeviceEventEmitter = typeof $$RCTDeviceEventEmitter;
export default $$RCTDeviceEventEmitter;
