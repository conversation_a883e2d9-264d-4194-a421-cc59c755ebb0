/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<42cbbc1df2b68d456fcc4db2c8aeec59>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/ScrollView/ScrollViewContext.js
 */

import * as React from "react";
type Value = {
  horizontal: boolean;
} | null;
declare const ScrollViewContext: React.Context<Value>;
declare const $$ScrollViewContext: typeof ScrollViewContext;
declare type $$ScrollViewContext = typeof $$ScrollViewContext;
export default $$ScrollViewContext;
export declare const HORIZONTAL: Value;
export declare type HORIZONTAL = typeof HORIZONTAL;
export declare const VERTICAL: Value;
export declare type VERTICAL = typeof VERTICAL;
