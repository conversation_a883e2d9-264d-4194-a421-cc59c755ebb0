/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<b66e43bf2755e611bb9733c103d3fac6>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/Switch/AndroidSwitchNativeComponent.js
 */

export * from "../../../src/private/specs_DEPRECATED/components/AndroidSwitchNativeComponent";
export { default } from "../../../src/private/specs_DEPRECATED/components/AndroidSwitchNativeComponent";
