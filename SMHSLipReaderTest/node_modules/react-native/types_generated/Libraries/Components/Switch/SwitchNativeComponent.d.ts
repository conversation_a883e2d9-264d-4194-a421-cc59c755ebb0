/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<1faa821192358efd8b615376daee8ba0>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/Switch/SwitchNativeComponent.js
 */

export * from "../../../src/private/specs_DEPRECATED/components/SwitchNativeComponent";
export { default } from "../../../src/private/specs_DEPRECATED/components/SwitchNativeComponent";
