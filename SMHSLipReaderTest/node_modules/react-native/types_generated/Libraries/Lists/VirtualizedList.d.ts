/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<6cf77422746dc8145e1e53c5c519d0d5>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Lists/VirtualizedList.js
 */

import VirtualizedLists from "@react-native/virtualized-lists";
type VirtualizedListType = typeof VirtualizedLists.VirtualizedList;
declare const VirtualizedListComponent: VirtualizedListType;
export type { ListRenderItemInfo, ListRenderItem, Separators, VirtualizedListProps } from "@react-native/virtualized-lists";
declare const $$VirtualizedList: typeof VirtualizedListComponent;
declare type $$VirtualizedList = typeof $$VirtualizedList;
export default $$VirtualizedList;
