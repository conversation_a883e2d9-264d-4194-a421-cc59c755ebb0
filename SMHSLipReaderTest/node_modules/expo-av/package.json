{"name": "expo-av", "version": "16.0.7", "description": "Expo universal module for Audio and Video playback", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["expo", "react-native", "audio", "video"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-av"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/av/", "devDependencies": {"expo-module-scripts": "^5.0.7"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*", "react-native-web": "*"}, "peerDependenciesMeta": {"react-native-web": {"optional": true}}, "gitHead": "088e79428be97cf3ee11fc93e0e5a1fc1c8bea1e"}