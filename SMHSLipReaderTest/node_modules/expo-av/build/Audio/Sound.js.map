{"version": 3, "file": "Sound.js", "sourceRoot": "", "sources": ["../../src/Audio/Sound.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAEtF,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAC7D,OAAO,EAEL,aAAa,EAKb,0BAA0B,EAC1B,+CAA+C,EAC/C,iBAAiB,GAElB,MAAM,OAAO,CAAC;AAEf,OAAO,UAAU,MAAM,eAAe,CAAC;AAoDvC,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,OAAO,KAAK;IAChB,OAAO,GAAY,KAAK,CAAC;IACzB,QAAQ,GAAY,KAAK,CAAC;IAC1B,IAAI,GAAkB,IAAI,CAAC;IAC3B,iBAAiB,GAAkB,IAAI,CAAC;IACxC,qBAAqB,GAAgB,IAAI,CAAC;IAC1C,cAAc,GAA6B,EAAE,CAAC;IAC9C,aAAa,GAAG,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC;IACnD,8BAA8B,GAAW,GAAG,CAAC;IAC7C,uBAAuB,GAAgD,IAAI,CAAC;IAC5E,iBAAiB,GAA4C,IAAI,CAAC;IAClE,sBAAsB,GAAwB,IAAI,CAAC;IAEnD,oDAAoD;IACpD,MAAM,CAAC,MAAM,GAAG,KAAK,EACnB,MAAwB,EACxB,gBAAuC,EAAE,EACzC,yBAAsE,IAAI,EAC1E,gBAAyB,IAAI,EACP,EAAE;QACxB,OAAO,CAAC,IAAI,CACV,2GAA2G,CAC5G,CAAC;QACF,OAAO,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE,sBAAsB,EAAE,aAAa,CAAC,CAAC;IACzF,CAAC,CAAC;IAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACH,MAAM,CAAC,WAAW,GAAG,KAAK,EACxB,MAAwB,EACxB,gBAAuC,EAAE,EACzC,yBAAsE,IAAI,EAC1E,gBAAyB,IAAI,EACP,EAAE;QACxB,MAAM,KAAK,GAAU,IAAI,KAAK,EAAE,CAAC;QACjC,KAAK,CAAC,yBAAyB,CAAC,sBAAsB,CAAC,CAAC;QACxD,MAAM,MAAM,GAAqB,MAAM,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAC7F,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;IAC3B,CAAC,CAAC;IAEF,mBAAmB;IAEnB,uCAAuC,CAAC,MAAwB;QAC9D,MAAM,8BAA8B,GAClC,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,iBAAiB;YACjD,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,8BAA8B,CAAC;QAE1F,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAC5E,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,qBAAqB,GAAG,IAAI,IAAI,EAAE,CAAC;YACxC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qCAAqC,CACzC,SAA0C;QAE1C,sBAAsB,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,uCAAuC,CAAC,MAAM,CAAC,CAAC;YACrD,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAEO,kCAAkC;QACxC,IAAI,UAAU,CAAC,uCAAuC,IAAI,IAAI,EAAE,CAAC;YAC/D,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;gBACvD,OAAO,CAAC,IAAI,CACV,0DAA0D;oBACxD,kHAAkH;oBAClH,wHAAwH,CAC3H,CAAC;gBACF,OAAO;YACT,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,mBAAmB,CAAC,SAAS,EAAE,0BAA0B,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CACb,+GAA+G,CAChH,CAAC;QACJ,CAAC;QACD,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CACb,8EAA8E,OAAO,IAAI;iBACtF,IAAI,sBAAsB,CAC9B,CAAC;QACJ,CAAC;QAED,UAAU,CAAC,uCAAuC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAC7F,CAAC;IAED,6BAA6B,GAAG,CAAC,EAC/B,GAAG,EACH,MAAM,GAIP,EAAE,EAAE;QACH,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,uCAAuC,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;IACH,CAAC,CAAC;IAEF,+BAA+B,GAAG,CAAC,EACjC,GAAG,EACH,QAAQ,GAIT,EAAE,EAAE;QACH,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;IACH,CAAC,CAAC;IAEF,sBAAsB,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAyC,EAAE,EAAE;QACjF,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC;IAEF,iGAAiG;IACjG,wBAAwB;QACtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CACtB,IAAI,CAAC,aAAa,CAAC,WAAW,CAC5B,yBAAyB,EACzB,IAAI,CAAC,6BAA6B,CACnC,EACD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAC,+BAA+B,CAAC,CAC1F,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CACtB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAClF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,mBAAmB;QACjB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc,GAAG,CAAC,KAAa,EAAE,EAAE;QACjC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,uCAAuC,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC;IAEF,0DAA0D;IAC1D,wEAAwE;IAExE,iBAAiB;IAEjB,cAAc,GAAG,KAAK,IAA+B,EAAE;QACrD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,qCAAqC,CAAC,GAAG,EAAE,CACrD,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC,CAAC;QACJ,CAAC;QACD,MAAM,MAAM,GAAqB,iBAAiB,EAAE,CAAC;QACrD,IAAI,CAAC,uCAAuC,CAAC,MAAM,CAAC,CAAC;QACrD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF;;;;;;;;;;;OAWG;IACH,yBAAyB,CAAC,sBAAmE;QAC3F,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,mBAAmB,CAAC,gBAAgD;QAClE,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACH,wBAAwB,CAAC,QAA6B;QACpD,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC;QACvC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,kCAAkC,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,0BAA0B;IAE1B,KAAK,CAAC,SAAS,CACb,MAAwB,EACxB,gBAAuC,EAAE,EACzC,gBAAyB,IAAI;QAE7B,sBAAsB,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YAErB,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAE,GACvC,MAAM,+CAA+C,CAAC,MAAM,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YAE9F,kFAAkF;YAClF,OAAO,IAAI,OAAO,CAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACvD,MAAM,WAAW,GAAG,CAAC,MAAyC,EAAE,EAAE;oBAChE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC;oBAC7B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;oBAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBAChC,IAAI,CAAC,uCAAuC,CAAC,MAAM,CAAC,CAAC;oBACrD,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC,CAAC;gBAEF,MAAM,SAAS,GAAG,CAAC,KAAY,EAAE,EAAE;oBACjC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC;gBAEF,UAAU,CAAC,YAAY,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;YACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YACpD,IAAI,CAAC,uCAAuC,CAAC,MAAM,CAAC,CAAC;YACrD,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,8CAA8C;QAC9E,CAAC;IACH,CAAC;IAED,wDAAwD;IAExD,KAAK,CAAC,cAAc,CAAC,MAA6B;QAChD,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC,qCAAqC,CAAC,GAAG,EAAE,CACrD,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAChD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAgC,EAAE;QAClD,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,IAAI,CAAC,qCAAqC,CAAC,GAAG,EAAE,CACrD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;YAChC,GAAG,MAAM;YACT,cAAc,EAAE,CAAC;YACjB,UAAU,EAAE,IAAI;SACjB,CAAC,CACH,CAAC;IACJ,CAAC;IAED,mEAAmE;IACnE,SAAS,CAAmC;IAC5C,qBAAqB,CAGU;IAC/B,UAAU,CAAmC;IAC7C,SAAS,CAAmC;IAC5C,gBAAgB,CAGe;IAC/B,YAAY,CAImB;IAC/B,cAAc,CAAoE;IAClF,eAAe,CAAmD;IAClE,iBAAiB,CAAqD;IACtE,8BAA8B,CAEC;;AAGjC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import { LegacyEventEmitter, Platform, UnavailabilityError } from 'expo-modules-core';\n\nimport { throwIfAudioIsDisabled } from './AudioAvailability';\nimport {\n  Playback,\n  PlaybackMixin,\n  AVPlaybackSource,\n  AVMetadata,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n  assertStatusValuesInBounds,\n  getNativeSourceAndFullInitialStatusForLoadAsync,\n  getUnloadedStatus,\n  AVPlaybackTolerance,\n} from '../AV';\nimport { PitchCorrectionQuality } from '../Audio';\nimport ExponentAV from '../ExponentAV';\n\n// @needsAudit\nexport type AudioChannel = {\n  /**\n   * All samples for this specific Audio Channel in PCM Buffer format (-1 to 1).\n   */\n  frames: number[];\n};\n\n// @needsAudit\n/**\n * Object passed to the `onAudioSampleReceived` function. Represents a single sample from an audio source.\n * The sample contains all frames (PCM Buffer values) for each channel of the audio, so if the audio is _stereo_ (interleaved),\n * there will be two channels, one for left and one for right audio.\n */\nexport type AudioSample = {\n  /**\n   * An array representing the data from each channel in PCM Buffer format. Array elements are objects in the following format: `{ frames: number[] }`,\n   * where each frame is a number in PCM Buffer format (`-1` to `1` range).\n   */\n  channels: AudioChannel[];\n  /**\n   * A number representing the timestamp of the current sample in seconds, relative to the audio track's timeline.\n   * > **Known issue:** When using the `ExoPlayer` Android implementation, the timestamp is always `-1`.\n   */\n  timestamp: number;\n};\n\n// @needsAudit\nexport type SoundObject = {\n  /**\n   * The newly created and loaded `Sound` object.\n   */\n  sound: Sound;\n  /**\n   * The `PlaybackStatus` of the `Sound` object. See the [AV documentation](/versions/latest/sdk/av) for further information.\n   */\n  status: AVPlaybackStatus;\n};\n\ntype AudioInstance = number | HTMLMediaElement | null;\n\n// @docsMissing\nexport type AudioSampleCallback = ((sample: AudioSample) => void) | null;\n\ndeclare namespace globalThis {\n  let __EXAV_setOnAudioSampleReceivedCallback:\n    | ((key: number, callback: AudioSampleCallback) => void)\n    | undefined;\n}\n\n// @needsAudit\n/**\n * This class represents a sound corresponding to an Asset or URL.\n * @return A newly constructed instance of `Audio.Sound`.\n *\n * @example\n * ```ts\n * const sound = new Audio.Sound();\n * try {\n *   await sound.loadAsync(require('./assets/sounds/hello.mp3'));\n *   await sound.playAsync();\n *   // Your sound is playing!\n *\n *   // Don't forget to unload the sound from memory\n *   // when you are done using the Sound object\n *   await sound.unloadAsync();\n * } catch (error) {\n *   // An error occurred!\n * }\n * ```\n *\n * > Method not described below and the rest of the API for `Audio.Sound` is the same as the imperative playback API for `Video`.\n * > See the [AV documentation](/versions/latest/sdk/av) for further information.\n */\nexport class Sound implements Playback {\n  _loaded: boolean = false;\n  _loading: boolean = false;\n  _key: AudioInstance = null;\n  _lastStatusUpdate: string | null = null;\n  _lastStatusUpdateTime: Date | null = null;\n  _subscriptions: { remove: () => void }[] = [];\n  _eventEmitter = new LegacyEventEmitter(ExponentAV);\n  _coalesceStatusUpdatesInMillis: number = 100;\n  _onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null;\n  _onMetadataUpdate: ((metadata: AVMetadata) => void) | null = null;\n  _onAudioSampleReceived: AudioSampleCallback = null;\n\n  /** @deprecated Use `Sound.createAsync()` instead */\n  static create = async (\n    source: AVPlaybackSource,\n    initialStatus: AVPlaybackStatusToSet = {},\n    onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null,\n    downloadFirst: boolean = true\n  ): Promise<SoundObject> => {\n    console.warn(\n      `Sound.create is deprecated in favor of Sound.createAsync with the same API except for the new method name`\n    );\n    return Sound.createAsync(source, initialStatus, onPlaybackStatusUpdate, downloadFirst);\n  };\n\n  /**\n   * Creates and loads a sound from source.\n   *\n   * ```ts\n   * const { sound } = await Audio.Sound.createAsync(\n   *   source,\n   *   initialStatus,\n   *   onPlaybackStatusUpdate,\n   *   downloadFirst\n   * );\n   *\n   * // Which is equivalent to the following:\n   * const sound = new Audio.Sound();\n   * sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);\n   * await sound.loadAsync(source, initialStatus, downloadFirst);\n   * ```\n   *\n   * @param source The source of the sound. See the [AV documentation](/versions/latest/sdk/av/#playback-api) for details on the possible `source` values.\n   *\n   * @param initialStatus The initial intended `PlaybackStatusToSet` of the sound, whose values will override the default initial playback status.\n   * This value defaults to `{}` if no parameter is passed. See the [AV documentation](/versions/latest/sdk/av) for details on `PlaybackStatusToSet` and the default\n   * initial playback status.\n   *\n   * @param onPlaybackStatusUpdate A function taking a single parameter `PlaybackStatus`. This value defaults to `null` if no parameter is passed.\n   * See the [AV documentation](/versions/latest/sdk/av) for details on the functionality provided by `onPlaybackStatusUpdate`\n   *\n   * @param downloadFirst If set to true, the system will attempt to download the resource to the device before loading. This value defaults to `true`.\n   * Note that at the moment, this will only work for `source`s of the form `require('path/to/file')` or `Asset` objects.\n   *\n   * @example\n   * ```ts\n   * try {\n   *   const { sound: soundObject, status } = await Audio.Sound.createAsync(\n   *     require('./assets/sounds/hello.mp3'),\n   *     { shouldPlay: true }\n   *   );\n   *   // Your sound is playing!\n   * } catch (error) {\n   *   // An error occurred!\n   * }\n   * ```\n   *\n   * @return A `Promise` that is rejected if creation failed, or fulfilled with the `SoundObject` if creation succeeded.\n   */\n  static createAsync = async (\n    source: AVPlaybackSource,\n    initialStatus: AVPlaybackStatusToSet = {},\n    onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null,\n    downloadFirst: boolean = true\n  ): Promise<SoundObject> => {\n    const sound: Sound = new Sound();\n    sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);\n    const status: AVPlaybackStatus = await sound.loadAsync(source, initialStatus, downloadFirst);\n    return { sound, status };\n  };\n\n  // Internal methods\n\n  _callOnPlaybackStatusUpdateForNewStatus(status: AVPlaybackStatus) {\n    const shouldDismissBasedOnCoalescing =\n      this._lastStatusUpdateTime &&\n      JSON.stringify(status) === this._lastStatusUpdate &&\n      Date.now() - this._lastStatusUpdateTime.getTime() < this._coalesceStatusUpdatesInMillis;\n\n    if (this._onPlaybackStatusUpdate != null && !shouldDismissBasedOnCoalescing) {\n      this._onPlaybackStatusUpdate(status);\n      this._lastStatusUpdateTime = new Date();\n      this._lastStatusUpdate = JSON.stringify(status);\n    }\n  }\n\n  async _performOperationAndHandleStatusAsync(\n    operation: () => Promise<AVPlaybackStatus>\n  ): Promise<AVPlaybackStatus> {\n    throwIfAudioIsDisabled();\n    if (this._loaded) {\n      const status = await operation();\n      this._callOnPlaybackStatusUpdateForNewStatus(status);\n      return status;\n    } else {\n      throw new Error('Cannot complete operation because sound is not loaded.');\n    }\n  }\n\n  private _updateAudioSampleReceivedCallback() {\n    if (globalThis.__EXAV_setOnAudioSampleReceivedCallback == null) {\n      if (Platform.OS === 'ios' || Platform.OS === 'android') {\n        console.warn(\n          'expo-av: Failed to set up Audio Sample Buffer callback. ' +\n            \"Do you have 'Remote Debugging' enabled in your app's Developer Menu (https://docs.expo.dev/workflow/debugging)? \" +\n            'Audio Sample Buffer callbacks are not supported while using Remote Debugging, you will need to disable it to use them.'\n        );\n        return;\n      } else {\n        throw new UnavailabilityError('expo-av', 'setOnAudioSampleReceived');\n      }\n    }\n    if (this._key == null) {\n      throw new Error(\n        'Cannot set Audio Sample Buffer callback when the Sound instance has not been successfully loaded/initialized!'\n      );\n    }\n    if (typeof this._key !== 'number') {\n      throw new Error(\n        `Cannot set Audio Sample Buffer callback when Sound instance key is of type ${typeof this\n          ._key}! (expected: number)`\n      );\n    }\n\n    globalThis.__EXAV_setOnAudioSampleReceivedCallback(this._key, this._onAudioSampleReceived);\n  }\n\n  _internalStatusUpdateCallback = ({\n    key,\n    status,\n  }: {\n    key: AudioInstance;\n    status: AVPlaybackStatus;\n  }) => {\n    if (this._key === key) {\n      this._callOnPlaybackStatusUpdateForNewStatus(status);\n    }\n  };\n\n  _internalMetadataUpdateCallback = ({\n    key,\n    metadata,\n  }: {\n    key: AudioInstance;\n    metadata: AVMetadata;\n  }) => {\n    if (this._key === key) {\n      this._onMetadataUpdate?.(metadata);\n    }\n  };\n\n  _internalErrorCallback = ({ key, error }: { key: AudioInstance; error: string }) => {\n    if (this._key === key) {\n      this._errorCallback(error);\n    }\n  };\n\n  // TODO: We can optimize by only using time observer on native if (this._onPlaybackStatusUpdate).\n  _subscribeToNativeEvents() {\n    if (this._loaded) {\n      this._subscriptions.push(\n        this._eventEmitter.addListener(\n          'didUpdatePlaybackStatus',\n          this._internalStatusUpdateCallback\n        ),\n        this._eventEmitter.addListener('didUpdateMetadata', this._internalMetadataUpdateCallback)\n      );\n\n      this._subscriptions.push(\n        this._eventEmitter.addListener('ExponentAV.onError', this._internalErrorCallback)\n      );\n    }\n  }\n\n  _clearSubscriptions() {\n    this._subscriptions.forEach((e) => e.remove());\n    this._subscriptions = [];\n  }\n\n  _errorCallback = (error: string) => {\n    this._clearSubscriptions();\n    this._loaded = false;\n    this._key = null;\n    this._callOnPlaybackStatusUpdateForNewStatus(getUnloadedStatus(error));\n  };\n\n  // ### Unified playback API ### (consistent with Video.js)\n  // All calls automatically call onPlaybackStatusUpdate as a side effect.\n\n  // Get status API\n\n  getStatusAsync = async (): Promise<AVPlaybackStatus> => {\n    if (this._loaded) {\n      return this._performOperationAndHandleStatusAsync(() =>\n        ExponentAV.getStatusForSound(this._key)\n      );\n    }\n    const status: AVPlaybackStatus = getUnloadedStatus();\n    this._callOnPlaybackStatusUpdateForNewStatus(status);\n    return status;\n  };\n\n  /**\n   * Sets a function to be called regularly with the `AVPlaybackStatus` of the playback object.\n   *\n   * `onPlaybackStatusUpdate` will be called whenever a call to the API for this playback object completes\n   * (such as `setStatusAsync()`, `getStatusAsync()`, or `unloadAsync()`), nd will also be called at regular intervals\n   * while the media is in the loaded state.\n   *\n   * Set `progressUpdateIntervalMillis` via `setStatusAsync()` or `setProgressUpdateIntervalAsync()` to modify\n   * the interval with which `onPlaybackStatusUpdate` is called while loaded.\n   *\n   * @param onPlaybackStatusUpdate A function taking a single parameter `AVPlaybackStatus`.\n   */\n  setOnPlaybackStatusUpdate(onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null) {\n    this._onPlaybackStatusUpdate = onPlaybackStatusUpdate;\n    this.getStatusAsync();\n  }\n\n  /**\n   * Sets a function to be called whenever the metadata of the sound object changes, if one is set.\n   * @param onMetadataUpdate A function taking a single object of type `AVMetadata` as a parameter.\n   * @platform ios\n   */\n  setOnMetadataUpdate(onMetadataUpdate: (metadata: AVMetadata) => void) {\n    this._onMetadataUpdate = onMetadataUpdate;\n  }\n\n  /**\n   * Sets a function to be called during playback, receiving the audio sample as parameter.\n   * @param callback A function taking the `AudioSampleCallback` as parameter.\n   */\n  setOnAudioSampleReceived(callback: AudioSampleCallback) {\n    this._onAudioSampleReceived = callback;\n    if (this._key != null) {\n      this._updateAudioSampleReceivedCallback();\n    }\n  }\n\n  // Loading / unloading API\n\n  async loadAsync(\n    source: AVPlaybackSource,\n    initialStatus: AVPlaybackStatusToSet = {},\n    downloadFirst: boolean = true\n  ): Promise<AVPlaybackStatus> {\n    throwIfAudioIsDisabled();\n    if (this._loading) {\n      throw new Error('The Sound is already loading.');\n    }\n    if (!this._loaded) {\n      this._loading = true;\n\n      const { nativeSource, fullInitialStatus } =\n        await getNativeSourceAndFullInitialStatusForLoadAsync(source, initialStatus, downloadFirst);\n\n      // This is a workaround, since using load with resolve / reject seems to not work.\n      return new Promise<AVPlaybackStatus>((resolve, reject) => {\n        const loadSuccess = (result: [AudioInstance, AVPlaybackStatus]) => {\n          const [key, status] = result;\n          this._key = key;\n          this._loaded = true;\n          this._loading = false;\n          this._subscribeToNativeEvents();\n          this._callOnPlaybackStatusUpdateForNewStatus(status);\n          resolve(status);\n        };\n\n        const loadError = (error: Error) => {\n          this._loading = false;\n          reject(error);\n        };\n\n        ExponentAV.loadForSound(nativeSource, fullInitialStatus).then(loadSuccess).catch(loadError);\n      });\n    } else {\n      throw new Error('The Sound is already loaded.');\n    }\n  }\n\n  async unloadAsync(): Promise<AVPlaybackStatus> {\n    if (this._loaded) {\n      this._loaded = false;\n      const key = this._key;\n      this._key = null;\n      const status = await ExponentAV.unloadForSound(key);\n      this._callOnPlaybackStatusUpdateForNewStatus(status);\n      this._clearSubscriptions();\n      return status;\n    } else {\n      return this.getStatusAsync(); // Automatically calls onPlaybackStatusUpdate.\n    }\n  }\n\n  // Set status API (only available while isLoaded = true)\n\n  async setStatusAsync(status: AVPlaybackStatusToSet): Promise<AVPlaybackStatus> {\n    assertStatusValuesInBounds(status);\n    return this._performOperationAndHandleStatusAsync(() =>\n      ExponentAV.setStatusForSound(this._key, status)\n    );\n  }\n\n  async replayAsync(status: AVPlaybackStatusToSet = {}): Promise<AVPlaybackStatus> {\n    if (status.positionMillis && status.positionMillis !== 0) {\n      throw new Error('Requested position after replay has to be 0.');\n    }\n\n    return this._performOperationAndHandleStatusAsync(() =>\n      ExponentAV.replaySound(this._key, {\n        ...status,\n        positionMillis: 0,\n        shouldPlay: true,\n      })\n    );\n  }\n\n  // Methods of the Playback interface that are set via PlaybackMixin\n  playAsync!: () => Promise<AVPlaybackStatus>;\n  playFromPositionAsync!: (\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ) => Promise<AVPlaybackStatus>;\n  pauseAsync!: () => Promise<AVPlaybackStatus>;\n  stopAsync!: () => Promise<AVPlaybackStatus>;\n  setPositionAsync!: (\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ) => Promise<AVPlaybackStatus>;\n  setRateAsync!: (\n    rate: number,\n    shouldCorrectPitch: boolean,\n    pitchCorrectionQuality?: PitchCorrectionQuality\n  ) => Promise<AVPlaybackStatus>;\n  setVolumeAsync!: (volume: number, audioPan?: number) => Promise<AVPlaybackStatus>;\n  setIsMutedAsync!: (isMuted: boolean) => Promise<AVPlaybackStatus>;\n  setIsLoopingAsync!: (isLooping: boolean) => Promise<AVPlaybackStatus>;\n  setProgressUpdateIntervalAsync!: (\n    progressUpdateIntervalMillis: number\n  ) => Promise<AVPlaybackStatus>;\n}\n\nObject.assign(Sound.prototype, PlaybackMixin);\n"]}