{"version": 3, "file": "Recording.js", "sourceRoot": "", "sources": ["../../src/Audio/Recording.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,gBAAgB,EAEhB,oBAAoB,EACpB,kBAAkB,EAElB,QAAQ,GACT,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,cAAc,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAO7E,OAAO,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,KAAK,EAAe,MAAM,SAAS,CAAC;AAC7C,OAAO,EACL,wCAAwC,GAGzC,MAAM,OAAO,CAAC;AACf,OAAO,UAAU,MAAM,eAAe,CAAC;AAEvC,IAAI,eAAe,GAAY,KAAK,CAAC;AACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAE3F;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB;IACvC,OAAO,UAAU,CAAC,mBAAmB,EAAE,CAAC;AAC1C,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB;IAC3C,OAAO,UAAU,CAAC,uBAAuB,EAAE,CAAC;AAC9C,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,oBAAoB,CAAC;IACjD,SAAS,EAAE,mBAAmB;IAC9B,aAAa,EAAE,uBAAuB;CACvC,CAAC,CAAC;AAEH,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,OAAO,SAAS;IACpB,aAAa,GAA6B,IAAI,CAAC;IAC/C,UAAU,GAAY,KAAK,CAAC;IAC5B,gBAAgB,GAAY,KAAK,CAAC;IAClC,oBAAoB,GAAW,CAAC,CAAC;IACjC,IAAI,GAAkB,IAAI,CAAC;IAC3B,wBAAwB,GAA+C,IAAI,CAAC;IAC5E,8BAA8B,GAAkB,IAAI,CAAC;IACrD,6BAA6B,GAAW,wCAAwC,CAAC;IACjF,QAAQ,GAA4B,IAAI,CAAC;IAEzC,mBAAmB;IAEnB,2BAA2B,GAAG,KAAK,EAAE,WAA6B,EAAE,EAAE;QACpE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,oBAAoB,GAAG,WAAW,EAAE,cAAc,IAAI,CAAC,CAAC;QAC7D,eAAe,GAAG,KAAK,CAAC;QACxB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,mEAAmE;IACzG,CAAC,CAAC;IAEF,YAAY,GAAG,KAAK,IAAI,EAAE;QACxB,IAAI,cAAc,EAAE,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,EAAE,CAAC;YACjF,IAAI,CAAC,8BAA8B,GAAG,UAAU,CAC9C,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,6BAA6B,CAC5B,CAAC;YACT,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC9B,CAAC;YAAC,MAAM,CAAC;gBACP,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,eAAe;QACb,IAAI,IAAI,CAAC,8BAA8B,IAAI,IAAI,EAAE,CAAC;YAChD,YAAY,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAClD,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,oCAAoC;QAClC,IAAI,cAAc,EAAE,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,EAAE,CAAC;YACjF,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED,wCAAwC,CAAC,MAAuB;QAC9D,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,EAAE,CAAC;YAC1C,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qCAAqC,CACzC,SAAyC;QAEzC,sBAAsB,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,wCAAwC,CAAC,MAAM,CAAC,CAAC;YACtD,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACH,MAAM,CAAC,WAAW,GAAG,KAAK,EACxB,UAA4B,uBAAuB,CAAC,WAAW,EAC/D,0BAAsE,IAAI,EAC1E,+BAA8C,IAAI,EACxB,EAAE;QAC5B,MAAM,SAAS,GAAc,IAAI,SAAS,EAAE,CAAC;QAC7C,IAAI,4BAA4B,EAAE,CAAC;YACjC,SAAS,CAAC,6BAA6B,GAAG,4BAA4B,CAAC;QACzE,CAAC;QACD,SAAS,CAAC,0BAA0B,CAAC,uBAAuB,CAAC,CAAC;QAC9D,MAAM,SAAS,CAAC,oBAAoB,CAAC;YACnC,GAAG,OAAO;YACV,mBAAmB,EAAE,IAAI;SAC1B,CAAC,CAAC;QACH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,UAAU,EAAE,CAAC;YAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,SAAS,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE;gBAC1C,iFAAiF;gBACjF,6CAA6C;gBAC7C,mCAAmC;YACrC,CAAC,CAAC,CAAC;YACH,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC,CAAC;IAEF,iBAAiB;IAEjB;;;OAGG;IACH,cAAc,GAAG,KAAK,IAA8B,EAAE;QACpD,+CAA+C;QAC/C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,qCAAqC,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,uBAAuB,EAAE,CAAC,CAAC;QAChG,CAAC;QACD,MAAM,MAAM,GAAG;YACb,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,cAAc,EAAE,IAAI,CAAC,oBAAoB;SAC1C,CAAC;QACF,IAAI,CAAC,wCAAwC,CAAC,MAAM,CAAC,CAAC;QACtD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF;;;;;;;;OAQG;IACH,0BAA0B,CAAC,uBAAmE;QAC5F,IAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAC;QACxD,IAAI,uBAAuB,IAAI,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,oCAAoC,EAAE,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,yBAAyB,CAAC,4BAAoC;QAC5D,IAAI,CAAC,6BAA6B,GAAG,4BAA4B,CAAC;QAClE,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED,aAAa;IAEb;;;;;;;;;;OAUG;IACH,KAAK,CAAC,oBAAoB,CACxB,UAA4B,uBAAuB,CAAC,WAAW;QAE/D,sBAAsB,EAAE,CAAC;QAEzB,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CACb,uFAAuF,CACxF,CAAC;QACJ,CAAC;QAED,MAAM,cAAc,GAAG,SAAS,CAAC;QACjC,IACE,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS;YAC1B,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS;YACtB,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;YAC/C,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAC3C,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,mCAAmC,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,WAAW,CAC3C,iCAAiC,EACjC,IAAI,CAAC,2BAA2B,CACjC,CAAC;YACJ,CAAC;YAED,MAAM,EACJ,GAAG,EACH,MAAM,GACP,GAIG,MAAM,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACnD,eAAe,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YAEvB,MAAM,aAAa,GAAG,EAAE,GAAG,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;YACrD,IAAI,CAAC,wCAAwC,CAAC,aAAa,CAAC,CAAC;YAC7D,IAAI,CAAC,oCAAoC,EAAE,CAAC;YAC5C,OAAO,aAAa,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,kBAAkB;QACtB,OAAO,UAAU,CAAC,kBAAkB,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe;QACnB,OAAO,UAAU,CAAC,eAAe,EAAE,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAC7B,OAAO,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,UAAU;QACd,OAAO,IAAI,CAAC,qCAAqC,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC;IAC5F,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,UAAU;QACd,OAAO,IAAI,CAAC,qCAAqC,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC;IAC5F,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;QACD,+FAA+F;QAC/F,sFAAsF;QACtF,IAAI,UAAuC,CAAC;QAC5C,IAAI,SAA4B,CAAC;QACjC,IAAI,CAAC;YACH,UAAU,GAAG,MAAM,UAAU,CAAC,kBAAkB,EAAE,CAAC;QACrD,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,SAAS,GAAG,GAAG,CAAC;QAClB,CAAC;QAED,qFAAqF;QACrF,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,UAAU,EAAE,GAAG,KAAK,SAAS,EAAE,CAAC;YAC3D,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC;QAC7B,CAAC;QAED,6BAA6B;QAC7B,MAAM,UAAU,CAAC,mBAAmB,EAAE,CAAC;QACvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;QAClE,OAAO,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACxD,CAAC;IAED,WAAW;IAEX;;;;;OAKG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,gBAAuC,EAAE,EACzC,yBAAsE,IAAI;QAE1E,OAAO,CAAC,IAAI,CACV,6HAA6H,CAC9H,CAAC;QACF,OAAO,IAAI,CAAC,yBAAyB,CAAC,aAAa,EAAE,sBAAsB,CAAC,CAAC;IAC/E,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,yBAAyB,CAC7B,gBAAuC,EAAE,EACzC,yBAAsE,IAAI;QAE1E,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,CAAC;QACD,OAAO,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,sBAAsB,EAAE,KAAK,CAAC,CAAC;IAC7F,CAAC;;AAGH,OAAO,EAAsB,gBAAgB,EAAyB,CAAC;AAEvE,cAAc,sBAAsB,CAAC;AAErC,cAAc,mBAAmB,CAAC", "sourcesContent": ["import {\n  PermissionResponse,\n  PermissionStatus,\n  PermissionHookOptions,\n  create<PERSON><PERSON><PERSON>H<PERSON>,\n  LegacyEventEmitter,\n  type EventSubscription,\n  Platform,\n} from 'expo-modules-core';\n\nimport { isAudioEnabled, throwIfAudioIsDisabled } from './AudioAvailability';\nimport {\n  RecordingInput,\n  RecordingObject,\n  RecordingOptions,\n  RecordingStatus,\n} from './Recording.types';\nimport { RecordingOptionsPresets } from './RecordingConstants';\nimport { Sound, SoundObject } from './Sound';\nimport {\n  _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n} from '../AV';\nimport ExponentAV from '../ExponentAV';\n\nlet _recorderExists: boolean = false;\nconst eventEmitter = Platform.OS === 'android' ? new LegacyEventEmitter(ExponentAV) : null;\n\n/**\n * Checks user's permissions for audio recording.\n * @return A promise that resolves to an object of type `PermissionResponse`.\n * @platform android\n * @platform ios\n */\nexport async function getPermissionsAsync(): Promise<PermissionResponse> {\n  return ExponentAV.getPermissionsAsync();\n}\n\n/**\n * Asks the user to grant permissions for audio recording.\n * @return A promise that resolves to an object of type `PermissionResponse`.\n * @platform android\n * @platform ios\n */\nexport async function requestPermissionsAsync(): Promise<PermissionResponse> {\n  return ExponentAV.requestPermissionsAsync();\n}\n\n/**\n * Check or request permissions to record audio.\n * This uses both `requestPermissionAsync` and `getPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [permissionResponse, requestPermission] = Audio.usePermissions();\n * ```\n */\nexport const usePermissions = createPermissionHook({\n  getMethod: getPermissionsAsync,\n  requestMethod: requestPermissionsAsync,\n});\n\n// @needsAudit\n/**\n * > **warning** **Warning**: Experimental for web.\n *\n * This class represents an audio recording. After creating an instance of this class, `prepareToRecordAsync`\n * must be called in order to record audio. Once recording is finished, call `stopAndUnloadAsync`. Note that\n * only one recorder is allowed to exist in the state between `prepareToRecordAsync` and `stopAndUnloadAsync`\n * at any given time.\n *\n * Note that your experience must request audio recording permissions in order for recording to function.\n * See the [`Permissions` module](/guides/permissions) for more details.\n *\n * Additionally, audio recording is [not supported in the iOS Simulator](/workflow/ios-simulator/#limitations).\n *\n * @example\n * ```ts\n * const recording = new Audio.Recording();\n * try {\n *   await recording.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);\n *   await recording.startAsync();\n *   // You are now recording!\n * } catch (error) {\n *   // An error occurred!\n * }\n * ```\n *\n * @return A newly constructed instance of `Audio.Recording`.\n * @platform android\n * @platform ios\n */\nexport class Recording {\n  _subscription: EventSubscription | null = null;\n  _canRecord: boolean = false;\n  _isDoneRecording: boolean = false;\n  _finalDurationMillis: number = 0;\n  _uri: string | null = null;\n  _onRecordingStatusUpdate: ((status: RecordingStatus) => void) | null = null;\n  _progressUpdateTimeoutVariable: number | null = null;\n  _progressUpdateIntervalMillis: number = _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS;\n  _options: RecordingOptions | null = null;\n\n  // Internal methods\n\n  _cleanupForUnloadedRecorder = async (finalStatus?: RecordingStatus) => {\n    this._canRecord = false;\n    this._isDoneRecording = true;\n    this._finalDurationMillis = finalStatus?.durationMillis ?? 0;\n    _recorderExists = false;\n    if (this._subscription) {\n      this._subscription.remove();\n      this._subscription = null;\n    }\n    this._disablePolling();\n    return await this.getStatusAsync(); // Automatically calls onRecordingStatusUpdate for the final state.\n  };\n\n  _pollingLoop = async () => {\n    if (isAudioEnabled() && this._canRecord && this._onRecordingStatusUpdate != null) {\n      this._progressUpdateTimeoutVariable = setTimeout(\n        this._pollingLoop,\n        this._progressUpdateIntervalMillis\n      ) as any;\n      try {\n        await this.getStatusAsync();\n      } catch {\n        this._disablePolling();\n      }\n    }\n  };\n\n  _disablePolling() {\n    if (this._progressUpdateTimeoutVariable != null) {\n      clearTimeout(this._progressUpdateTimeoutVariable);\n      this._progressUpdateTimeoutVariable = null;\n    }\n  }\n\n  _enablePollingIfNecessaryAndPossible() {\n    if (isAudioEnabled() && this._canRecord && this._onRecordingStatusUpdate != null) {\n      this._disablePolling();\n      this._pollingLoop();\n    }\n  }\n\n  _callOnRecordingStatusUpdateForNewStatus(status: RecordingStatus) {\n    if (this._onRecordingStatusUpdate != null) {\n      this._onRecordingStatusUpdate(status);\n    }\n  }\n\n  async _performOperationAndHandleStatusAsync(\n    operation: () => Promise<RecordingStatus>\n  ): Promise<RecordingStatus> {\n    throwIfAudioIsDisabled();\n    if (this._canRecord) {\n      const status = await operation();\n      this._callOnRecordingStatusUpdateForNewStatus(status);\n      return status;\n    } else {\n      throw new Error('Cannot complete operation because this recorder is not ready to record.');\n    }\n  }\n\n  /**\n   * Creates and starts a recording using the given options, with optional `onRecordingStatusUpdate` and `progressUpdateIntervalMillis`.\n   *\n   * ```ts\n   * const { recording, status } = await Audio.Recording.createAsync(\n   *   options,\n   *   onRecordingStatusUpdate,\n   *   progressUpdateIntervalMillis\n   * );\n   *\n   * // Which is equivalent to the following:\n   * const recording = new Audio.Recording();\n   * await recording.prepareToRecordAsync(options);\n   * recording.setOnRecordingStatusUpdate(onRecordingStatusUpdate);\n   * await recording.startAsync();\n   * ```\n   *\n   * @param options Options for the recording, including sample rate, bitrate, channels, format, encoder, and extension. If no options are passed to,\n   * the recorder will be created with options `Audio.RecordingOptionsPresets.LOW_QUALITY`. See below for details on `RecordingOptions`.\n   * @param onRecordingStatusUpdate A function taking a single parameter `status` (a dictionary, described in `getStatusAsync`).\n   * @param progressUpdateIntervalMillis The interval between calls of `onRecordingStatusUpdate`. This value defaults to 500 milliseconds.\n   *\n   * @example\n   * ```ts\n   * try {\n   *   const { recording: recordingObject, status } = await Audio.Recording.createAsync(\n   *     Audio.RecordingOptionsPresets.HIGH_QUALITY\n   *   );\n   *   // You are now recording!\n   * } catch (error) {\n   *   // An error occurred!\n   * }\n   * ```\n   *\n   * @return A `Promise` that is rejected if creation failed, or fulfilled with the following dictionary if creation succeeded.\n   */\n  static createAsync = async (\n    options: RecordingOptions = RecordingOptionsPresets.LOW_QUALITY,\n    onRecordingStatusUpdate: ((status: RecordingStatus) => void) | null = null,\n    progressUpdateIntervalMillis: number | null = null\n  ): Promise<RecordingObject> => {\n    const recording: Recording = new Recording();\n    if (progressUpdateIntervalMillis) {\n      recording._progressUpdateIntervalMillis = progressUpdateIntervalMillis;\n    }\n    recording.setOnRecordingStatusUpdate(onRecordingStatusUpdate);\n    await recording.prepareToRecordAsync({\n      ...options,\n      keepAudioActiveHint: true,\n    });\n    try {\n      const status = await recording.startAsync();\n      return { recording, status };\n    } catch (err) {\n      recording.stopAndUnloadAsync().catch((_e) => {\n        // Since there was an issue with starting, when trying calling stopAndUnloadAsync\n        // the promise is rejected which is unhandled\n        // lets catch it since its expected\n      });\n      throw err;\n    }\n  };\n\n  // Get status API\n\n  /**\n   * Gets the `status` of the `Recording`.\n   * @return A `Promise` that is resolved with the `RecordingStatus` object.\n   */\n  getStatusAsync = async (): Promise<RecordingStatus> => {\n    // Automatically calls onRecordingStatusUpdate.\n    if (this._canRecord) {\n      return this._performOperationAndHandleStatusAsync(() => ExponentAV.getAudioRecordingStatus());\n    }\n    const status = {\n      canRecord: false,\n      isRecording: false,\n      isDoneRecording: this._isDoneRecording,\n      durationMillis: this._finalDurationMillis,\n    };\n    this._callOnRecordingStatusUpdateForNewStatus(status);\n    return status;\n  };\n\n  /**\n   * Sets a function to be called regularly with the `RecordingStatus` of the `Recording`.\n   *\n   * `onRecordingStatusUpdate` will be called when another call to the API for this recording completes (such as `prepareToRecordAsync()`,\n   * `startAsync()`, `getStatusAsync()`, or `stopAndUnloadAsync()`), and will also be called at regular intervals while the recording can record.\n   * Call `setProgressUpdateInterval()` to modify the interval with which `onRecordingStatusUpdate` is called while the recording can record.\n   *\n   * @param onRecordingStatusUpdate A function taking a single parameter `RecordingStatus`.\n   */\n  setOnRecordingStatusUpdate(onRecordingStatusUpdate: ((status: RecordingStatus) => void) | null) {\n    this._onRecordingStatusUpdate = onRecordingStatusUpdate;\n    if (onRecordingStatusUpdate == null) {\n      this._disablePolling();\n    } else {\n      this._enablePollingIfNecessaryAndPossible();\n    }\n    this.getStatusAsync();\n  }\n\n  /**\n   * Sets the interval with which `onRecordingStatusUpdate` is called while the recording can record.\n   * See `setOnRecordingStatusUpdate` for details. This value defaults to 500 milliseconds.\n   * @param progressUpdateIntervalMillis The new interval between calls of `onRecordingStatusUpdate`.\n   */\n  setProgressUpdateInterval(progressUpdateIntervalMillis: number) {\n    this._progressUpdateIntervalMillis = progressUpdateIntervalMillis;\n    this.getStatusAsync();\n  }\n\n  // Record API\n\n  /**\n   * Loads the recorder into memory and prepares it for recording. This must be called before calling `startAsync()`.\n   * This method can only be called if the `Recording` instance has never yet been prepared.\n   *\n   * @param options `RecordingOptions` for the recording, including sample rate, bitrate, channels, format, encoder, and extension.\n   * If no options are passed to `prepareToRecordAsync()`, the recorder will be created with options `Audio.RecordingOptionsPresets.LOW_QUALITY`.\n   *\n   * @return A `Promise` that is fulfilled when the recorder is loaded and prepared, or rejects if this failed. If another `Recording` exists\n   * in your experience that is currently prepared to record, the `Promise` will reject. If the `RecordingOptions` provided are invalid,\n   * the `Promise` will also reject. The promise is resolved with the `RecordingStatus` of the recording.\n   */\n  async prepareToRecordAsync(\n    options: RecordingOptions = RecordingOptionsPresets.LOW_QUALITY\n  ): Promise<RecordingStatus> {\n    throwIfAudioIsDisabled();\n\n    if (_recorderExists) {\n      throw new Error('Only one Recording object can be prepared at a given time.');\n    }\n\n    if (this._isDoneRecording) {\n      throw new Error('This Recording object is done recording; you must make a new one.');\n    }\n\n    if (!options || !options.android || !options.ios) {\n      throw new Error(\n        'You must provide recording options for android and ios in order to prepare to record.'\n      );\n    }\n\n    const extensionRegex = /^\\.\\w+$/;\n    if (\n      !options.android.extension ||\n      !options.ios.extension ||\n      !extensionRegex.test(options.android.extension) ||\n      !extensionRegex.test(options.ios.extension)\n    ) {\n      throw new Error(`Your file extensions must match ${extensionRegex.toString()}.`);\n    }\n\n    if (!this._canRecord) {\n      if (eventEmitter) {\n        this._subscription = eventEmitter.addListener(\n          'Expo.Recording.recorderUnloaded',\n          this._cleanupForUnloadedRecorder\n        );\n      }\n\n      const {\n        uri,\n        status,\n      }: {\n        uri: string | null;\n        // status is of type RecordingStatus, but without the canRecord field populated\n        status: Pick<RecordingStatus, Exclude<keyof RecordingStatus, 'canRecord'>>;\n      } = await ExponentAV.prepareAudioRecorder(options);\n      _recorderExists = true;\n      this._uri = uri;\n      this._options = options;\n      this._canRecord = true;\n\n      const currentStatus = { ...status, canRecord: true };\n      this._callOnRecordingStatusUpdateForNewStatus(currentStatus);\n      this._enablePollingIfNecessaryAndPossible();\n      return currentStatus;\n    } else {\n      throw new Error('This Recording object is already prepared to record.');\n    }\n  }\n\n  /**\n   * Returns a list of available recording inputs. This method can only be called if the `Recording` has been prepared.\n   * @return A `Promise` that is fulfilled with an array of `RecordingInput` objects.\n   */\n  async getAvailableInputs(): Promise<RecordingInput[]> {\n    return ExponentAV.getAvailableInputs();\n  }\n\n  /**\n   * Returns the currently-selected recording input. This method can only be called if the `Recording` has been prepared.\n   * @return A `Promise` that is fulfilled with a `RecordingInput` object.\n   */\n  async getCurrentInput(): Promise<RecordingInput> {\n    return ExponentAV.getCurrentInput();\n  }\n\n  /**\n   * Sets the current recording input.\n   * @param inputUid The uid of a `RecordingInput`.\n   * @return A `Promise` that is resolved if successful or rejected if not.\n   */\n  async setInput(inputUid: string): Promise<void> {\n    return ExponentAV.setInput(inputUid);\n  }\n\n  /**\n   * Begins recording. This method can only be called if the `Recording` has been prepared.\n   * @return A `Promise` that is fulfilled when recording has begun, or rejects if recording could not be started.\n   * The promise is resolved with the `RecordingStatus` of the recording.\n   */\n  async startAsync(): Promise<RecordingStatus> {\n    return this._performOperationAndHandleStatusAsync(() => ExponentAV.startAudioRecording());\n  }\n\n  /**\n   * Pauses recording. This method can only be called if the `Recording` has been prepared.\n   *\n   * > This is only available on Android API version 24 and later.\n   *\n   * @return A `Promise` that is fulfilled when recording has paused, or rejects if recording could not be paused.\n   * If the Android API version is less than 24, the `Promise` will reject. The promise is resolved with the\n   * `RecordingStatus` of the recording.\n   */\n  async pauseAsync(): Promise<RecordingStatus> {\n    return this._performOperationAndHandleStatusAsync(() => ExponentAV.pauseAudioRecording());\n  }\n\n  /**\n   * Stops the recording and deallocates the recorder from memory. This reverts the `Recording` instance\n   * to an unprepared state, and another `Recording` instance must be created in order to record again.\n   * This method can only be called if the `Recording` has been prepared.\n   *\n   * > On Android this method may fail with `E_AUDIO_NODATA` when called too soon after `startAsync` and\n   * > no audio data has been recorded yet. In that case the recorded file will be invalid and should be discarded.\n   *\n   * @return A `Promise` that is fulfilled when recording has stopped, or rejects if recording could not be stopped.\n   * The promise is resolved with the `RecordingStatus` of the recording.\n   */\n  async stopAndUnloadAsync(): Promise<RecordingStatus> {\n    if (!this._canRecord) {\n      if (this._isDoneRecording) {\n        throw new Error('Cannot unload a Recording that has already been unloaded.');\n      } else {\n        throw new Error('Cannot unload a Recording that has not been prepared.');\n      }\n    }\n    // We perform a separate native API call so that the state of the Recording can be updated with\n    // the final duration of the recording. (We cast stopStatus as Object to appease Flow)\n    let stopResult: RecordingStatus | undefined;\n    let stopError: Error | undefined;\n    try {\n      stopResult = await ExponentAV.stopAudioRecording();\n    } catch (err: any) {\n      stopError = err;\n    }\n\n    // Web has to return the URI at the end of recording, so needs a little destructuring\n    if (Platform.OS === 'web' && stopResult?.uri !== undefined) {\n      this._uri = stopResult.uri;\n    }\n\n    // Clean-up and return status\n    await ExponentAV.unloadAudioRecorder();\n    const status = await this._cleanupForUnloadedRecorder(stopResult);\n    return stopError ? Promise.reject(stopError) : status;\n  }\n\n  // Read API\n\n  /**\n   * Gets the local URI of the `Recording`. Note that this will only succeed once the `Recording` is prepared\n   * to record. On web, this will not return the URI until the recording is finished.\n   * @return A `string` with the local URI of the `Recording`, or `null` if the `Recording` is not prepared\n   * to record (or, on Web, if the recording has not finished).\n   */\n  getURI(): string | null {\n    return this._uri;\n  }\n\n  /**\n   * @deprecated Use `createNewLoadedSoundAsync()` instead.\n   */\n  async createNewLoadedSound(\n    initialStatus: AVPlaybackStatusToSet = {},\n    onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null\n  ): Promise<SoundObject> {\n    console.warn(\n      `createNewLoadedSound is deprecated in favor of createNewLoadedSoundAsync, which has the same API aside from the method name`\n    );\n    return this.createNewLoadedSoundAsync(initialStatus, onPlaybackStatusUpdate);\n  }\n\n  /**\n   * Creates and loads a new `Sound` object to play back the `Recording`. Note that this will only succeed once the `Recording`\n   * is done recording and `stopAndUnloadAsync()` has been called.\n   *\n   * @param initialStatus The initial intended `PlaybackStatusToSet` of the sound, whose values will override the default initial playback status.\n   * This value defaults to `{}` if no parameter is passed. See the [AV documentation](/versions/latest/sdk/av) for details on `PlaybackStatusToSet`\n   * and the default initial playback status.\n   * @param onPlaybackStatusUpdate A function taking a single parameter `PlaybackStatus`. This value defaults to `null` if no parameter is passed.\n   * See the [AV documentation](/versions/latest/sdk/av) for details on the functionality provided by `onPlaybackStatusUpdate`\n   *\n   * @return A `Promise` that is rejected if creation failed, or fulfilled with the `SoundObject`.\n   */\n  async createNewLoadedSoundAsync(\n    initialStatus: AVPlaybackStatusToSet = {},\n    onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null\n  ): Promise<SoundObject> {\n    if (this._uri == null || !this._isDoneRecording) {\n      throw new Error('Cannot create sound when the Recording has not finished!');\n    }\n    return Sound.createAsync({ uri: this._uri }, initialStatus, onPlaybackStatusUpdate, false);\n  }\n}\n\nexport { PermissionResponse, PermissionStatus, PermissionHookOptions };\n\nexport * from './RecordingConstants';\n\nexport * from './Recording.types';\n"]}