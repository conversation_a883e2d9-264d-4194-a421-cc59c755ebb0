{"version": 3, "file": "AudioAvailability.js", "sourceRoot": "", "sources": ["../../src/Audio/AudioAvailability.ts"], "names": [], "mappings": "AAAA,OAAO,UAAU,MAAM,eAAe,CAAC;AAEvC,IAAI,QAAQ,GAAG,IAAI,CAAC;AAEpB,MAAM,UAAU,cAAc;IAC5B,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,sBAAsB;IACpC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;AACH,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,KAAc;IACpD,QAAQ,GAAG,KAAK,CAAC;IACjB,MAAM,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAC1C,6FAA6F;IAC7F,yFAAyF;IACzF,gGAAgG;IAChG,oDAAoD;AACtD,CAAC", "sourcesContent": ["import ExponentAV from '../ExponentAV';\n\nlet _enabled = true;\n\nexport function isAudioEnabled(): boolean {\n  return _enabled;\n}\n\nexport function throwIfAudioIsDisabled(): void {\n  if (!_enabled) {\n    throw new Error('Cannot complete operation because audio is not enabled.');\n  }\n}\n\n// @needsAudit\n/**\n * Audio is enabled by default, but if you want to write your own Audio API in a bare workflow app, you might want to disable the Audio API.\n * @param value `true` enables Audio, and `false` disables it.\n * @return A `Promise` that will reject if audio playback could not be enabled for the device.\n */\nexport async function setIsEnabledAsync(value: boolean): Promise<void> {\n  _enabled = value;\n  await ExponentAV.setAudioIsEnabled(value);\n  // TODO : We immediately pause all players when disabled, but we do not resume all shouldPlay\n  // players when enabled. Perhaps for completeness we should allow this; the design of the\n  // enabling API is for people to enable / disable this audio library, but I think that it should\n  // intuitively also double as a global pause/resume.\n}\n"]}