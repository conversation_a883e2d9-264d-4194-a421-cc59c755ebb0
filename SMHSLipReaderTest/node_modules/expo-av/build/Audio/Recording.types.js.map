{"version": 3, "file": "Recording.types.js", "sourceRoot": "", "sources": ["../../src/Audio/Recording.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Recording } from './Recording';\nimport {\n  AndroidAudioEncoder,\n  AndroidOutputFormat,\n  IOSAudioQuality,\n  IOSOutputFormat,\n} from './RecordingConstants';\n\n// TODO: For consistency with PlaybackStatus, should we include progressUpdateIntervalMillis here as well?\n\n// @needsAudit\nexport type RecordingStatus = {\n  /**\n   * A boolean describing if the `Recording` can initiate the recording.\n   * @platform android\n   * @platform ios\n   */\n  canRecord: boolean;\n  /**\n   * A boolean describing if the `Recording` is currently recording.\n   * @platform android\n   * @platform ios\n   */\n  isRecording: boolean;\n  /**\n   * A boolean describing if the `Recording` has been stopped.\n   * @platform android\n   * @platform ios\n   */\n  isDoneRecording: boolean;\n  /**\n   * The current duration of the recorded audio or the final duration is the recording has been stopped.\n   * @platform android\n   * @platform ios\n   */\n  durationMillis: number;\n  /**\n   * A number that's the most recent reading of the loudness in dB. The value ranges from `–160` dBFS, indicating minimum power,\n   * to `0` dBFS, indicating maximum power. Present or not based on Recording options. See `RecordingOptions` for more information.\n   * @platform android\n   * @platform ios\n   */\n  metering?: number;\n  // @docsMissing\n  uri?: string | null;\n  /**\n   * A boolean indicating whether media services were reset during recording. This may occur if the active input ceases to be available\n   * during recording.\n   *\n   * For example: airpods are the active input and they run out of batteries during recording.\n   *\n   * @platform ios\n   */\n  mediaServicesDidReset?: boolean;\n};\n\n/**\n * @platform android\n */\nexport type RecordingOptionsAndroid = {\n  /**\n   * The desired file extension. Example valid values are `.3gp` and `.m4a`.\n   * For more information, see the [Android docs](https://developer.android.com/guide/topics/media/media-formats)\n   * for supported output formats.\n   */\n  extension: string;\n  /**\n   * The desired file format. See the [`AndroidOutputFormat`](#androidoutputformat) enum for all valid values.\n   */\n  outputFormat: AndroidOutputFormat | number;\n  /**\n   * The desired audio encoder. See the [`AndroidAudioEncoder`](#androidaudioencoder) enum for all valid values.\n   */\n  audioEncoder: AndroidAudioEncoder | number;\n  /**\n   * The desired sample rate.\n   *\n   * Note that the sampling rate depends on the format for the audio recording, as well as the capabilities of the platform.\n   * For instance, the sampling rate supported by AAC audio coding standard ranges from 8 to 96 kHz,\n   * the sampling rate supported by AMRNB is 8kHz, and the sampling rate supported by AMRWB is 16kHz.\n   * Please consult with the related audio coding standard for the supported audio sampling rate.\n   *\n   * @example\n   * 44100\n   */\n  sampleRate?: number;\n  /**\n   * The desired number of channels.\n   *\n   * Note that `prepareToRecordAsync()` may perform additional checks on the parameter to make sure whether the specified\n   * number of audio channels are applicable.\n   *\n   * @example\n   * `1`, `2`\n   */\n  numberOfChannels?: number;\n  /**\n   * The desired bit rate.\n   *\n   * Note that `prepareToRecordAsync()` may perform additional checks on the parameter to make sure whether the specified\n   * bit rate is applicable, and sometimes the passed bitRate will be clipped internally to ensure the audio recording\n   * can proceed smoothly based on the capabilities of the platform.\n   *\n   * @example\n   * `128000`\n   */\n  bitRate?: number;\n  /**\n   * The desired maximum file size in bytes, after which the recording will stop (but `stopAndUnloadAsync()` must still\n   * be called after this point).\n   *\n   * @example\n   * `65536`\n   */\n  maxFileSize?: number;\n};\n\n/**\n * @platform ios\n */\nexport type RecordingOptionsIOS = {\n  /**\n   * The desired file extension.\n   *\n   * @example\n   * `'.caf'`\n   */\n  extension: string;\n  /**\n   * The desired file format. See the [`IOSOutputFormat`](#iosoutputformat) enum for all valid values.\n   */\n  outputFormat?: string | IOSOutputFormat | number;\n  /**\n   * The desired audio quality. See the [`IOSAudioQuality`](#iosaudioquality) enum for all valid values.\n   */\n  audioQuality: IOSAudioQuality | number;\n  /**\n   * The desired sample rate.\n   *\n   * @example\n   * `44100`\n   */\n  sampleRate: number;\n  /**\n   * The desired number of channels.\n   *\n   * @example\n   * `1`, `2`\n   */\n  numberOfChannels: number;\n  /**\n   * The desired bit rate.\n   *\n   * @example\n   * `128000`\n   */\n  bitRate: number;\n  /**\n   * The desired bit rate strategy. See the next section for an enumeration of all valid values of `bitRateStrategy`.\n   */\n  bitRateStrategy?: number;\n  /**\n   * The desired bit depth hint.\n   *\n   * @example\n   * `16`\n   */\n  bitDepthHint?: number;\n  /**\n   * The desired PCM bit depth.\n   *\n   * @example\n   * `16`\n   */\n  linearPCMBitDepth?: number;\n  /**\n   * A boolean describing if the PCM data should be formatted in big endian.\n   */\n  linearPCMIsBigEndian?: boolean;\n  /**\n   * A boolean describing if the PCM data should be encoded in floating point or integral values.\n   */\n  linearPCMIsFloat?: boolean;\n};\n\n// @docsMissing\nexport type RecordingOptionsWeb = {\n  mimeType?: string;\n  bitsPerSecond?: number;\n};\n\n// @needsAudit\n/**\n * The recording extension, sample rate, bitrate, channels, format, encoder, etc. which can be customized by passing options to `prepareToRecordAsync()`.\n *\n * We provide the following preset options for convenience, as used in the example above. See below for the definitions of these presets.\n * - `Audio.RecordingOptionsPresets.HIGH_QUALITY`\n * - `Audio.RecordingOptionsPresets.LOW_QUALITY`\n *\n * We also provide the ability to define your own custom recording options, but **we recommend you use the presets,\n * as not all combinations of options will allow you to successfully `prepareToRecordAsync()`.**\n * You will have to test your custom options on iOS and Android to make sure it's working. In the future,\n * we will enumerate all possible valid combinations, but at this time, our goal is to make the basic use-case easy (with presets)\n * and the advanced use-case possible (by exposing all the functionality available on all supported platforms).\n */\nexport type RecordingOptions = {\n  /**\n   * A boolean that determines whether audio level information will be part of the status object under the \"metering\" key.\n   */\n  isMeteringEnabled?: boolean;\n  /**\n   * A boolean that hints to keep the audio active after `prepareToRecordAsync` completes.\n   * Setting this value can improve the speed at which the recording starts. Only set this value to `true` when you call `startAsync`\n   * immediately after `prepareToRecordAsync`. This value is automatically set when using `Audio.recording.createAsync()`.\n   */\n  keepAudioActiveHint?: boolean;\n  /**\n   * Recording options for the Android platform.\n   */\n  android: RecordingOptionsAndroid;\n  /**\n   * Recording options for the iOS platform.\n   */\n  ios: RecordingOptionsIOS;\n  /**\n   * Recording options for the Web platform.\n   */\n  web: RecordingOptionsWeb;\n};\n\n/**\n * @platform android\n * @platform ios\n */\nexport type RecordingInput = {\n  name: string;\n  type: string;\n  uid: string;\n};\n\n/**\n * @platform android\n * @platform ios\n */\nexport type RecordingObject = {\n  /**\n   * The newly created and started `Recording` object.\n   */\n  recording: Recording;\n  /**\n   * The `RecordingStatus` of the `Recording` object. See the [AV documentation](/versions/latest/sdk/av) for further information.\n   */\n  status: RecordingStatus;\n};\n"]}