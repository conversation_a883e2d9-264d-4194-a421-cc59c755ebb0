{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.av", "version": "16.0.7", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.14.3"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.google.android.exoplayer", "module": "exoplayer", "version": {"requires": "2.18.1"}}, {"group": "com.google.android.exoplayer", "module": "extension-okhttp", "version": {"requires": "2.18.1"}}, {"group": "com.squareup.okhttp3", "module": "okhttp", "version": {"requires": "3.14.9"}}, {"group": "com.squareup.okhttp3", "module": "okhttp-urlconnection", "version": {"requires": "3.14.9"}}], "files": [{"name": "expo.modules.av-16.0.7.aar", "url": "expo.modules.av-16.0.7.aar", "size": 326994, "sha512": "05df90e3b3c6dfa4c23d53322fef67de0022be80badf70df08b340d125033e582054df643d34b458453bb6d1102924de7e29d650128a1cbefcb23f73049fe570", "sha256": "5f972012ad33619fc02b134aa707c316f21080a8c9c366a5f76a50a2c282a60c", "sha1": "f8690fefd2b990fc49b815040de4befa4783a327", "md5": "68f4ac88b2a7097f0f6f593d74f3b15b"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.1.20"}}, {"group": "com.facebook.react", "module": "react-android"}, {"group": "com.google.android.exoplayer", "module": "exoplayer", "version": {"requires": "2.18.1"}}, {"group": "com.google.android.exoplayer", "module": "extension-okhttp", "version": {"requires": "2.18.1"}}, {"group": "com.squareup.okhttp3", "module": "okhttp", "version": {"requires": "3.14.9"}}, {"group": "com.squareup.okhttp3", "module": "okhttp-urlconnection", "version": {"requires": "3.14.9"}}], "files": [{"name": "expo.modules.av-16.0.7.aar", "url": "expo.modules.av-16.0.7.aar", "size": 326994, "sha512": "05df90e3b3c6dfa4c23d53322fef67de0022be80badf70df08b340d125033e582054df643d34b458453bb6d1102924de7e29d650128a1cbefcb23f73049fe570", "sha256": "5f972012ad33619fc02b134aa707c316f21080a8c9c366a5f76a50a2c282a60c", "sha1": "f8690fefd2b990fc49b815040de4befa4783a327", "md5": "68f4ac88b2a7097f0f6f593d74f3b15b"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.av-16.0.7-sources.jar", "url": "expo.modules.av-16.0.7-sources.jar", "size": 48640, "sha512": "5a9efd16a2765beb449746e0db391431d85acb119fad8713ace9076c79159a36e989ad8de0642c907c906c01fa4ee204b5cd03b1f4bbec5e8e080960c0988f8b", "sha256": "91e24c69bd7e044eaf3ebee51c58f543b5f7f3340b01c387b73f31aab1841616", "sha1": "589c44d807c4ca3df01bfe1f81be82e026b572c8", "md5": "d7881eeb90fedb38f4a03d71135ba173"}]}]}