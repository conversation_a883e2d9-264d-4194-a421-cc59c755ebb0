<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>host.exp.exponent</groupId>
  <artifactId>expo.modules.av</artifactId>
  <version>16.0.7</version>
  <packaging>aar</packaging>
  <name>expo.modules.av</name>
  <url>https://github.com/expo/expo</url>
  <licenses>
    <license>
      <name>MIT License</name>
      <url>https://github.com/expo/expo/blob/main/LICENSE</url>
    </license>
  </licenses>
  <scm>
    <connection>https://github.com/expo/expo.git</connection>
    <developerConnection>https://github.com/expo/expo.git</developerConnection>
    <url>https://github.com/expo/expo</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.google.android.exoplayer</groupId>
      <artifactId>exoplayer</artifactId>
      <version>2.18.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.android.exoplayer</groupId>
      <artifactId>extension-okhttp</artifactId>
      <version>2.18.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>3.14.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp-urlconnection</artifactId>
      <version>3.14.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk7</artifactId>
      <version>2.1.20</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.facebook.react</groupId>
      <artifactId>react-android</artifactId>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
