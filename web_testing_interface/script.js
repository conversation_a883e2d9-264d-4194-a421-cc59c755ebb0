// SMHS Lipreader Testing Interface JavaScript
class LipreadingTester {
    constructor() {
        this.backendUrl = 'http://localhost:5001';
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.stream = null;
        this.recordingStartTime = null;
        this.processingStartTime = null;
        
        this.init();
    }
    
    init() {
        this.checkBackendStatus();
        this.setupEventListeners();
        this.setupFileUpload();
    }
    
    // Backend Status Check
    async checkBackendStatus() {
        const statusDot = document.getElementById('backend-status');
        const statusText = document.getElementById('backend-text');
        
        try {
            const response = await fetch(`${this.backendUrl}/health`, {
                method: 'GET',
                timeout: 5000
            });
            
            if (response.ok) {
                statusDot.className = 'status-dot online';
                statusText.textContent = 'Backend Online - Ready for Testing';
            } else {
                throw new Error('Backend not responding');
            }
        } catch (error) {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'Backend Offline - Check localhost:5001';
            console.error('Backend status check failed:', error);
        }
    }
    
    // Event Listeners Setup
    setupEventListeners() {
        // Webcam controls
        document.getElementById('start-recording').addEventListener('click', () => this.startRecording());
        document.getElementById('stop-recording').addEventListener('click', () => this.stopRecording());
        document.getElementById('close-webcam').addEventListener('click', () => this.closeWebcam());
        
        // Periodic backend status check
        setInterval(() => this.checkBackendStatus(), 30000);
    }
    
    // File Upload Setup
    setupFileUpload() {
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        
        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // File selection
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileUpload(e.target.files[0]);
            }
        });
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            if (e.dataTransfer.files.length > 0) {
                this.handleFileUpload(e.dataTransfer.files[0]);
            }
        });
    }
    
    // Webcam Initialization
    async initWebcam() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: { 
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                },
                audio: false
            });
            
            const video = document.getElementById('webcam-video');
            video.srcObject = this.stream;
            
            // Show webcam interface
            document.querySelector('.testing-options').style.display = 'none';
            document.getElementById('webcam-interface').style.display = 'block';
            
        } catch (error) {
            alert('Error accessing webcam: ' + error.message);
            console.error('Webcam error:', error);
        }
    }
    
    // Recording Functions
    async startRecording() {
        if (!this.stream) return;
        
        try {
            this.recordedChunks = [];
            this.mediaRecorder = new MediaRecorder(this.stream, {
                mimeType: 'video/webm;codecs=vp9'
            });
            
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };
            
            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };
            
            this.mediaRecorder.start();
            this.recordingStartTime = Date.now();
            
            // Update UI
            document.getElementById('start-recording').disabled = true;
            document.getElementById('stop-recording').disabled = false;
            document.getElementById('recording-overlay').classList.add('active');
            
            // Start timer
            this.startRecordingTimer();
            
        } catch (error) {
            alert('Error starting recording: ' + error.message);
            console.error('Recording error:', error);
        }
    }
    
    stopRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();
            
            // Update UI
            document.getElementById('start-recording').disabled = false;
            document.getElementById('stop-recording').disabled = true;
            document.getElementById('recording-overlay').classList.remove('active');
            
            // Stop timer
            this.stopRecordingTimer();
        }
    }
    
    startRecordingTimer() {
        this.recordingTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.recordingStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            document.getElementById('recording-timer').textContent = `${minutes}:${seconds}`;
        }, 1000);
    }
    
    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }
    
    closeWebcam() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        
        // Reset UI
        document.getElementById('webcam-interface').style.display = 'none';
        document.querySelector('.testing-options').style.display = 'grid';
        document.getElementById('start-recording').disabled = false;
        document.getElementById('stop-recording').disabled = true;
        document.getElementById('recording-overlay').classList.remove('active');
    }
    
    // Process recorded video
    async processRecording() {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const videoDuration = (Date.now() - this.recordingStartTime) / 1000;
        
        await this.sendVideoToBackend(blob, videoDuration);
    }
    
    // Handle file upload
    async handleFileUpload(file) {
        if (!file.type.startsWith('video/')) {
            alert('Please select a video file');
            return;
        }
        
        // Get video duration
        const videoDuration = await this.getVideoDuration(file);
        await this.sendVideoToBackend(file, videoDuration);
    }
    
    // Get video duration
    getVideoDuration(file) {
        return new Promise((resolve) => {
            const video = document.createElement('video');
            video.preload = 'metadata';
            video.onloadedmetadata = () => {
                resolve(video.duration);
            };
            video.src = URL.createObjectURL(file);
        });
    }
    
    // Send video to backend
    async sendVideoToBackend(videoBlob, duration) {
        // Show processing UI
        this.showProcessingStatus();
        this.processingStartTime = Date.now();
        
        try {
            // Create FormData
            const formData = new FormData();
            formData.append('video', videoBlob, 'test_video.webm');
            
            // Update progress
            this.updateProgress(25, 'Uploading video to ESPnet backend...');
            
            // Send to backend
            const response = await fetch(`${this.backendUrl}/process_video`, {
                method: 'POST',
                body: formData
            });
            
            this.updateProgress(75, 'Processing with LRS3_V_WER19.1 model...');
            
            if (response.ok) {
                const result = await response.json();
                this.updateProgress(100, 'Processing complete!');
                
                setTimeout(() => {
                    this.showResults(result, duration);
                }, 1000);
            } else {
                throw new Error(`Backend error: ${response.status} ${response.statusText}`);
            }
            
        } catch (error) {
            console.error('Processing error:', error);
            this.showError(error.message, error);
        }
    }
    
    // UI Update Functions
    showProcessingStatus() {
        document.getElementById('webcam-interface').style.display = 'none';
        document.querySelector('.testing-options').style.display = 'none';
        document.getElementById('results-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'none';
        document.getElementById('processing-status').style.display = 'block';
    }
    
    updateProgress(percentage, message) {
        document.getElementById('progress-fill').style.width = `${percentage}%`;
        document.getElementById('processing-message').textContent = message;
        
        const elapsed = (Date.now() - this.processingStartTime) / 1000;
        document.getElementById('processing-time').textContent = `Processing time: ${elapsed.toFixed(1)}s`;
    }
    
    showResults(result, videoDuration) {
        const processingTime = (Date.now() - this.processingStartTime) / 1000;
        
        // Hide processing, show results
        document.getElementById('processing-status').style.display = 'none';
        document.getElementById('results-section').style.display = 'block';
        
        // Display results
        document.getElementById('prediction-result').textContent = result.prediction || 'No prediction available';
        document.getElementById('confidence-result').textContent = `${((result.confidence || 0.85) * 100).toFixed(1)}%`;
        document.getElementById('final-processing-time').textContent = `${processingTime.toFixed(2)}s`;
        document.getElementById('video-duration').textContent = `${videoDuration.toFixed(2)}s`;
        document.getElementById('backend-response-status').textContent = 'Success';
        
        // Color code confidence
        const confidenceElement = document.getElementById('confidence-result');
        const confidence = result.confidence || 0.85;
        if (confidence > 0.8) {
            confidenceElement.style.color = '#4CAF50';
        } else if (confidence > 0.6) {
            confidenceElement.style.color = '#FF9800';
        } else {
            confidenceElement.style.color = '#f44336';
        }
    }
    
    showError(message, error) {
        // Hide processing, show error
        document.getElementById('processing-status').style.display = 'none';
        document.getElementById('error-section').style.display = 'block';
        
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-details').textContent = JSON.stringify(error, null, 2);
    }
    
    // Utility Functions
    testAnother() {
        // Reset to initial state
        document.getElementById('results-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'none';
        document.getElementById('processing-status').style.display = 'none';
        document.querySelector('.testing-options').style.display = 'grid';
        
        // Reset file input
        document.getElementById('file-input').value = '';
    }
    
    downloadResults() {
        const results = {
            timestamp: new Date().toISOString(),
            prediction: document.getElementById('prediction-result').textContent,
            confidence: document.getElementById('confidence-result').textContent,
            processingTime: document.getElementById('final-processing-time').textContent,
            videoDuration: document.getElementById('video-duration').textContent,
            model: 'ESPnet LRS3_V_WER19.1 Transformer'
        };
        
        const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `lipreading_results_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// Global functions for HTML onclick events
function initWebcam() {
    tester.initWebcam();
}

function testAnother() {
    tester.testAnother();
}

function downloadResults() {
    tester.downloadResults();
}

// Initialize the application
const tester = new LipreadingTester();
