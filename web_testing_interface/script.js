// SMHS Lipreader Testing Interface JavaScript
class LipreadingTester {
    constructor() {
        this.backendUrl = 'http://localhost:5001';
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.stream = null;
        this.recordingStartTime = null;
        this.processingStartTime = null;
        
        this.init();
    }
    
    init() {
        this.checkBackendStatus();
        this.setupEventListeners();
        this.setupFileUpload();
    }
    
    // Backend Status Check
    async checkBackendStatus() {
        const statusDot = document.getElementById('backend-status');
        const statusText = document.getElementById('backend-text');

        try {
            console.log('Checking backend status at:', `${this.backendUrl}/health`);

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            const response = await fetch(`${this.backendUrl}/health`, {
                method: 'GET',
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const data = await response.json();
                console.log('Backend response:', data);
                statusDot.className = 'status-dot online';
                statusText.textContent = 'Backend Online - Ready for Testing';
            } else {
                throw new Error(`Backend responded with status: ${response.status}`);
            }
        } catch (error) {
            console.error('Backend status check failed:', error);
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'Backend Offline - Check localhost:5001';

            // Show more specific error messages
            if (error.name === 'AbortError') {
                statusText.textContent = 'Backend Timeout - Check localhost:5001';
            } else if (error.message.includes('Failed to fetch')) {
                statusText.textContent = 'Backend Connection Failed - Check localhost:5001';
            }
        }
    }
    
    // Event Listeners Setup
    setupEventListeners() {
        // Webcam controls
        document.getElementById('start-recording').addEventListener('click', () => this.startRecording());
        document.getElementById('stop-recording').addEventListener('click', () => this.stopRecording());
        document.getElementById('close-webcam').addEventListener('click', () => this.closeWebcam());
        
        // Periodic backend status check
        setInterval(() => this.checkBackendStatus(), 30000);
    }
    
    // File Upload Setup
    setupFileUpload() {
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        
        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // File selection
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileUpload(e.target.files[0]);
            }
        });
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            if (e.dataTransfer.files.length > 0) {
                this.handleFileUpload(e.dataTransfer.files[0]);
            }
        });
    }
    
    // Webcam Initialization
    async initWebcam() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                },
                audio: false
            });

            const video = document.getElementById('webcam-video');
            video.srcObject = this.stream;

            // Initialize lip positioning guide
            video.addEventListener('loadedmetadata', () => {
                this.initializeLipGuide(video);
            });

            // Show webcam interface
            document.querySelector('.testing-options').style.display = 'none';
            document.getElementById('webcam-interface').style.display = 'block';

        } catch (error) {
            alert('Error accessing webcam: ' + error.message);
            console.error('Webcam error:', error);
        }
    }

    // Initialize Lip Positioning Guide
    initializeLipGuide(video) {
        const guideFrame = document.getElementById('lip-guide-frame');
        const guideInstructions = document.getElementById('guide-instructions');
        const alignmentStatus = document.getElementById('alignment-status');
        const alignmentText = document.getElementById('alignment-text');

        // Calculate optimal lip region (centered, lower third of video)
        const updateGuidePosition = () => {
            const videoRect = video.getBoundingClientRect();
            const containerRect = video.parentElement.getBoundingClientRect();

            // Calculate relative position within the video container
            const videoWidth = video.videoWidth || 640;
            const videoHeight = video.videoHeight || 480;
            const displayWidth = videoRect.width;
            const displayHeight = videoRect.height;

            // Optimal lip region: center horizontally, lower third vertically
            const lipRegionWidth = displayWidth * 0.4;  // 40% of video width
            const lipRegionHeight = displayHeight * 0.25; // 25% of video height
            const lipRegionLeft = (displayWidth - lipRegionWidth) / 2;
            const lipRegionTop = displayHeight * 0.45; // Start at 45% down (lower third)

            // Position the guide frame
            guideFrame.style.width = `${lipRegionWidth}px`;
            guideFrame.style.height = `${lipRegionHeight}px`;
            guideFrame.style.left = `${lipRegionLeft}px`;
            guideFrame.style.top = `${lipRegionTop}px`;

            console.log('Lip guide positioned:', {
                width: lipRegionWidth,
                height: lipRegionHeight,
                left: lipRegionLeft,
                top: lipRegionTop,
                videoSize: { width: videoWidth, height: videoHeight },
                displaySize: { width: displayWidth, height: displayHeight }
            });
        };

        // Update guide position when video dimensions change
        updateGuidePosition();
        window.addEventListener('resize', updateGuidePosition);

        // Simulate face detection feedback (in real implementation, this would use face detection)
        this.startAlignmentFeedback();
    }

    // Start Alignment Feedback Simulation
    startAlignmentFeedback() {
        const guideFrame = document.getElementById('lip-guide-frame');
        const guideInstructions = document.getElementById('guide-instructions');
        const alignmentStatus = document.getElementById('alignment-status');
        const alignmentText = document.getElementById('alignment-text');

        // Simulate alignment detection
        let alignmentQuality = 'positioning';
        let feedbackInterval = setInterval(() => {
            // Simulate varying alignment quality
            const random = Math.random();

            if (random > 0.7) {
                alignmentQuality = 'good';
                guideFrame.classList.add('aligned');
                guideFrame.classList.remove('recording');
                guideInstructions.textContent = 'Perfect! Ready to record';
                guideInstructions.classList.add('aligned');
                guideInstructions.classList.remove('recording');
                alignmentStatus.classList.add('good');
                alignmentStatus.classList.remove('poor');
                alignmentText.textContent = 'Good Alignment';
            } else if (random > 0.3) {
                alignmentQuality = 'fair';
                guideFrame.classList.remove('aligned', 'recording');
                guideInstructions.textContent = 'Position your lips within the green frame';
                guideInstructions.classList.remove('aligned', 'recording');
                alignmentStatus.classList.remove('good', 'poor');
                alignmentText.textContent = 'Adjusting...';
            } else {
                alignmentQuality = 'poor';
                guideFrame.classList.remove('aligned', 'recording');
                guideInstructions.textContent = 'Move closer and center your face';
                guideInstructions.classList.remove('aligned', 'recording');
                alignmentStatus.classList.add('poor');
                alignmentStatus.classList.remove('good');
                alignmentText.textContent = 'Poor Alignment';
            }
        }, 2000);

        // Store interval for cleanup
        this.alignmentInterval = feedbackInterval;
    }
    
    // Recording Functions
    async startRecording() {
        if (!this.stream) return;

        try {
            this.recordedChunks = [];

            // Try different codec options for better compatibility
            let mimeType = 'video/webm;codecs=vp9';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
                mimeType = 'video/webm;codecs=vp8';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'video/webm';
                    if (!MediaRecorder.isTypeSupported(mimeType)) {
                        mimeType = 'video/mp4';
                    }
                }
            }

            console.log('Using MIME type for recording:', mimeType);

            this.mediaRecorder = new MediaRecorder(this.stream, {
                mimeType: mimeType,
                videoBitsPerSecond: 1000000 // 1 Mbps for good quality
            });

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                    console.log('Recorded chunk size:', event.data.size);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };

            this.mediaRecorder.start(1000); // Collect data every second
            this.recordingStartTime = Date.now();

            // Update UI and lip guide
            document.getElementById('start-recording').disabled = true;
            document.getElementById('stop-recording').disabled = false;
            document.getElementById('recording-overlay').classList.add('active');

            // Update lip guide for recording state
            this.updateLipGuideForRecording(true);

            // Start timer
            this.startRecordingTimer();

            console.log('Recording started with format:', mimeType);

        } catch (error) {
            alert('Error starting recording: ' + error.message);
            console.error('Recording error:', error);
        }
    }

    // Update Lip Guide for Recording State
    updateLipGuideForRecording(isRecording) {
        const guideFrame = document.getElementById('lip-guide-frame');
        const guideInstructions = document.getElementById('guide-instructions');
        const alignmentStatus = document.getElementById('alignment-status');

        if (isRecording) {
            guideFrame.classList.add('recording');
            guideFrame.classList.remove('aligned');
            guideInstructions.textContent = '🔴 Recording - Keep lips in frame and speak clearly';
            guideInstructions.classList.add('recording');
            guideInstructions.classList.remove('aligned');
            alignmentStatus.style.display = 'none'; // Hide during recording
        } else {
            guideFrame.classList.remove('recording');
            guideInstructions.classList.remove('recording');
            alignmentStatus.style.display = 'flex'; // Show after recording
        }
    }
    
    stopRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();

            // Update UI
            document.getElementById('start-recording').disabled = false;
            document.getElementById('stop-recording').disabled = true;
            document.getElementById('recording-overlay').classList.remove('active');

            // Update lip guide back to positioning mode
            this.updateLipGuideForRecording(false);

            // Stop timer
            this.stopRecordingTimer();

            console.log('Recording stopped, total chunks:', this.recordedChunks.length);
        }
    }
    
    startRecordingTimer() {
        this.recordingTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.recordingStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            document.getElementById('recording-timer').textContent = `${minutes}:${seconds}`;
        }, 1000);
    }
    
    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }
    
    closeWebcam() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        // Clean up alignment feedback
        if (this.alignmentInterval) {
            clearInterval(this.alignmentInterval);
            this.alignmentInterval = null;
        }

        // Reset UI
        document.getElementById('webcam-interface').style.display = 'none';
        document.querySelector('.testing-options').style.display = 'grid';
        document.getElementById('start-recording').disabled = false;
        document.getElementById('stop-recording').disabled = true;
        document.getElementById('recording-overlay').classList.remove('active');

        // Reset lip guide
        const guideFrame = document.getElementById('lip-guide-frame');
        const guideInstructions = document.getElementById('guide-instructions');
        const alignmentStatus = document.getElementById('alignment-status');

        if (guideFrame) guideFrame.classList.remove('recording', 'aligned');
        if (guideInstructions) guideInstructions.classList.remove('recording', 'aligned');
        if (alignmentStatus) alignmentStatus.classList.remove('good', 'poor');
    }
    
    // Process recorded video
    async processRecording() {
        if (this.recordedChunks.length === 0) {
            alert('No video data recorded. Please try again.');
            return;
        }

        // Determine the correct MIME type based on what was recorded
        let mimeType = 'video/webm';
        if (this.mediaRecorder && this.mediaRecorder.mimeType) {
            mimeType = this.mediaRecorder.mimeType;
        }

        const blob = new Blob(this.recordedChunks, { type: mimeType });
        const videoDuration = (Date.now() - this.recordingStartTime) / 1000;

        console.log('Processing recorded video:', {
            size: blob.size,
            type: blob.type,
            duration: videoDuration,
            chunks: this.recordedChunks.length
        });

        // Verify video format compatibility
        await this.verifyVideoFormat(blob);

        await this.sendVideoToBackend(blob, videoDuration);
    }

    // Verify Video Format Compatibility
    async verifyVideoFormat(blob) {
        return new Promise((resolve) => {
            const video = document.createElement('video');
            const url = URL.createObjectURL(blob);

            video.onloadedmetadata = () => {
                console.log('Video format verification:', {
                    duration: video.duration,
                    videoWidth: video.videoWidth,
                    videoHeight: video.videoHeight,
                    size: blob.size,
                    type: blob.type
                });

                // Check if video meets ESPnet requirements
                if (video.videoWidth < 224 || video.videoHeight < 224) {
                    console.warn('Video resolution may be too low for optimal lipreading');
                }

                if (video.duration < 1) {
                    console.warn('Video duration is very short, may affect accuracy');
                }

                URL.revokeObjectURL(url);
                resolve(true);
            };

            video.onerror = () => {
                console.error('Video format verification failed');
                URL.revokeObjectURL(url);
                resolve(false);
            };

            video.src = url;
        });
    }
    
    // Handle file upload
    async handleFileUpload(file) {
        if (!file.type.startsWith('video/')) {
            alert('Please select a video file');
            return;
        }
        
        // Get video duration
        const videoDuration = await this.getVideoDuration(file);
        await this.sendVideoToBackend(file, videoDuration);
    }
    
    // Get video duration
    getVideoDuration(file) {
        return new Promise((resolve) => {
            const video = document.createElement('video');
            video.preload = 'metadata';
            video.onloadedmetadata = () => {
                resolve(video.duration);
            };
            video.src = URL.createObjectURL(file);
        });
    }
    
    // Send video to backend
    async sendVideoToBackend(videoBlob, duration) {
        // Show processing UI
        this.showProcessingStatus();
        this.processingStartTime = Date.now();
        
        try {
            // Create FormData
            const formData = new FormData();
            formData.append('video', videoBlob, 'test_video.webm');
            
            // Update progress
            this.updateProgress(25, 'Uploading video to ESPnet backend...');
            
            // Send to backend
            console.log('Sending video to backend:', `${this.backendUrl}/process_video`);

            const response = await fetch(`${this.backendUrl}/process_video`, {
                method: 'POST',
                body: formData,
                headers: {
                    // Don't set Content-Type for FormData - browser will set it with boundary
                }
            });
            
            this.updateProgress(75, 'Processing with LRS3_V_WER19.1 model...');
            
            if (response.ok) {
                const result = await response.json();
                this.updateProgress(100, 'Processing complete!');
                
                setTimeout(() => {
                    this.showResults(result, duration);
                }, 1000);
            } else {
                throw new Error(`Backend error: ${response.status} ${response.statusText}`);
            }
            
        } catch (error) {
            console.error('Processing error:', error);
            this.showError(error.message, error);
        }
    }
    
    // UI Update Functions
    showProcessingStatus() {
        document.getElementById('webcam-interface').style.display = 'none';
        document.querySelector('.testing-options').style.display = 'none';
        document.getElementById('results-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'none';
        document.getElementById('processing-status').style.display = 'block';
    }
    
    updateProgress(percentage, message) {
        document.getElementById('progress-fill').style.width = `${percentage}%`;
        document.getElementById('processing-message').textContent = message;
        
        const elapsed = (Date.now() - this.processingStartTime) / 1000;
        document.getElementById('processing-time').textContent = `Processing time: ${elapsed.toFixed(1)}s`;
    }
    
    showResults(result, videoDuration) {
        const processingTime = (Date.now() - this.processingStartTime) / 1000;
        
        // Hide processing, show results
        document.getElementById('processing-status').style.display = 'none';
        document.getElementById('results-section').style.display = 'block';
        
        // Display results
        document.getElementById('prediction-result').textContent = result.prediction || 'No prediction available';
        document.getElementById('confidence-result').textContent = `${((result.confidence || 0.85) * 100).toFixed(1)}%`;
        document.getElementById('final-processing-time').textContent = `${processingTime.toFixed(2)}s`;
        document.getElementById('video-duration').textContent = `${videoDuration.toFixed(2)}s`;
        document.getElementById('backend-response-status').textContent = 'Success';
        
        // Color code confidence
        const confidenceElement = document.getElementById('confidence-result');
        const confidence = result.confidence || 0.85;
        if (confidence > 0.8) {
            confidenceElement.style.color = '#4CAF50';
        } else if (confidence > 0.6) {
            confidenceElement.style.color = '#FF9800';
        } else {
            confidenceElement.style.color = '#f44336';
        }
    }
    
    showError(message, error) {
        // Hide processing, show error
        document.getElementById('processing-status').style.display = 'none';
        document.getElementById('error-section').style.display = 'block';
        
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-details').textContent = JSON.stringify(error, null, 2);
    }
    
    // Utility Functions
    testAnother() {
        // Reset to initial state
        document.getElementById('results-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'none';
        document.getElementById('processing-status').style.display = 'none';
        document.querySelector('.testing-options').style.display = 'grid';
        
        // Reset file input
        document.getElementById('file-input').value = '';
    }
    
    downloadResults() {
        const results = {
            timestamp: new Date().toISOString(),
            prediction: document.getElementById('prediction-result').textContent,
            confidence: document.getElementById('confidence-result').textContent,
            processingTime: document.getElementById('final-processing-time').textContent,
            videoDuration: document.getElementById('video-duration').textContent,
            model: 'ESPnet LRS3_V_WER19.1 Transformer'
        };
        
        const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `lipreading_results_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// Global functions for HTML onclick events
function initWebcam() {
    tester.initWebcam();
}

function testAnother() {
    tester.testAnother();
}

function downloadResults() {
    tester.downloadResults();
}

// Initialize the application
const tester = new LipreadingTester();
