/* SMHS Lipreader Testing Interface Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    color: #5a2b81;
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 20px;
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.online {
    background: #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

.status-dot.offline {
    background: #f44336;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
}

/* Main Content */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Testing Options */
.testing-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.option-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.option-card:hover {
    border-color: #5a2b81;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(90, 43, 129, 0.1);
}

.option-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.option-card h3 {
    color: #5a2b81;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.option-card p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.5;
}

.option-btn {
    background: #5a2b81;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-btn:hover {
    background: #4a1f6b;
    transform: translateY(-2px);
}

/* Upload Area */
.upload-area {
    border: 2px dashed #5a2b81;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #4a1f6b;
    background: rgba(90, 43, 129, 0.05);
}

.upload-area.dragover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

#file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Webcam Interface */
.webcam-interface {
    text-align: center;
}

.video-container {
    position: relative;
    display: inline-block;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
}

#webcam-video {
    width: 100%;
    max-width: 640px;
    height: auto;
    display: block;
}

/* Lip Positioning Guide Overlay */
.lip-guide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.lip-guide-frame {
    position: absolute;
    border: 3px solid rgba(76, 175, 80, 0.8);
    border-radius: 8px;
    background: rgba(76, 175, 80, 0.1);
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
    transition: all 0.3s ease;
}

.lip-guide-frame.recording {
    border-color: rgba(255, 68, 68, 0.8);
    background: rgba(255, 68, 68, 0.1);
    box-shadow: 0 0 20px rgba(255, 68, 68, 0.3);
    animation: pulse-recording 2s infinite;
}

.lip-guide-frame.aligned {
    border-color: rgba(76, 175, 80, 1);
    background: rgba(76, 175, 80, 0.15);
    box-shadow: 0 0 25px rgba(76, 175, 80, 0.5);
}

@keyframes pulse-recording {
    0%, 100% {
        border-color: rgba(255, 68, 68, 0.8);
        box-shadow: 0 0 20px rgba(255, 68, 68, 0.3);
    }
    50% {
        border-color: rgba(255, 68, 68, 1);
        box-shadow: 0 0 30px rgba(255, 68, 68, 0.6);
    }
}

/* Crosshair Guidelines */
.crosshair-guides {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.crosshair-horizontal,
.crosshair-vertical {
    position: absolute;
    background: rgba(255, 255, 255, 0.6);
    transition: opacity 0.3s ease;
}

.crosshair-horizontal {
    width: 100%;
    height: 1px;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}

.crosshair-vertical {
    width: 1px;
    height: 100%;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
}

/* Guide Instructions */
.guide-instructions {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
    transition: all 0.3s ease;
    z-index: 15;
}

.guide-instructions.recording {
    background: rgba(255, 68, 68, 0.9);
    animation: pulse-text 1.5s infinite;
}

.guide-instructions.aligned {
    background: rgba(76, 175, 80, 0.9);
}

@keyframes pulse-text {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Alignment Status Indicator */
.alignment-status {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    z-index: 15;
}

.alignment-status.good {
    background: rgba(76, 175, 80, 0.9);
}

.alignment-status.poor {
    background: rgba(255, 152, 0, 0.9);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* Medical Phrases Reference */
.medical-phrases-reference {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    border: 2px solid #6c5ce7;
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
}

.medical-phrases-reference h4 {
    color: #6c5ce7;
    margin-bottom: 15px;
    font-size: 1.1rem;
    text-align: center;
}

.phrases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.phrase-item {
    background: white;
    border: 2px solid #ddd;
    border-radius: 10px;
    padding: 12px 16px;
    text-align: center;
    font-weight: 500;
    color: #333;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.phrase-item:hover {
    border-color: #6c5ce7;
    background: #f8f9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 92, 231, 0.2);
}

.phrase-item:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(108, 92, 231, 0.3);
}

.recording-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    display: none;
}

.recording-overlay.active {
    display: block;
}

.recording-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.recording-dot {
    width: 8px;
    height: 8px;
    background: #ff4444;
    border-radius: 50%;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

.webcam-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.control-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn.primary {
    background: #4CAF50;
    color: white;
}

.control-btn.primary:hover {
    background: #45a049;
}

.control-btn.secondary {
    background: #ff4444;
    color: white;
}

.control-btn.secondary:hover {
    background: #cc3333;
}

.control-btn.tertiary {
    background: #666;
    color: white;
}

.control-btn.tertiary:hover {
    background: #555;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.recording-guidelines {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: left;
    max-width: 500px;
    margin: 0 auto;
}

.recording-guidelines h4 {
    color: #5a2b81;
    margin-bottom: 15px;
}

.recording-guidelines ul {
    list-style: none;
    padding-left: 0;
}

.recording-guidelines li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
}

.recording-guidelines li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #4CAF50;
    font-weight: bold;
}

/* Processing Status */
.processing-status {
    text-align: center;
    padding: 40px;
}

.processing-animation {
    margin-bottom: 20px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #5a2b81;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.processing-status h3 {
    color: #5a2b81;
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    max-width: 400px;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    margin: 20px auto;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #5a2b81, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.processing-details {
    color: #666;
    font-size: 0.9rem;
}

/* Results Section */
.results-section h3 {
    color: #5a2b81;
    margin-bottom: 20px;
    text-align: center;
}

.result-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 20px;
}

.prediction-text {
    margin-bottom: 20px;
}

.prediction-text label {
    display: block;
    font-weight: 600;
    color: #5a2b81;
    margin-bottom: 8px;
}

.prediction-display {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
    min-height: 50px;
    display: flex;
    align-items: center;
}

.confidence-score {
    margin-bottom: 20px;
}

.confidence-score label {
    display: block;
    font-weight: 600;
    color: #5a2b81;
    margin-bottom: 8px;
}

.confidence-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4CAF50;
}

.model-info {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(90, 43, 129, 0.1);
    border-radius: 8px;
}

.model-info label {
    font-weight: 600;
    color: #5a2b81;
}

.processing-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.metric {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.metric label {
    display: block;
    font-weight: 600;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.metric span {
    font-size: 1.1rem;
    color: #333;
}

.result-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.action-btn {
    background: #5a2b81;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #4a1f6b;
    transform: translateY(-2px);
}

/* Error Section */
.error-section {
    text-align: center;
    padding: 40px;
}

.error-card {
    background: #fff5f5;
    border: 2px solid #fed7d7;
    border-radius: 15px;
    padding: 30px;
    max-width: 600px;
    margin: 0 auto;
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.error-card h3 {
    color: #e53e3e;
    margin-bottom: 15px;
}

.error-details {
    margin: 20px 0;
    text-align: left;
}

.error-details summary {
    cursor: pointer;
    font-weight: 600;
    color: #666;
}

.error-details pre {
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    font-size: 0.9rem;
    margin-top: 10px;
}

/* Footer */
.footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    padding: 20px;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .testing-options {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .webcam-controls {
        flex-direction: column;
        align-items: center;
    }

    .result-actions {
        flex-direction: column;
        align-items: center;
    }

    .processing-metrics {
        grid-template-columns: 1fr;
    }

    .main-content {
        padding: 20px;
    }
}
