#!/usr/bin/env python3
"""
Simple HTTP server for SMHS Lipreader Web Testing Interface
Serves the web interface on localhost:8080
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers to allow cross-origin requests
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # Custom logging format
        print(f"[WEB SERVER] {format % args}")

def main():
    # Change to the web interface directory
    web_dir = Path(__file__).parent
    os.chdir(web_dir)
    
    PORT = 8080
    
    print("🌐 SMHS Lipreader Web Testing Interface")
    print("=" * 50)
    print(f"Starting web server on port {PORT}")
    print(f"Serving files from: {web_dir}")
    print(f"Web interface: http://localhost:{PORT}")
    print(f"Backend expected: http://localhost:5001")
    print("=" * 50)
    print("Press Ctrl+C to stop the server")
    print()
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Server running at http://localhost:{PORT}")
            print("📱 Open this URL in your browser to start testing")
            print()
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use")
            print("Try stopping other servers or use a different port")
        else:
            print(f"❌ Server error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
