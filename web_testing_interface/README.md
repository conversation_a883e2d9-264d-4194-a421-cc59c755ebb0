# SMHS Lipreader Web Testing Interface

A comprehensive browser-based testing application for validating the TRUE ESPnet LRS3_V_WER19.1 transformer model before mobile app deployment.

## 🎯 Purpose

This web interface allows you to:
- Test the TRUE ESPnet lipreading model independently from mobile app complexity
- Validate prediction accuracy with various video inputs
- Monitor backend performance and response times
- Debug model behavior before fixing mobile app issues

## 🚀 Quick Start

### 1. Start the Backend (Required)
Make sure your TRUE ESPnet backend is running:
```bash
cd /path/to/your/backend
python app.py  # Should be running on localhost:5001
```

### 2. Start the Web Interface
```bash
cd web_testing_interface
python3 server.py
```

### 3. Open in Browser
Navigate to: **http://localhost:8080**

## 📋 Features

### 🎥 Testing Options
- **Live Webcam Recording**: Record video directly from your webcam
- **File Upload**: Test with existing video files (MP4, WebM, MOV)
- **Drag & Drop**: Easy file upload interface

### 🧠 Model Testing
- **TRUE ESPnet Integration**: Direct connection to localhost:5001 backend
- **Real-time Processing**: Live status updates during model inference
- **Detailed Results**: Prediction text, confidence scores, processing metrics

### 📊 Performance Monitoring
- **Backend Status**: Real-time connection monitoring
- **Processing Time**: Measure model inference speed
- **Confidence Scoring**: Evaluate prediction reliability
- **Error Handling**: Detailed error reporting and debugging

### 💾 Results Management
- **Download Results**: Export test results as JSON
- **Processing Metrics**: Track performance over multiple tests
- **Test History**: Compare different video inputs

## 🎬 Recording Guidelines

For best lipreading results:
- ✅ Ensure good lighting on your face
- ✅ Position camera at eye level
- ✅ Speak clearly and naturally
- ✅ Keep your face centered in the frame
- ✅ Record for 5-15 seconds per test

## 🔧 Technical Details

### Frontend
- **Pure HTML/CSS/JavaScript** - No frameworks required
- **Modern Web APIs** - MediaRecorder, File API, Fetch API
- **Responsive Design** - Works on desktop and mobile browsers
- **Real-time Updates** - Live processing status and progress

### Backend Integration
- **Endpoint**: `POST /process_video` on localhost:5001
- **Format**: Multipart form data with video file
- **Response**: JSON with prediction and confidence
- **Health Check**: `GET /health` for backend status monitoring

### Supported Video Formats
- **WebM** (preferred for webcam recording)
- **MP4** (common upload format)
- **MOV** (Apple devices)
- **Other formats** supported by HTML5 video

## 🐛 Troubleshooting

### Backend Connection Issues
- Verify ESPnet backend is running on localhost:5001
- Check backend logs for errors
- Ensure CORS is properly configured

### Webcam Access Problems
- Grant camera permissions in browser
- Check if other applications are using the camera
- Try refreshing the page

### Video Upload Issues
- Ensure video file is not corrupted
- Check file size (large files may timeout)
- Verify video format is supported

## 📁 File Structure

```
web_testing_interface/
├── index.html          # Main web interface
├── styles.css          # UI styling and responsive design
├── script.js           # JavaScript functionality
├── server.py           # Local HTTP server
└── README.md           # This documentation
```

## 🔄 Testing Workflow

1. **Start Backend**: Ensure TRUE ESPnet model is running
2. **Open Web Interface**: Navigate to localhost:8080
3. **Check Status**: Verify backend connection (green dot)
4. **Choose Input**: Webcam recording or file upload
5. **Record/Upload**: Capture or select video for testing
6. **Review Results**: Analyze prediction accuracy and confidence
7. **Download Data**: Export results for analysis
8. **Repeat**: Test with different videos and conditions

## 🎯 Validation Goals

Use this interface to:
- **Verify Model Accuracy**: Test with known phrases and sentences
- **Evaluate Confidence Scores**: Understand model certainty levels
- **Test Edge Cases**: Poor lighting, different angles, various speakers
- **Measure Performance**: Processing times and resource usage
- **Debug Issues**: Identify model limitations before mobile deployment

## 🔗 Integration with Mobile App

Once the web testing validates the TRUE ESPnet model:
1. Confirm prediction accuracy meets requirements
2. Verify backend stability and performance
3. Document any model limitations or requirements
4. Return to mobile app development with confidence in the backend

## 📞 Support

If you encounter issues:
1. Check browser console for JavaScript errors
2. Verify backend logs for processing errors
3. Test with different video inputs
4. Ensure all dependencies are properly installed

---

**Ready to validate your TRUE ESPnet LRS3_V_WER19.1 transformer model!** 🚀🧠📹
