<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMHS Lipreader - TRUE ESPnet LRS3_V_WER19.1 Testing Interface</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🎤 SMHS Lipreader Testing Interface</h1>
            <p class="subtitle">TRUE ESPnet LRS3_V_WER19.1 Transformer Model Validation</p>
            <div class="status-indicator">
                <span id="backend-status" class="status-dot offline"></span>
                <span id="backend-text">Checking backend...</span>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Testing Options -->
            <div class="testing-options">
                <div class="option-card" id="webcam-option">
                    <div class="option-icon">📹</div>
                    <h3>Live Webcam Recording</h3>
                    <p>Record video directly from your webcam for real-time testing</p>
                    <button class="option-btn" onclick="initWebcam()">Start Webcam</button>
                </div>
                
                <div class="option-card" id="upload-option">
                    <div class="option-icon">📁</div>
                    <h3>Upload Video File</h3>
                    <p>Test with existing video files (MP4, WebM, MOV)</p>
                    <div class="upload-area" id="upload-area">
                        <p>Drag & drop video files here or click to browse</p>
                        <input type="file" id="file-input" accept="video/*" multiple>
                    </div>
                </div>
            </div>

            <!-- Webcam Interface -->
            <div class="webcam-interface" id="webcam-interface" style="display: none;">
                <div class="video-container">
                    <video id="webcam-video" autoplay muted playsinline></video>
                    <div class="recording-overlay" id="recording-overlay">
                        <div class="recording-indicator">
                            <span class="recording-dot"></span>
                            <span id="recording-timer">00:00</span>
                        </div>
                    </div>
                </div>
                
                <div class="webcam-controls">
                    <button id="start-recording" class="control-btn primary">🎤 Start Recording</button>
                    <button id="stop-recording" class="control-btn secondary" disabled>⏹️ Stop Recording</button>
                    <button id="close-webcam" class="control-btn tertiary">❌ Close Webcam</button>
                </div>
                
                <div class="recording-guidelines">
                    <h4>📋 Recording Guidelines for Best Results:</h4>
                    <ul>
                        <li>Ensure good lighting on your face</li>
                        <li>Position camera at eye level</li>
                        <li>Speak clearly and naturally</li>
                        <li>Keep your face centered in the frame</li>
                        <li>Record for 5-15 seconds per test</li>
                    </ul>
                </div>
            </div>

            <!-- Processing Status -->
            <div class="processing-status" id="processing-status" style="display: none;">
                <div class="processing-animation">
                    <div class="spinner"></div>
                </div>
                <h3>Processing Video with TRUE ESPnet Model...</h3>
                <p id="processing-message">Uploading video to backend...</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="processing-details">
                    <span id="processing-time">Processing time: 0s</span>
                </div>
            </div>

            <!-- Results Display -->
            <div class="results-section" id="results-section" style="display: none;">
                <h3>🧠 Lipreading Results</h3>
                <div class="result-card">
                    <div class="prediction-text">
                        <label>Predicted Text:</label>
                        <div id="prediction-result" class="prediction-display"></div>
                    </div>
                    <div class="confidence-score">
                        <label>Confidence Score:</label>
                        <div id="confidence-result" class="confidence-display"></div>
                    </div>
                    <div class="model-info">
                        <label>Model:</label>
                        <span>ESPnet LRS3_V_WER19.1 Transformer</span>
                    </div>
                    <div class="processing-metrics">
                        <div class="metric">
                            <label>Processing Time:</label>
                            <span id="final-processing-time">-</span>
                        </div>
                        <div class="metric">
                            <label>Video Duration:</label>
                            <span id="video-duration">-</span>
                        </div>
                        <div class="metric">
                            <label>Backend Status:</label>
                            <span id="backend-response-status">-</span>
                        </div>
                    </div>
                </div>
                
                <div class="result-actions">
                    <button class="action-btn" onclick="testAnother()">🔄 Test Another Video</button>
                    <button class="action-btn" onclick="downloadResults()">💾 Download Results</button>
                </div>
            </div>

            <!-- Error Display -->
            <div class="error-section" id="error-section" style="display: none;">
                <div class="error-card">
                    <div class="error-icon">⚠️</div>
                    <h3>Error Processing Video</h3>
                    <p id="error-message"></p>
                    <div class="error-details">
                        <details>
                            <summary>Technical Details</summary>
                            <pre id="error-details"></pre>
                        </details>
                    </div>
                    <button class="action-btn" onclick="testAnother()">🔄 Try Again</button>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>SMHS Lipreader - TRUE AI Medical Communication System</p>
            <p>Backend: <span id="backend-url">http://localhost:5001</span></p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
