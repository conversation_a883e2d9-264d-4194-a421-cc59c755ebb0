// API service for SMHS Lipreader
const API_BASE_URL = 'http://*************:5001';

export const apiRequest = async (method, endpoint, data) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  console.log('API request to:', url);
  
  let headers = {};
  let body = undefined;
  
  if (data) {
    if (data instanceof FormData) {
      // Don't set headers for FormData, fetch will do it
      body = data;
    } else {
      headers = { 'Content-Type': 'application/json' };
      body = JSON.stringify(data);
    }
  }
  
  try {
    const response = await fetch(url, {
      method,
      headers,
      body,
    });
    
    if (!response.ok) {
      const text = await response.text() || response.statusText;
      throw new Error(`${response.status}: ${text}`);
    }
    
    return response;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

export const uploadVideoForPrediction = async (videoUri) => {
  try {
    // Create FormData for video upload
    const formData = new FormData();
    formData.append('video', {
      uri: videoUri,
      type: 'video/mp4',
      name: 'lipreading_video.mp4',
    });
    
    console.log('Uploading video for prediction:', videoUri);
    
    const response = await apiRequest('POST', '/process_video', formData);
    const result = await response.json();
    
    return result;
  } catch (error) {
    console.error('Video upload failed:', error);
    throw error;
  }
};

// Medical phrases for ICU patients
export const MEDICAL_PHRASES = [
  "I need water",
  "I'm in pain", 
  "Call the nurse",
  "Help me please",
  "I can't breathe",
  "I need medication",
  "I'm uncomfortable",
  "Turn me over",
  "I'm cold",
  "I'm hot",
  "I need the bathroom",
  "I'm nauseous",
  "My head hurts",
  "I'm dizzy",
  "I can't sleep",
  "I'm scared",
  "Thank you",
  "Yes",
  "No",
  "Stop",
  "More",
  "Less",
  "Slow down",
  "Faster",
  "I understand"
];

export default {
  apiRequest,
  uploadVideoForPrediction,
  MEDICAL_PHRASES
};
