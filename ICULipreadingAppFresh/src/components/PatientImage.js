import React from 'react';
import Svg, { 
  Rect, 
  Ellipse, 
  Path, 
  Line, 
  Text as SvgText 
} from 'react-native-svg';

const PatientImage = ({ width = 240, height = 240 }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 240 240">
      <Rect width="240" height="240" fill="#ffffff" rx="8" ry="8"/>
      
      {/* Background hospital/room elements */}
      <Rect x="0" y="0" width="240" height="60" fill="#f2f2f2" opacity="0.3"/>
      <Rect x="180" y="10" width="30" height="30" rx="15" fill="#e6e6e6" opacity="0.5"/>
      
      {/* Face outline - older man */}
      <Ellipse cx="120" cy="110" rx="65" ry="75" fill="#f2d6bd"/>
      
      {/* Hair - gray/white hair for older man */}
      <Path d="M70 70 Q95 40 120 45 Q150 40 170 70 Q180 90 175 110 Q170 80 160 75 Q140 50 100 55 Q80 65 70 70" fill="#e0e0e0"/>
      <Path d="M70 70 Q75 60 85 55 Q100 45 120 45 Q140 45 155 55 Q165 60 170 70 Q160 55 140 45 Q120 40 100 45 Q80 50 70 70" fill="#f5f5f5"/>
      
      {/* Ears */}
      <Ellipse cx="55" cy="110" rx="10" ry="20" fill="#e8c9b0"/>
      <Ellipse cx="185" cy="110" rx="10" ry="20" fill="#e8c9b0"/>
      
      {/* Wrinkles for older appearance */}
      <Path d="M75 90 Q95 95 105 90" stroke="#d4b89e" strokeWidth="1.5" fill="none"/>
      <Path d="M135 90 Q145 95 165 90" stroke="#d4b89e" strokeWidth="1.5" fill="none"/>
      <Path d="M75 95 Q95 100 105 95" stroke="#d4b89e" strokeWidth="1.5" fill="none"/>
      <Path d="M135 95 Q145 100 165 95" stroke="#d4b89e" strokeWidth="1.5" fill="none"/>
      <Path d="M75 135 Q100 140 120 140 Q140 140 165 135" stroke="#d4b89e" strokeWidth="1.5" fill="none"/>
      <Path d="M85 145 Q100 150 120 150 Q140 150 155 145" stroke="#d4b89e" strokeWidth="1.5" fill="none"/>
      
      {/* Eyes with glasses */}
      <Ellipse cx="90" cy="100" rx="12" ry="8" fill="white"/>
      <Ellipse cx="150" cy="100" rx="12" ry="8" fill="white"/>
      <Ellipse cx="90" cy="100" rx="5" ry="5" fill="#5a4f7a"/>
      <Ellipse cx="150" cy="100" rx="5" ry="5" fill="#5a4f7a"/>
      <Ellipse cx="92" cy="98" rx="2" ry="2" fill="white"/>
      <Ellipse cx="152" cy="98" rx="2" ry="2" fill="white"/>
      
      {/* Glasses */}
      <Rect x="73" y="92" width="34" height="16" rx="8" ry="8" stroke="#666" strokeWidth="2" fill="none"/>
      <Rect x="133" y="92" width="34" height="16" rx="8" ry="8" stroke="#666" strokeWidth="2" fill="none"/>
      <Line x1="107" y1="100" x2="133" y2="100" stroke="#666" strokeWidth="2"/>
      
      {/* Nose */}
      <Path d="M120 100 Q115 120 120 125 Q125 120 120 100" fill="#e8c9b0"/>
      
      {/* Mouth - slightly open, speaking */}
      <Path d="M100 140 Q120 155 140 140" fill="#b92c38"/>
      <Path d="M105 140 Q120 150 135 140" fill="#701f29"/>
      
      {/* Neck */}
      <Path d="M95 170 Q120 180 145 170 L150 210 Q120 220 90 210 L95 170" fill="#f2d6bd"/>
      
      {/* Hospital gown */}
      <Path d="M50 180 Q120 200 190 180 L200 240 L40 240 L50 180" fill="#f5f5f5"/>
      
      {/* Phone/device frame */}
      <Rect x="85" y="30" width="70" height="120" rx="10" ry="10" fill="#444"/>
      <Rect x="90" y="40" width="60" height="100" rx="2" ry="2" fill="#fff"/>
      <Rect x="115" y="35" width="10" height="2" rx="1" ry="1" fill="#222"/>
      
      {/* SMHS purple brand color as accent */}
      <Rect x="92" y="42" width="56" height="15" fill="#5a2b81"/>
      <SvgText x="95" y="53" fontFamily="Arial" fontSize="8" fill="white">SMHS Lipreader</SvgText>
      
      {/* Device-on-device concept */}
      <Rect x="95" y="60" width="50" height="75" rx="5" ry="5" fill="#f0f0f0"/>
      <Ellipse cx="120" cy="98" rx="20" ry="25" fill="#f2d6bd"/>
      <Path d="M110 110 Q120 115 130 110" fill="#b92c38" stroke="none"/>
    </Svg>
  );
};

export default PatientImage;
