import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';

export default function App() {
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      <Text style={styles.title}>🏥 ICU Lipreading Assistant</Text>
      <Text style={styles.subtitle}>Helping you communicate when you can't speak</Text>

      <View style={styles.illustrationContainer}>
        <Text style={styles.illustration}>👤</Text>
        <View style={styles.speechBubble}>
          <Text style={styles.speechText}>I need water</Text>
        </View>
      </View>

      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>How it works:</Text>

        <View style={styles.feature}>
          <Text style={styles.featureIcon}>📹</Text>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>1. Position Your Face</Text>
            <Text style={styles.featureDescription}>
              Place your face in the oval guide on the camera screen
            </Text>
          </View>
        </View>

        <View style={styles.feature}>
          <Text style={styles.featureIcon}>👄</Text>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>2. Mouth Your Words</Text>
            <Text style={styles.featureDescription}>
              Speak clearly with your lips while recording (no sound needed)
            </Text>
          </View>
        </View>

        <View style={styles.feature}>
          <Text style={styles.featureIcon}>💬</Text>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>3. Get Your Message</Text>
            <Text style={styles.featureDescription}>
              The app will convert your lip movements to text
            </Text>
          </View>
        </View>
      </View>

      <TouchableOpacity style={styles.startButton}>
        <Text style={styles.startButtonText}>Start Communication ➡️</Text>
      </TouchableOpacity>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Designed for ICU patients with tracheostomy procedures
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingTop: 60,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007AFF',
    textAlign: 'center',
    marginBottom: 8,
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 40,
    paddingHorizontal: 24,
    lineHeight: 22,
  },
  illustrationContainer: {
    alignItems: 'center',
    marginBottom: 40,
    paddingHorizontal: 24,
  },
  illustration: {
    fontSize: 80,
    marginBottom: 16,
  },
  speechBubble: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    position: 'relative',
  },
  speechText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  featuresContainer: {
    paddingHorizontal: 24,
    marginBottom: 40,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 24,
    textAlign: 'center',
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  featureIcon: {
    fontSize: 24,
    marginRight: 16,
    marginTop: 2,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  startButton: {
    backgroundColor: '#007AFF',
    marginHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  footerText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    lineHeight: 18,
  },
});
