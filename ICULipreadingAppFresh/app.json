{"expo": {"name": "SMHS Lipreader", "slug": "smhs-lipreader", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "SMHS Lipreader needs camera access to record video for lipreading analysis.", "NSMicrophoneUsageDescription": "SMHS Lipreader needs microphone access for video recording."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO"], "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/favicon.png"}}}