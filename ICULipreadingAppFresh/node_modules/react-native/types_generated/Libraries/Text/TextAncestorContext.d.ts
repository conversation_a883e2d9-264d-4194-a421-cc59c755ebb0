/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<e93bba7322b7ea4dc63573c0204af6ee>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Text/TextAncestorContext.js
 */

import * as React from "react";
declare const TextAncestorContext: React.Context<boolean>;
/**
 * Whether the current element is the descendant of a <Text> element.
 */
declare const $$TextAncestorContext: typeof TextAncestorContext;
declare type $$TextAncestorContext = typeof $$TextAncestorContext;
export default $$TextAncestorContext;
