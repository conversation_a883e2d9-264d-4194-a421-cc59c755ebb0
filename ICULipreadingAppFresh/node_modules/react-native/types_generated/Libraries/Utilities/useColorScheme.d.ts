/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<289a827cb1dd4903c36451996b8cdc68>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Utilities/useColorScheme.js
 */

import type { ColorSchemeName } from "./NativeAppearance";
declare function useColorScheme(): null | undefined | ColorSchemeName;
export default useColorScheme;
