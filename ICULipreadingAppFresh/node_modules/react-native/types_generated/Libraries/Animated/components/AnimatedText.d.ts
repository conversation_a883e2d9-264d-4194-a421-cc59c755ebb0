/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<49a7d653104f009d30b8200bb01678c9>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/components/AnimatedText.js
 */

import type { AnimatedComponentType } from "../createAnimatedComponent";
import Text, { type TextProps } from "../../Text/Text";
import * as React from "react";
declare const $$AnimatedText: AnimatedComponentType<TextProps, React.ComponentRef<typeof Text>>;
declare type $$AnimatedText = typeof $$AnimatedText;
export default $$AnimatedText;
