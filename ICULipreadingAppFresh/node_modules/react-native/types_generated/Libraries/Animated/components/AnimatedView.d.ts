/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<4df1184d42ff255b692006941e5266ef>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/components/AnimatedView.js
 */

import type { ViewProps } from "../../Components/View/ViewPropTypes";
import type { AnimatedComponentType } from "../createAnimatedComponent";
import View from "../../Components/View/View";
import * as React from "react";
declare const $$AnimatedView: AnimatedComponentType<ViewProps, React.ComponentRef<typeof View>>;
declare type $$AnimatedView = typeof $$AnimatedView;
export default $$AnimatedView;
