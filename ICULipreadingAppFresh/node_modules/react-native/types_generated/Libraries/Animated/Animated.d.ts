/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<bdf39377a8da5a0ab65e2d04e0298b92>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/Animated.js.flow
 */

export * as default from "./AnimatedExports";
export type { CompositeAnimation, Numeric } from "./AnimatedImplementation";
