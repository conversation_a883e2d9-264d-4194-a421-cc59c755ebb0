/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<3f2fda38d84b5e80bdc7e4a3e8d67538>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/ReactNative/AppRegistry.js.flow
 */

export type { TaskProvider, ComponentProvider, ComponentProviderInstrumentationHook, AppConfig, Runnable, Runnables, Registry, WrapperComponentProvider, RootViewStyleProvider } from "./AppRegistry.flow";
export * as AppRegistry from "./AppRegistryImpl";
