/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<cbaf437d52a127e8a4c69844cf881015>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/EventEmitter/RCTEventEmitter.js
 */

declare const RCTEventEmitter: {
  register(eventEmitter: any): void;
};
declare const $$RCTEventEmitter: typeof RCTEventEmitter;
declare type $$RCTEventEmitter = typeof $$RCTEventEmitter;
export default $$RCTEventEmitter;
