/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<02559d35812b41b8197de0b723f016df>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/EventEmitter/RCTNativeAppEventEmitter.js
 */

import RCTDeviceEventEmitter from "./RCTDeviceEventEmitter";
declare const RCTNativeAppEventEmitter: typeof RCTDeviceEventEmitter;
/**
 * Deprecated - subclass NativeEventEmitter to create granular event modules instead of
 * adding all event listeners directly to RCTNativeAppEventEmitter.
 */
declare const $$RCTNativeAppEventEmitter: typeof RCTNativeAppEventEmitter;
declare type $$RCTNativeAppEventEmitter = typeof $$RCTNativeAppEventEmitter;
export default $$RCTNativeAppEventEmitter;
