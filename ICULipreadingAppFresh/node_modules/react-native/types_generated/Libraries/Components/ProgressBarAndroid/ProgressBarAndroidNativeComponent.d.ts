/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<9fe473a21a6a02fa0ab8ba54e81407c7>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/ProgressBarAndroid/ProgressBarAndroidNativeComponent.js
 */

export * from "../../../src/private/specs_DEPRECATED/components/ProgressBarAndroidNativeComponent";
export { default } from "../../../src/private/specs_DEPRECATED/components/ProgressBarAndroidNativeComponent";
