/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<2c803a2214d7c0f890878abeb2e9e754>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Types/CodegenTypesNamespace.js
 */

import * as CodegenTypes from "./CodegenTypes";
export type { CodegenTypes };
