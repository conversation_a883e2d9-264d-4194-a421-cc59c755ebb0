/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import <Foundation/Foundation.h>

#include <react/utils/FollyConvert.h>

// This file has been moved to `react/utils/FollyConvert.h`.
// We are keeping the header here for backwards compatibility.

#warning \
    "[DEPRECATION] `React/RCTFollyconvert.h` is deprecated and will be removed in the future. \
    If this warning appears due to a library, please open an issue in that library, and ask for an update. \
    Please, replace the `#include <React/RCTFollyConvert.h>` statements \
    with `#include <react/utils/FollyConvert.h>`."
