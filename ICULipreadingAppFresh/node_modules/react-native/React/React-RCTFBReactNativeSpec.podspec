# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

require "json"

# This Podspec wraps the code generated by Codegen that defines all the interfaces for the
# internal Native Modules that React Native depends on.

package = JSON.parse(File.read(File.join(__dir__, "..", "package.json")))
version = package['version']

source = { :git => 'https://github.com/facebook/react-native.git' }
if version == '1000.0.0'
  # This is an unpublished version, use the latest commit hash of the react-native repo, which we’re presumably in.
  source[:commit] = `git rev-parse HEAD`.strip if system("git rev-parse --git-dir > /dev/null 2>&1")
else
  source[:tag] = "v#{version}"
end

header_search_paths = [
  "\"$(PODS_TARGET_SRCROOT)/FBReactNativeSpec\"",
  "\"$(PODS_ROOT)/Headers/Private/Yoga\"",
]

new_arch_flags = ENV['RCT_NEW_ARCH_ENABLED'] == '1' ? ' -DRCT_NEW_ARCH_ENABLED=1' : ''

Pod::Spec.new do |s|
  s.name                   = "React-RCTFBReactNativeSpec"
  s.version                = version
  s.summary                = "FBReactNativeSpec for React Native."
  s.homepage               = "https://reactnative.dev/"
  s.license                = package["license"]
  s.author                 = "Meta Platforms, Inc. and its affiliates"
  s.platforms              = min_supported_versions
  s.source                 = source
  s.source_files           = podspec_sources("FBReactNativeSpec/**/*.{c,h,m,mm,cpp}", "FBReactNativeSpec/**/*.{h}")
  s.exclude_files          = "FBReactNativeSpec/react/renderer/components",
  s.compiler_flags         = new_arch_flags
  s.header_dir             = 'FBReactNativeSpec'
  s.pod_target_xcconfig    = {
    "USE_HEADERMAP" => "NO",
    "CLANG_CXX_LANGUAGE_STANDARD" => rct_cxx_language_standard(),
    "HEADER_SEARCH_PATHS" => header_search_paths.join(' '),
  }

  resolve_use_frameworks(s, header_mappings_dir: 'FBReactNativeSpec', module_name: "React_RCTFBReactNativeSpec")

  s.dependency "React-jsi"
  s.dependency "RCTRequired"
  s.dependency "RCTTypeSafety"
  s.dependency "React-Core"
  s.dependency "React-NativeModulesApple"

  add_dependency(s, "ReactCommon", :subspec => "turbomodule/core", :additional_framework_paths => ["react/nativemodule/core"])
  add_dependency(s, "ReactCommon", :subspec => "turbomodule/bridging", :additional_framework_paths => ["react/nativemodule/bridging"])

  depend_on_js_engine(s)
  add_rn_third_party_dependencies(s)
  add_rncore_dependency(s)

  s.subspec "components" do |ss|
    ss.source_files         = podspec_sources("FBReactNativeSpec/react/renderer/components/FBReactNativeSpec/**/*.{m,mm,cpp,h}", "FBReactNativeSpec/react/renderer/components/FBReactNativeSpec/**/*.{h}")
    ss.header_dir           = "react/renderer/components/FBReactNativeSpec"

    add_dependency(ss, "React-featureflags")
    add_dependency(ss, "React-debug")
    add_dependency(ss, "React-rendererdebug")
    add_dependency(ss, "React-utils")
    add_dependency(ss, "React-graphics", :additional_framework_paths => ["react/renderer/graphics/platform/ios"])
    add_dependency(ss, "React-Fabric", :additional_framework_paths => [
      "react/renderer/components/view/platform/cxx",
    ])

    ss.dependency "Yoga"
  end

  s.script_phases = [
    {
      :name => '[RN]Check FBReactNativeSpec',
      :execution_position => :before_compile,
      :always_out_of_date => '1',
      :script => <<-EOS
echo "Checking whether Codegen has run..."
fbReactNativeSpecPath="$REACT_NATIVE_PATH/React/FBReactNativeSpec"

if [[ ! -d "$fbReactNativeSpecPath" ]]; then
  echo 'error: Codegen did not run properly in your project. Please reinstall cocoapods with `bundle exec pod install`.'
  exit 1
fi
      EOS
    }
  ]
end
