[versions]
# Android versions
minSdk = "24"
targetSdk = "36"
compileSdk = "36"
buildTools = "36.0.0"
ndkVersion = "27.1.12297006"
# Dependencies versions
agp = "8.11.0"
androidx-annotation = "1.6.0"
androidx-appcompat = "1.7.0"
androidx-autofill = "1.1.0"
androidx-benchmark-macro-junit4 = "1.3.3"
androidx-profileinstaller = "1.4.1"
androidx-swiperefreshlayout = "1.1.0"
androidx-test = "1.5.0"
androidx-test-junit = "1.2.1"
androidx-tracing = "1.1.0"
assertj = "3.21.0"
binary-compatibility-validator = "0.13.2"
download = "5.4.0"
espresso-core = "3.6.1"
fbjni = "0.7.0"
fresco = "3.6.0"
infer-annotation = "0.18.0"
javax-annotation-api = "1.3.2"
javax-inject = "1"
jsc-android = "2026004.0.1"
jsr305 = "3.0.2"
junit = "4.13.2"
kotlin = "2.1.20"
mockito = "3.12.4"
mockito-kotlin = "3.2.0"
nexus-publish = "1.3.0"
okhttp = "4.9.2"
okio = "2.9.0"
robolectric = "4.9.2"
soloader = "0.12.1"
uiautomator = "2.3.0"
xstream = "1.4.20"
yoga-proguard-annotations = "1.19.0"
# Native Dependencies
boost="1_83_0"
doubleconversion="1.1.6"
fastFloat="8.0.0"
fmt="11.0.2"
folly="2024.11.18.00"
glog="0.3.5"
gflags="2.2.0"
nlohmannjson="3.11.2"

[libraries]
androidx-annotation = { module = "androidx.annotation:annotation", version.ref = "androidx-annotation" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "androidx-appcompat" }
androidx-appcompat-resources = { module = "androidx.appcompat:appcompat-resources", version.ref = "androidx-appcompat" }
androidx-autofill = { module = "androidx.autofill:autofill", version.ref = "androidx-autofill" }
androidx-benchmark-macro-junit4 = { group = "androidx.benchmark", name = "benchmark-macro-junit4", version.ref = "androidx-benchmark-macro-junit4" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espresso-core" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidx-test-junit" }
androidx-profileinstaller = { group = "androidx.profileinstaller", name = "profileinstaller", version.ref = "androidx-profileinstaller" }
androidx-swiperefreshlayout = { module = "androidx.swiperefreshlayout:swiperefreshlayout", version.ref = "androidx-swiperefreshlayout" }
androidx-test-rules = { module = "androidx.test:rules", version.ref = "androidx-test" }
androidx-test-runner = { module = "androidx.test:runner", version.ref = "androidx-test" }
androidx-tracing = { module = "androidx.tracing:tracing", version.ref = "androidx-tracing" }
androidx-uiautomator = { group = "androidx.test.uiautomator", name = "uiautomator", version.ref = "uiautomator" }

fbjni = { module = "com.facebook.fbjni:fbjni", version.ref = "fbjni" }
fresco = { module = "com.facebook.fresco:fresco", version.ref = "fresco" }
fresco-middleware = { module = "com.facebook.fresco:middleware", version.ref = "fresco" }
fresco-imagepipeline-okhttp3 = { module = "com.facebook.fresco:imagepipeline-okhttp3", version.ref = "fresco" }
fresco-ui-common = { module = "com.facebook.fresco:ui-common", version.ref = "fresco" }
infer-annotation = { module = "com.facebook.infer.annotation:infer-annotation", version.ref = "infer-annotation" }
soloader = { module = "com.facebook.soloader:soloader", version.ref = "soloader" }
yoga-proguard-annotations = { module = "com.facebook.yoga:proguard-annotations", version.ref = "yoga-proguard-annotations" }

jsr305 = { module = "com.google.code.findbugs:jsr305", version.ref = "jsr305" }
okhttp3-urlconnection = { module = "com.squareup.okhttp3:okhttp-urlconnection", version.ref = "okhttp" }
okhttp3 = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
okio = { module = "com.squareup.okio:okio", version.ref = "okio" }
javax-inject = { module = "javax.inject:javax.inject", version.ref = "javax-inject" }
javax-annotation-api = { module = "javax.annotation:javax.annotation-api", version.ref = "javax-annotation-api" }
jsc-android = { module = "io.github.react-native-community:jsc-android", version.ref = "jsc-android" }

junit = {module = "junit:junit", version.ref = "junit" }
assertj = {module = "org.assertj:assertj-core", version.ref = "assertj" }
mockito = {module = "org.mockito:mockito-inline", version.ref = "mockito" }
mockito-kotlin = {module = "org.mockito.kotlin:mockito-kotlin", version.ref = "mockito-kotlin" }
robolectric = {module = "org.robolectric:robolectric", version.ref = "robolectric" }
thoughtworks = {module = "com.thoughtworks.xstream:xstream", version.ref = "xstream" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
download = { id = "de.undercouch.download", version.ref = "download" }
nexus-publish = { id = "io.github.gradle-nexus.publish-plugin", version.ref = "nexus-publish" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
binary-compatibility-validator = { id = "org.jetbrains.kotlinx.binary-compatibility-validator", version.ref = "binary-compatibility-validator" }
android-test = { id = "com.android.test", version.ref = "agp" }
