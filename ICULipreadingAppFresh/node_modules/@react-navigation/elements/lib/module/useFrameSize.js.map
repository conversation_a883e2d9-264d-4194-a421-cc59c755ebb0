{"version": 3, "names": ["React", "Platform", "StyleSheet", "useSafeAreaFrame", "useLatestCallback", "useSyncExternalStoreWithSelector", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "SafeAreaListener", "require", "FrameContext", "createContext", "undefined", "useFrameSize", "selector", "throttle", "context", "useContext", "Error", "value", "subscribeThrottled", "subscribe", "get<PERSON>urrent", "FrameSizeProvider", "initialFrame", "children", "FrameSizeProviderInner", "frameRef", "useRef", "width", "height", "listeners", "Set", "current", "listener", "add", "delete", "delay", "timer", "updated", "waiting", "throttledListener", "clearTimeout", "setTimeout", "unsubscribe", "useMemo", "onChange", "frame", "for<PERSON>ach", "OS", "FrameSizeListenerWeb", "FrameSizeListenerNativeFallback", "style", "absoluteFill", "Provider", "useLayoutEffect", "elementRef", "useEffect", "rect", "getBoundingClientRect", "observer", "ResizeObserver", "entries", "entry", "contentRect", "observe", "disconnect", "ref", "position", "left", "right", "top", "bottom", "pointerEvents", "visibility"], "sourceRoot": "../../src", "sources": ["useFrameSize.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAERC,UAAU,QAEL,cAAc;AACrB;AACE;AACAC,gBAAgB,QACX,gCAAgC;AACvC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,gCAAgC,QAAQ,uCAAuC;;AAExF;AACA;AAAA,SAAAC,GAAA,IAAAC,IAAA,EAAAC,QAAA,IAAAC,SAAA,EAAAC,IAAA,IAAAC,KAAA;AACA,MAAMC,gBAAgB,GAAGC,OAAO,CAAC,gCAAgC,CAAC,CAC/DD,gBAEU;AAiBb,MAAME,YAAY,gBAAGd,KAAK,CAACe,aAAa,CACtCC,SACF,CAAC;AAED,OAAO,SAASC,YAAYA,CAC1BC,QAA6B,EAC7BC,QAAkB,EACf;EACH,MAAMC,OAAO,GAAGpB,KAAK,CAACqB,UAAU,CAACP,YAAY,CAAC;EAE9C,IAAIM,OAAO,IAAI,IAAI,EAAE;IACnB,MAAM,IAAIE,KAAK,CAAC,sDAAsD,CAAC;EACzE;EAEA,MAAMC,KAAK,GAAGlB,gCAAgC,CAC5Cc,QAAQ,GAAGC,OAAO,CAACI,kBAAkB,GAAGJ,OAAO,CAACK,SAAS,EACzDL,OAAO,CAACM,UAAU,EAClBN,OAAO,CAACM,UAAU,EAClBR,QACF,CAAC;EAED,OAAOK,KAAK;AACd;AAQA,OAAO,SAASI,iBAAiBA,CAAC;EAChCC,YAAY;EACZC;AACsB,CAAC,EAAE;EACzB,MAAMT,OAAO,GAAGpB,KAAK,CAACqB,UAAU,CAACP,YAAY,CAAC;EAE9C,IAAIM,OAAO,IAAI,IAAI,EAAE;IACnB;IACA,OAAOS,QAAQ;EACjB;EAEA,oBACEtB,IAAA,CAACuB,sBAAsB;IAACF,YAAY,EAAEA,YAAa;IAAAC,QAAA,EAChDA;EAAQ,CACa,CAAC;AAE7B;AAEA,SAASC,sBAAsBA,CAAC;EAC9BF,YAAY;EACZC;AACsB,CAAC,EAAE;EACzB,MAAME,QAAQ,GAAG/B,KAAK,CAACgC,MAAM,CAAQ;IACnCC,KAAK,EAAEL,YAAY,CAACK,KAAK;IACzBC,MAAM,EAAEN,YAAY,CAACM;EACvB,CAAC,CAAC;EAEF,MAAMC,SAAS,GAAGnC,KAAK,CAACgC,MAAM,CAAgB,IAAII,GAAG,CAAC,CAAC,CAAC;EAExD,MAAMV,UAAU,GAAGtB,iBAAiB,CAAC,MAAM2B,QAAQ,CAACM,OAAO,CAAC;EAE5D,MAAMZ,SAAS,GAAGrB,iBAAiB,CAAEkC,QAAkB,IAAqB;IAC1EH,SAAS,CAACE,OAAO,CAACE,GAAG,CAACD,QAAQ,CAAC;IAE/B,OAAO,MAAM;MACXH,SAAS,CAACE,OAAO,CAACG,MAAM,CAACF,QAAQ,CAAC;IACpC,CAAC;EACH,CAAC,CAAC;EAEF,MAAMd,kBAAkB,GAAGpB,iBAAiB,CACzCkC,QAAkB,IAAqB;IACtC,MAAMG,KAAK,GAAG,GAAG,CAAC,CAAC;;IAEnB,IAAIC,KAAoC;IACxC,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIC,OAAO,GAAG,KAAK;IAEnB,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9BC,YAAY,CAACJ,KAAK,CAAC;MAEnBC,OAAO,GAAG,IAAI;MAEd,IAAIC,OAAO,EAAE;QACX;QACAF,KAAK,GAAGK,UAAU,CAAC,MAAM;UACvB,IAAIJ,OAAO,EAAE;YACXA,OAAO,GAAG,KAAK;YACfL,QAAQ,CAAC,CAAC;UACZ;QACF,CAAC,EAAEG,KAAK,CAAC;MACX,CAAC,MAAM;QACLG,OAAO,GAAG,IAAI;QACdG,UAAU,CAAC,YAAY;UACrBH,OAAO,GAAG,KAAK;QACjB,CAAC,EAAEH,KAAK,CAAC;;QAET;QACAE,OAAO,GAAG,KAAK;QACfL,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;IAED,MAAMU,WAAW,GAAGvB,SAAS,CAACoB,iBAAiB,CAAC;IAEhD,OAAO,MAAM;MACXG,WAAW,CAAC,CAAC;MACbF,YAAY,CAACJ,KAAK,CAAC;IACrB,CAAC;EACH,CACF,CAAC;EAED,MAAMtB,OAAO,GAAGpB,KAAK,CAACiD,OAAO,CAC3B,OAAO;IACLvB,UAAU;IACVD,SAAS;IACTD;EACF,CAAC,CAAC,EACF,CAACC,SAAS,EAAED,kBAAkB,EAAEE,UAAU,CAC5C,CAAC;EAED,MAAMwB,QAAQ,GAAG9C,iBAAiB,CAAE+C,KAAY,IAAK;IACnD,IACEpB,QAAQ,CAACM,OAAO,CAACH,MAAM,KAAKiB,KAAK,CAACjB,MAAM,IACxCH,QAAQ,CAACM,OAAO,CAACJ,KAAK,KAAKkB,KAAK,CAAClB,KAAK,EACtC;MACA;IACF;IAEAF,QAAQ,CAACM,OAAO,GAAG;MAAEJ,KAAK,EAAEkB,KAAK,CAAClB,KAAK;MAAEC,MAAM,EAAEiB,KAAK,CAACjB;IAAO,CAAC;IAC/DC,SAAS,CAACE,OAAO,CAACe,OAAO,CAAEd,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;EACrD,CAAC,CAAC;EAEF,oBACE3B,KAAA,CAAAF,SAAA;IAAAoB,QAAA,GACG5B,QAAQ,CAACoD,EAAE,KAAK,KAAK,gBACpB9C,IAAA,CAAC+C,oBAAoB;MAACJ,QAAQ,EAAEA;IAAS,CAAE,CAAC,GAC1C,OAAOtC,gBAAgB,KAAK,WAAW,gBACzCL,IAAA,CAACgD,+BAA+B;MAACL,QAAQ,EAAEA;IAAS,CAAE,CAAC,gBAEvD3C,IAAA,CAACK,gBAAgB;MACfsC,QAAQ,EAAEA,CAAC;QAAEC;MAAM,CAAC,KAAKD,QAAQ,CAACC,KAAK,CAAE;MACzCK,KAAK,EAAEtD,UAAU,CAACuD;IAAa,CAChC,CACF,eACDlD,IAAA,CAACO,YAAY,CAAC4C,QAAQ;MAACnC,KAAK,EAAEH,OAAQ;MAAAS,QAAA,EAAEA;IAAQ,CAAwB,CAAC;EAAA,CACzE,CAAC;AAEP;;AAEA;AACA;AACA,SAAS0B,+BAA+BA,CAAC;EACvCL;AAGF,CAAC,EAAE;EACD,MAAMC,KAAK,GAAGhD,gBAAgB,CAAC,CAAC;EAEhCH,KAAK,CAAC2D,eAAe,CAAC,MAAM;IAC1BT,QAAQ,CAACC,KAAK,CAAC;EACjB,CAAC,EAAE,CAACA,KAAK,EAAED,QAAQ,CAAC,CAAC;EAErB,OAAO,IAAI;AACb;;AAEA;AACA;AACA,SAASI,oBAAoBA,CAAC;EAC5BJ;AAGF,CAAC,EAAE;EACD,MAAMU,UAAU,GAAG5D,KAAK,CAACgC,MAAM,CAAiB,IAAI,CAAC;EAErDhC,KAAK,CAAC6D,SAAS,CAAC,MAAM;IACpB,IAAID,UAAU,CAACvB,OAAO,IAAI,IAAI,EAAE;MAC9B;IACF;IAEA,MAAMyB,IAAI,GAAGF,UAAU,CAACvB,OAAO,CAAC0B,qBAAqB,CAAC,CAAC;IAEvDb,QAAQ,CAAC;MACPjB,KAAK,EAAE6B,IAAI,CAAC7B,KAAK;MACjBC,MAAM,EAAE4B,IAAI,CAAC5B;IACf,CAAC,CAAC;IAEF,MAAM8B,QAAQ,GAAG,IAAIC,cAAc,CAAEC,OAAO,IAAK;MAC/C,MAAMC,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC;MAExB,IAAIC,KAAK,EAAE;QACT,MAAM;UAAElC,KAAK;UAAEC;QAAO,CAAC,GAAGiC,KAAK,CAACC,WAAW;QAE3ClB,QAAQ,CAAC;UAAEjB,KAAK;UAAEC;QAAO,CAAC,CAAC;MAC7B;IACF,CAAC,CAAC;IAEF8B,QAAQ,CAACK,OAAO,CAACT,UAAU,CAACvB,OAAO,CAAC;IAEpC,OAAO,MAAM;MACX2B,QAAQ,CAACM,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACpB,QAAQ,CAAC,CAAC;EAEd,oBACE3C,IAAA;IACEgE,GAAG,EAAEX,UAAW;IAChBJ,KAAK,EAAE;MACLgB,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTC,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE;IACd;EAAE,CACH,CAAC;AAEN", "ignoreList": []}