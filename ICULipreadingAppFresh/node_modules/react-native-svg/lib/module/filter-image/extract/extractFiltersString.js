// @generated by Peggy 4.0.3.
//
// https://peggyjs.org/

"use strict";

function buildDropShadow(offsetX, offsetY, blurRadius, color) {
  return [{
    name: "feGaussianBlur",
    in: "SourceAlpha",
    stdDeviation: blurRadius || 0
  }, {
    name: "feOffset",
    dx: offsetX,
    dy: offsetY,
    result: "offsetblur"
  }, {
    name: "feFlood",
    floodColor: color || "transparent"
  }, {
    name: "feComposite",
    in2: "offsetblur",
    operator: "in"
  }, {
    name: "feMerge",
    children: [{
      name: "feMergeNode"
    }, {
      name: "feMergeNode",
      in: "SourceGraphic"
    }]
  }];
}
function peg$subclass(child, parent) {
  function C() {
    this.constructor = child;
  }
  C.prototype = parent.prototype;
  child.prototype = new C();
}
function peg$SyntaxError(message, expected, found, location) {
  var self = Error.call(this, message);
  // istanbul ignore next Check is a necessary evil to support older environments
  if (Object.setPrototypeOf) {
    Object.setPrototypeOf(self, peg$SyntaxError.prototype);
  }
  self.expected = expected;
  self.found = found;
  self.location = location;
  self.name = "SyntaxError";
  return self;
}
peg$subclass(peg$SyntaxError, Error);
function peg$padEnd(str, targetLength, padString) {
  padString = padString || " ";
  if (str.length > targetLength) {
    return str;
  }
  targetLength -= str.length;
  padString += padString.repeat(targetLength);
  return str + padString.slice(0, targetLength);
}
peg$SyntaxError.prototype.format = function (sources) {
  var str = "Error: " + this.message;
  if (this.location) {
    var src = null;
    var k;
    for (k = 0; k < sources.length; k++) {
      if (sources[k].source === this.location.source) {
        src = sources[k].text.split(/\r\n|\n|\r/g);
        break;
      }
    }
    var s = this.location.start;
    var offset_s = this.location.source && typeof this.location.source.offset === "function" ? this.location.source.offset(s) : s;
    var loc = this.location.source + ":" + offset_s.line + ":" + offset_s.column;
    if (src) {
      var e = this.location.end;
      var filler = peg$padEnd("", offset_s.line.toString().length, ' ');
      var line = src[s.line - 1];
      var last = s.line === e.line ? e.column : line.length + 1;
      var hatLen = last - s.column || 1;
      str += "\n --> " + loc + "\n" + filler + " |\n" + offset_s.line + " | " + line + "\n" + filler + " | " + peg$padEnd("", s.column - 1, ' ') + peg$padEnd("", hatLen, "^");
    } else {
      str += "\n at " + loc;
    }
  }
  return str;
};
peg$SyntaxError.buildMessage = function (expected, found) {
  var DESCRIBE_EXPECTATION_FNS = {
    literal: function (expectation) {
      return "\"" + literalEscape(expectation.text) + "\"";
    },
    class: function (expectation) {
      var escapedParts = expectation.parts.map(function (part) {
        return Array.isArray(part) ? classEscape(part[0]) + "-" + classEscape(part[1]) : classEscape(part);
      });
      return "[" + (expectation.inverted ? "^" : "") + escapedParts.join("") + "]";
    },
    any: function () {
      return "any character";
    },
    end: function () {
      return "end of input";
    },
    other: function (expectation) {
      return expectation.description;
    }
  };
  function hex(ch) {
    return ch.charCodeAt(0).toString(16).toUpperCase();
  }
  function literalEscape(s) {
    return s.replace(/\\/g, "\\\\").replace(/"/g, "\\\"").replace(/\0/g, "\\0").replace(/\t/g, "\\t").replace(/\n/g, "\\n").replace(/\r/g, "\\r").replace(/[\x00-\x0F]/g, function (ch) {
      return "\\x0" + hex(ch);
    }).replace(/[\x10-\x1F\x7F-\x9F]/g, function (ch) {
      return "\\x" + hex(ch);
    });
  }
  function classEscape(s) {
    return s.replace(/\\/g, "\\\\").replace(/\]/g, "\\]").replace(/\^/g, "\\^").replace(/-/g, "\\-").replace(/\0/g, "\\0").replace(/\t/g, "\\t").replace(/\n/g, "\\n").replace(/\r/g, "\\r").replace(/[\x00-\x0F]/g, function (ch) {
      return "\\x0" + hex(ch);
    }).replace(/[\x10-\x1F\x7F-\x9F]/g, function (ch) {
      return "\\x" + hex(ch);
    });
  }
  function describeExpectation(expectation) {
    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);
  }
  function describeExpected(expected) {
    var descriptions = expected.map(describeExpectation);
    var i, j;
    descriptions.sort();
    if (descriptions.length > 0) {
      for (i = 1, j = 1; i < descriptions.length; i++) {
        if (descriptions[i - 1] !== descriptions[i]) {
          descriptions[j] = descriptions[i];
          j++;
        }
      }
      descriptions.length = j;
    }
    switch (descriptions.length) {
      case 1:
        return descriptions[0];
      case 2:
        return descriptions[0] + " or " + descriptions[1];
      default:
        return descriptions.slice(0, -1).join(", ") + ", or " + descriptions[descriptions.length - 1];
    }
  }
  function describeFound(found) {
    return found ? "\"" + literalEscape(found) + "\"" : "end of input";
  }
  return "Expected " + describeExpected(expected) + " but " + describeFound(found) + " found.";
};
function peg$parse(input, options) {
  options = options !== undefined ? options : {};
  var peg$FAILED = {};
  var peg$source = options.grammarSource;
  var peg$startRuleFunctions = {
    start: peg$parsestart
  };
  var peg$startRuleFunction = peg$parsestart;
  var peg$c0 = "none";
  var peg$c1 = "grayscale(";
  var peg$c2 = ")";
  var peg$c3 = "sepia(";
  var peg$c4 = "saturate(";
  var peg$c5 = "hue-rotate(";
  var peg$c6 = "invert(";
  var peg$c7 = "opacity(";
  var peg$c8 = "brightness(";
  var peg$c9 = "contrast(";
  var peg$c10 = "blur(";
  var peg$c11 = "drop-shadow(";
  var peg$c12 = "%";
  var peg$c13 = "deg";
  var peg$c14 = "grad";
  var peg$c15 = "rad";
  var peg$c16 = "turn";
  var peg$c17 = "0";
  var peg$c18 = "#";
  var peg$c19 = "rgb(";
  var peg$c20 = ",";
  var peg$c21 = "rgba(";
  var peg$c22 = ".";
  var peg$c23 = "e";
  var peg$r0 = /^[ \t\n\r]/;
  var peg$r1 = /^[a-z\-]/;
  var peg$r2 = /^[0-9a-fA-F]/;
  var peg$r3 = /^[+\-]/;
  var peg$r4 = /^[0-9]/;
  var peg$e0 = peg$otherExpectation("filters or 'none'");
  var peg$e1 = peg$otherExpectation("'none'");
  var peg$e2 = peg$literalExpectation("none", false);
  var peg$e3 = peg$otherExpectation("filters");
  var peg$e4 = peg$otherExpectation("filter");
  var peg$e5 = peg$otherExpectation("grayscale");
  var peg$e6 = peg$literalExpectation("grayscale(", false);
  var peg$e7 = peg$literalExpectation(")", false);
  var peg$e8 = peg$otherExpectation("sepia");
  var peg$e9 = peg$literalExpectation("sepia(", false);
  var peg$e10 = peg$otherExpectation("saturate");
  var peg$e11 = peg$literalExpectation("saturate(", false);
  var peg$e12 = peg$otherExpectation("hue-rotate");
  var peg$e13 = peg$literalExpectation("hue-rotate(", false);
  var peg$e14 = peg$otherExpectation("invert");
  var peg$e15 = peg$literalExpectation("invert(", false);
  var peg$e16 = peg$otherExpectation("opacity");
  var peg$e17 = peg$literalExpectation("opacity(", false);
  var peg$e18 = peg$otherExpectation("brightness");
  var peg$e19 = peg$literalExpectation("brightness(", false);
  var peg$e20 = peg$otherExpectation("contrast");
  var peg$e21 = peg$literalExpectation("contrast(", false);
  var peg$e22 = peg$otherExpectation("blur");
  var peg$e23 = peg$literalExpectation("blur(", false);
  var peg$e24 = peg$otherExpectation("drop-shadow");
  var peg$e25 = peg$literalExpectation("drop-shadow(", false);
  var peg$e26 = peg$otherExpectation("number or percentage");
  var peg$e27 = peg$literalExpectation("%", false);
  var peg$e28 = peg$otherExpectation("angle or zero");
  var peg$e29 = peg$otherExpectation("angle");
  var peg$e30 = peg$literalExpectation("deg", false);
  var peg$e31 = peg$literalExpectation("grad", false);
  var peg$e32 = peg$literalExpectation("rad", false);
  var peg$e33 = peg$literalExpectation("turn", false);
  var peg$e34 = peg$otherExpectation("zero");
  var peg$e35 = peg$literalExpectation("0", false);
  var peg$e36 = peg$otherExpectation("whitespace");
  var peg$e37 = peg$classExpectation([" ", "\t", "\n", "\r"], false, false);
  var peg$e38 = peg$otherExpectation("identifier");
  var peg$e39 = peg$classExpectation([["a", "z"], "-"], false, false);
  var peg$e40 = peg$otherExpectation("color");
  var peg$e41 = peg$literalExpectation("#", false);
  var peg$e42 = peg$classExpectation([["0", "9"], ["a", "f"], ["A", "F"]], false, false);
  var peg$e43 = peg$literalExpectation("rgb(", false);
  var peg$e44 = peg$literalExpectation(",", false);
  var peg$e45 = peg$literalExpectation("rgba(", false);
  var peg$e46 = peg$classExpectation(["+", "-"], false, false);
  var peg$e47 = peg$classExpectation([["0", "9"]], false, false);
  var peg$e48 = peg$literalExpectation(".", false);
  var peg$e49 = peg$literalExpectation("e", false);
  var peg$f0 = function () {
    return null;
  };
  var peg$f1 = function (head, tail) {
    const results = [head];
    tail.forEach(element => {
      if (Array.isArray(element[1])) {
        results.push(...element[1]);
      } else {
        results.push(element[1]);
      }
    });
    return results;
  };
  var peg$f2 = function (value) {
    return {
      name: "feColorMatrix",
      type: "matrix",
      values: [0.2126 + 0.7874 * (1 - value), 0.7152 - 0.7152 * (1 - value), 0.0722 - 0.0722 * (1 - value), 0, 0, 0.2126 - 0.2126 * (1 - value), 0.7152 + 0.2848 * (1 - value), 0.0722 - 0.0722 * (1 - value), 0, 0, 0.2126 - 0.2126 * (1 - value), 0.7152 - 0.7152 * (1 - value), 0.0722 + 0.9278 * (1 - value), 0, 0, 0, 0, 0, 1, 0]
    };
  };
  var peg$f3 = function (value) {
    return {
      name: "feColorMatrix",
      type: "matrix",
      values: [0.393 + 0.607 * (1 - value), 0.769 - 0.769 * (1 - value), 0.189 - 0.189 * (1 - value), 0, 0, 0.349 - 0.349 * (1 - value), 0.686 + 0.314 * (1 - value), 0.168 - 0.168 * (1 - value), 0, 0, 0.272 - 0.272 * (1 - value), 0.534 - 0.534 * (1 - value), 0.131 + 0.869 * (1 - value), 0, 0, 0, 0, 0, 1, 0]
    };
  };
  var peg$f4 = function (value) {
    return {
      name: "feColorMatrix",
      type: "saturate",
      values: [value]
    };
  };
  var peg$f5 = function (value) {
    return {
      name: "feColorMatrix",
      type: "hueRotate",
      values: [value]
    };
  };
  var peg$f6 = function (value) {
    return {
      name: "feComponentTransfer",
      children: [{
        name: "feFuncR",
        type: "table",
        tableValues: [value, 1 - value]
      }, {
        name: "feFuncG",
        type: "table",
        tableValues: [value, 1 - value]
      }, {
        name: "feFuncB",
        type: "table",
        tableValues: [value, 1 - value]
      }]
    };
  };
  var peg$f7 = function (value) {
    return {
      name: "feComponentTransfer",
      children: [{
        name: "feFuncA",
        type: "table",
        tableValues: [0, value]
      }]
    };
  };
  var peg$f8 = function (value) {
    return {
      name: "feComponentTransfer",
      children: [{
        name: "feFuncR",
        type: "linear",
        slope: value
      }, {
        name: "feFuncG",
        type: "linear",
        slope: value
      }, {
        name: "feFuncB",
        type: "linear",
        slope: value
      }]
    };
  };
  var peg$f9 = function (value) {
    return {
      name: "feComponentTransfer",
      children: [{
        name: "feFuncR",
        type: "linear",
        slope: value,
        intercept: -(0.5 * value) + 0.5
      }, {
        name: "feFuncG",
        type: "linear",
        slope: value,
        intercept: -(0.5 * value) + 0.5
      }, {
        name: "feFuncB",
        type: "linear",
        slope: value,
        intercept: -(0.5 * value) + 0.5
      }]
    };
  };
  var peg$f10 = function (value) {
    return {
      name: "feGaussianBlur",
      stdDeviation: value
    };
  };
  var peg$f11 = function (offsetX, offsetY, blurRadius, color) {
    return buildDropShadow(offsetX, offsetY, blurRadius, color);
  };
  var peg$f12 = function (color, offsetX, offsetY, blurRadius) {
    return buildDropShadow(offsetX, offsetY, blurRadius, color);
  };
  var peg$f13 = function (value) {
    return value / 100;
  };
  var peg$f14 = function (value) {
    return value;
  };
  var peg$f15 = function (value) {
    return value * 180 / 200;
  };
  var peg$f16 = function (value) {
    return value * 180 / Math.PI;
  };
  var peg$f17 = function (value) {
    return value * 360;
  };
  var peg$f18 = function () {
    return text();
  };
  var peg$f19 = function () {
    return text();
  };
  var peg$f20 = function (r, g, b) {
    return text();
  };
  var peg$f21 = function (r, g, b, a) {
    return text();
  };
  var peg$f22 = function () {
    return text();
  };
  var peg$f23 = function () {
    return parseFloat(text());
  };
  var peg$currPos = options.peg$currPos | 0;
  var peg$savedPos = peg$currPos;
  var peg$posDetailsCache = [{
    line: 1,
    column: 1
  }];
  var peg$maxFailPos = peg$currPos;
  var peg$maxFailExpected = options.peg$maxFailExpected || [];
  var peg$silentFails = options.peg$silentFails | 0;
  var peg$result;
  if (options.startRule) {
    if (!(options.startRule in peg$startRuleFunctions)) {
      throw new Error("Can't start parsing from rule \"" + options.startRule + "\".");
    }
    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];
  }
  function text() {
    return input.substring(peg$savedPos, peg$currPos);
  }
  function offset() {
    return peg$savedPos;
  }
  function range() {
    return {
      source: peg$source,
      start: peg$savedPos,
      end: peg$currPos
    };
  }
  function location() {
    return peg$computeLocation(peg$savedPos, peg$currPos);
  }
  function expected(description, location) {
    location = location !== undefined ? location : peg$computeLocation(peg$savedPos, peg$currPos);
    throw peg$buildStructuredError([peg$otherExpectation(description)], input.substring(peg$savedPos, peg$currPos), location);
  }
  function error(message, location) {
    location = location !== undefined ? location : peg$computeLocation(peg$savedPos, peg$currPos);
    throw peg$buildSimpleError(message, location);
  }
  function peg$literalExpectation(text, ignoreCase) {
    return {
      type: "literal",
      text: text,
      ignoreCase: ignoreCase
    };
  }
  function peg$classExpectation(parts, inverted, ignoreCase) {
    return {
      type: "class",
      parts: parts,
      inverted: inverted,
      ignoreCase: ignoreCase
    };
  }
  function peg$anyExpectation() {
    return {
      type: "any"
    };
  }
  function peg$endExpectation() {
    return {
      type: "end"
    };
  }
  function peg$otherExpectation(description) {
    return {
      type: "other",
      description: description
    };
  }
  function peg$computePosDetails(pos) {
    var details = peg$posDetailsCache[pos];
    var p;
    if (details) {
      return details;
    } else {
      if (pos >= peg$posDetailsCache.length) {
        p = peg$posDetailsCache.length - 1;
      } else {
        p = pos;
        while (!peg$posDetailsCache[--p]) {}
      }
      details = peg$posDetailsCache[p];
      details = {
        line: details.line,
        column: details.column
      };
      while (p < pos) {
        if (input.charCodeAt(p) === 10) {
          details.line++;
          details.column = 1;
        } else {
          details.column++;
        }
        p++;
      }
      peg$posDetailsCache[pos] = details;
      return details;
    }
  }
  function peg$computeLocation(startPos, endPos, offset) {
    var startPosDetails = peg$computePosDetails(startPos);
    var endPosDetails = peg$computePosDetails(endPos);
    var res = {
      source: peg$source,
      start: {
        offset: startPos,
        line: startPosDetails.line,
        column: startPosDetails.column
      },
      end: {
        offset: endPos,
        line: endPosDetails.line,
        column: endPosDetails.column
      }
    };
    if (offset && peg$source && typeof peg$source.offset === "function") {
      res.start = peg$source.offset(res.start);
      res.end = peg$source.offset(res.end);
    }
    return res;
  }
  function peg$fail(expected) {
    if (peg$currPos < peg$maxFailPos) {
      return;
    }
    if (peg$currPos > peg$maxFailPos) {
      peg$maxFailPos = peg$currPos;
      peg$maxFailExpected = [];
    }
    peg$maxFailExpected.push(expected);
  }
  function peg$buildSimpleError(message, location) {
    return new peg$SyntaxError(message, null, null, location);
  }
  function peg$buildStructuredError(expected, found, location) {
    return new peg$SyntaxError(peg$SyntaxError.buildMessage(expected, found), expected, found, location);
  }
  function peg$parsestart() {
    var s0, s1;
    peg$silentFails++;
    s0 = peg$parsenone();
    if (s0 === peg$FAILED) {
      s0 = peg$parsefiltersList();
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e0);
      }
    }
    return s0;
  }
  function peg$parsenone() {
    var s0, s1;
    peg$silentFails++;
    s0 = peg$currPos;
    if (input.substr(peg$currPos, 4) === peg$c0) {
      s1 = peg$c0;
      peg$currPos += 4;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e2);
      }
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f0();
    }
    s0 = s1;
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e1);
      }
    }
    return s0;
  }
  function peg$parsefiltersList() {
    var s0, s1, s2, s3, s4, s5;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parsefunction();
    if (s1 !== peg$FAILED) {
      s2 = [];
      s3 = peg$currPos;
      s4 = peg$parse_();
      s5 = peg$parsefunction();
      if (s5 !== peg$FAILED) {
        s4 = [s4, s5];
        s3 = s4;
      } else {
        peg$currPos = s3;
        s3 = peg$FAILED;
      }
      while (s3 !== peg$FAILED) {
        s2.push(s3);
        s3 = peg$currPos;
        s4 = peg$parse_();
        s5 = peg$parsefunction();
        if (s5 !== peg$FAILED) {
          s4 = [s4, s5];
          s3 = s4;
        } else {
          peg$currPos = s3;
          s3 = peg$FAILED;
        }
      }
      peg$savedPos = s0;
      s0 = peg$f1(s1, s2);
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e3);
      }
    }
    return s0;
  }
  function peg$parsefunction() {
    var s0, s1;
    peg$silentFails++;
    s0 = peg$parsegrayscale();
    if (s0 === peg$FAILED) {
      s0 = peg$parsesepia();
      if (s0 === peg$FAILED) {
        s0 = peg$parsesaturate();
        if (s0 === peg$FAILED) {
          s0 = peg$parsehueRotate();
          if (s0 === peg$FAILED) {
            s0 = peg$parseinvert();
            if (s0 === peg$FAILED) {
              s0 = peg$parseopacity();
              if (s0 === peg$FAILED) {
                s0 = peg$parsebrightness();
                if (s0 === peg$FAILED) {
                  s0 = peg$parsecontrast();
                  if (s0 === peg$FAILED) {
                    s0 = peg$parseblur();
                    if (s0 === peg$FAILED) {
                      s0 = peg$parsedropShadow();
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e4);
      }
    }
    return s0;
  }
  function peg$parsegrayscale() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 10) === peg$c1) {
      s2 = peg$c1;
      peg$currPos += 10;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e6);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parsenumberPercentage();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s6 = peg$c2;
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s6 !== peg$FAILED) {
          s7 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f2(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e5);
      }
    }
    return s0;
  }
  function peg$parsesepia() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 6) === peg$c3) {
      s2 = peg$c3;
      peg$currPos += 6;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e9);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parsenumberPercentage();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s6 = peg$c2;
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s6 !== peg$FAILED) {
          s7 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f3(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e8);
      }
    }
    return s0;
  }
  function peg$parsesaturate() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 9) === peg$c4) {
      s2 = peg$c4;
      peg$currPos += 9;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e11);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parsenumberPercentage();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s6 = peg$c2;
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s6 !== peg$FAILED) {
          s7 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f4(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e10);
      }
    }
    return s0;
  }
  function peg$parsehueRotate() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 11) === peg$c5) {
      s2 = peg$c5;
      peg$currPos += 11;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e13);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parseangleZero();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s6 = peg$c2;
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s6 !== peg$FAILED) {
          s7 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f5(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e12);
      }
    }
    return s0;
  }
  function peg$parseinvert() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 7) === peg$c6) {
      s2 = peg$c6;
      peg$currPos += 7;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e15);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parsenumberPercentage();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s6 = peg$c2;
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s6 !== peg$FAILED) {
          s7 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f6(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e14);
      }
    }
    return s0;
  }
  function peg$parseopacity() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 8) === peg$c7) {
      s2 = peg$c7;
      peg$currPos += 8;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e17);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parsenumberPercentage();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s6 = peg$c2;
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s6 !== peg$FAILED) {
          s7 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f7(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e16);
      }
    }
    return s0;
  }
  function peg$parsebrightness() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 11) === peg$c8) {
      s2 = peg$c8;
      peg$currPos += 11;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e19);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parsenumberPercentage();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s6 = peg$c2;
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s6 !== peg$FAILED) {
          s7 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f8(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e18);
      }
    }
    return s0;
  }
  function peg$parsecontrast() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 9) === peg$c9) {
      s2 = peg$c9;
      peg$currPos += 9;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e21);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parsenumberPercentage();
      if (s4 !== peg$FAILED) {
        s5 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s6 = peg$c2;
          peg$currPos++;
        } else {
          s6 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s6 !== peg$FAILED) {
          s7 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f9(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e20);
      }
    }
    return s0;
  }
  function peg$parseblur() {
    var s0, s1, s2, s3, s4, s5, s6, s7, s8;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 5) === peg$c10) {
      s2 = peg$c10;
      peg$currPos += 5;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e23);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parseNUM();
      if (s4 !== peg$FAILED) {
        s5 = peg$parseIDENTIFIER();
        if (s5 === peg$FAILED) {
          s5 = null;
        }
        s6 = peg$parse_();
        if (input.charCodeAt(peg$currPos) === 41) {
          s7 = peg$c2;
          peg$currPos++;
        } else {
          s7 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e7);
          }
        }
        if (s7 !== peg$FAILED) {
          s8 = peg$parse_();
          peg$savedPos = s0;
          s0 = peg$f10(s4);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e22);
      }
    }
    return s0;
  }
  function peg$parsedropShadow() {
    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15, s16;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parse_();
    if (input.substr(peg$currPos, 12) === peg$c11) {
      s2 = peg$c11;
      peg$currPos += 12;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e25);
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$parse_();
      s4 = peg$parseNUM();
      if (s4 !== peg$FAILED) {
        s5 = peg$parseIDENTIFIER();
        if (s5 === peg$FAILED) {
          s5 = null;
        }
        s6 = peg$parse_();
        s7 = peg$parseNUM();
        if (s7 !== peg$FAILED) {
          s8 = peg$parseIDENTIFIER();
          if (s8 === peg$FAILED) {
            s8 = null;
          }
          s9 = peg$parse_();
          s10 = peg$parseNUM();
          if (s10 === peg$FAILED) {
            s10 = null;
          }
          s11 = peg$parseIDENTIFIER();
          if (s11 === peg$FAILED) {
            s11 = null;
          }
          s12 = peg$parse_();
          s13 = peg$parseCOLOR();
          if (s13 === peg$FAILED) {
            s13 = null;
          }
          s14 = peg$parse_();
          if (input.charCodeAt(peg$currPos) === 41) {
            s15 = peg$c2;
            peg$currPos++;
          } else {
            s15 = peg$FAILED;
            if (peg$silentFails === 0) {
              peg$fail(peg$e7);
            }
          }
          if (s15 !== peg$FAILED) {
            s16 = peg$parse_();
            peg$savedPos = s0;
            s0 = peg$f11(s4, s7, s10, s13);
          } else {
            peg$currPos = s0;
            s0 = peg$FAILED;
          }
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = peg$parse_();
      if (input.substr(peg$currPos, 12) === peg$c11) {
        s2 = peg$c11;
        peg$currPos += 12;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e25);
        }
      }
      if (s2 !== peg$FAILED) {
        s3 = peg$parse_();
        s4 = peg$parseCOLOR();
        if (s4 !== peg$FAILED) {
          s5 = peg$parse_();
          s6 = peg$parseNUM();
          if (s6 !== peg$FAILED) {
            s7 = peg$parseIDENTIFIER();
            if (s7 === peg$FAILED) {
              s7 = null;
            }
            s8 = peg$parse_();
            s9 = peg$parseNUM();
            if (s9 !== peg$FAILED) {
              s10 = peg$parseIDENTIFIER();
              if (s10 === peg$FAILED) {
                s10 = null;
              }
              s11 = peg$parse_();
              s12 = peg$parseNUM();
              if (s12 === peg$FAILED) {
                s12 = null;
              }
              s13 = peg$parseIDENTIFIER();
              if (s13 === peg$FAILED) {
                s13 = null;
              }
              s14 = peg$parse_();
              if (input.charCodeAt(peg$currPos) === 41) {
                s15 = peg$c2;
                peg$currPos++;
              } else {
                s15 = peg$FAILED;
                if (peg$silentFails === 0) {
                  peg$fail(peg$e7);
                }
              }
              if (s15 !== peg$FAILED) {
                s16 = peg$parse_();
                peg$savedPos = s0;
                s0 = peg$f12(s4, s6, s9, s12);
              } else {
                peg$currPos = s0;
                s0 = peg$FAILED;
              }
            } else {
              peg$currPos = s0;
              s0 = peg$FAILED;
            }
          } else {
            peg$currPos = s0;
            s0 = peg$FAILED;
          }
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e24);
      }
    }
    return s0;
  }
  function peg$parsenumberPercentage() {
    var s0, s1, s2;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parseNUM();
    if (s1 !== peg$FAILED) {
      if (input.charCodeAt(peg$currPos) === 37) {
        s2 = peg$c12;
        peg$currPos++;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e27);
        }
      }
      if (s2 !== peg$FAILED) {
        peg$savedPos = s0;
        s0 = peg$f13(s1);
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    if (s0 === peg$FAILED) {
      s0 = peg$parseNUM();
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e26);
      }
    }
    return s0;
  }
  function peg$parseangleZero() {
    var s0, s1;
    peg$silentFails++;
    s0 = peg$parseangle();
    if (s0 === peg$FAILED) {
      s0 = peg$parsezero();
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e28);
      }
    }
    return s0;
  }
  function peg$parseangle() {
    var s0, s1, s2;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = peg$parseNUM();
    if (s1 !== peg$FAILED) {
      if (input.substr(peg$currPos, 3) === peg$c13) {
        s2 = peg$c13;
        peg$currPos += 3;
      } else {
        s2 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e30);
        }
      }
      if (s2 === peg$FAILED) {
        s2 = null;
      }
      peg$savedPos = s0;
      s0 = peg$f14(s1);
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      s1 = peg$parseNUM();
      if (s1 !== peg$FAILED) {
        if (input.substr(peg$currPos, 4) === peg$c14) {
          s2 = peg$c14;
          peg$currPos += 4;
        } else {
          s2 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e31);
          }
        }
        if (s2 !== peg$FAILED) {
          peg$savedPos = s0;
          s0 = peg$f15(s1);
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
      if (s0 === peg$FAILED) {
        s0 = peg$currPos;
        s1 = peg$parseNUM();
        if (s1 !== peg$FAILED) {
          if (input.substr(peg$currPos, 3) === peg$c15) {
            s2 = peg$c15;
            peg$currPos += 3;
          } else {
            s2 = peg$FAILED;
            if (peg$silentFails === 0) {
              peg$fail(peg$e32);
            }
          }
          if (s2 !== peg$FAILED) {
            peg$savedPos = s0;
            s0 = peg$f16(s1);
          } else {
            peg$currPos = s0;
            s0 = peg$FAILED;
          }
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
        if (s0 === peg$FAILED) {
          s0 = peg$currPos;
          s1 = peg$parseNUM();
          if (s1 !== peg$FAILED) {
            if (input.substr(peg$currPos, 4) === peg$c16) {
              s2 = peg$c16;
              peg$currPos += 4;
            } else {
              s2 = peg$FAILED;
              if (peg$silentFails === 0) {
                peg$fail(peg$e33);
              }
            }
            if (s2 !== peg$FAILED) {
              peg$savedPos = s0;
              s0 = peg$f17(s1);
            } else {
              peg$currPos = s0;
              s0 = peg$FAILED;
            }
          } else {
            peg$currPos = s0;
            s0 = peg$FAILED;
          }
        }
      }
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e29);
      }
    }
    return s0;
  }
  function peg$parsezero() {
    var s0, s1;
    peg$silentFails++;
    if (input.charCodeAt(peg$currPos) === 48) {
      s0 = peg$c17;
      peg$currPos++;
    } else {
      s0 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e35);
      }
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e34);
      }
    }
    return s0;
  }
  function peg$parse_() {
    var s0, s1;
    peg$silentFails++;
    s0 = [];
    s1 = input.charAt(peg$currPos);
    if (peg$r0.test(s1)) {
      peg$currPos++;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e37);
      }
    }
    while (s1 !== peg$FAILED) {
      s0.push(s1);
      s1 = input.charAt(peg$currPos);
      if (peg$r0.test(s1)) {
        peg$currPos++;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e37);
        }
      }
    }
    peg$silentFails--;
    s1 = peg$FAILED;
    if (peg$silentFails === 0) {
      peg$fail(peg$e36);
    }
    return s0;
  }
  function peg$parseIDENTIFIER() {
    var s0, s1, s2;
    peg$silentFails++;
    s0 = peg$currPos;
    s1 = [];
    s2 = input.charAt(peg$currPos);
    if (peg$r1.test(s2)) {
      peg$currPos++;
    } else {
      s2 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e39);
      }
    }
    if (s2 !== peg$FAILED) {
      while (s2 !== peg$FAILED) {
        s1.push(s2);
        s2 = input.charAt(peg$currPos);
        if (peg$r1.test(s2)) {
          peg$currPos++;
        } else {
          s2 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e39);
          }
        }
      }
    } else {
      s1 = peg$FAILED;
    }
    if (s1 !== peg$FAILED) {
      peg$savedPos = s0;
      s1 = peg$f18();
    }
    s0 = s1;
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e38);
      }
    }
    return s0;
  }
  function peg$parseCOLOR() {
    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15, s16, s17;
    peg$silentFails++;
    s0 = peg$currPos;
    if (input.charCodeAt(peg$currPos) === 35) {
      s1 = peg$c18;
      peg$currPos++;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e41);
      }
    }
    if (s1 !== peg$FAILED) {
      s2 = [];
      s3 = input.charAt(peg$currPos);
      if (peg$r2.test(s3)) {
        peg$currPos++;
      } else {
        s3 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e42);
        }
      }
      if (s3 !== peg$FAILED) {
        while (s3 !== peg$FAILED) {
          s2.push(s3);
          s3 = input.charAt(peg$currPos);
          if (peg$r2.test(s3)) {
            peg$currPos++;
          } else {
            s3 = peg$FAILED;
            if (peg$silentFails === 0) {
              peg$fail(peg$e42);
            }
          }
        }
      } else {
        s2 = peg$FAILED;
      }
      if (s2 !== peg$FAILED) {
        peg$savedPos = s0;
        s0 = peg$f19();
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    if (s0 === peg$FAILED) {
      s0 = peg$currPos;
      if (input.substr(peg$currPos, 4) === peg$c19) {
        s1 = peg$c19;
        peg$currPos += 4;
      } else {
        s1 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e43);
        }
      }
      if (s1 !== peg$FAILED) {
        s2 = peg$parse_();
        s3 = peg$parseNUM();
        if (s3 !== peg$FAILED) {
          s4 = peg$parse_();
          if (input.charCodeAt(peg$currPos) === 44) {
            s5 = peg$c20;
            peg$currPos++;
          } else {
            s5 = peg$FAILED;
            if (peg$silentFails === 0) {
              peg$fail(peg$e44);
            }
          }
          if (s5 !== peg$FAILED) {
            s6 = peg$parse_();
            s7 = peg$parseNUM();
            if (s7 !== peg$FAILED) {
              s8 = peg$parse_();
              if (input.charCodeAt(peg$currPos) === 44) {
                s9 = peg$c20;
                peg$currPos++;
              } else {
                s9 = peg$FAILED;
                if (peg$silentFails === 0) {
                  peg$fail(peg$e44);
                }
              }
              if (s9 !== peg$FAILED) {
                s10 = peg$parse_();
                s11 = peg$parseNUM();
                if (s11 !== peg$FAILED) {
                  s12 = peg$parse_();
                  if (input.charCodeAt(peg$currPos) === 41) {
                    s13 = peg$c2;
                    peg$currPos++;
                  } else {
                    s13 = peg$FAILED;
                    if (peg$silentFails === 0) {
                      peg$fail(peg$e7);
                    }
                  }
                  if (s13 !== peg$FAILED) {
                    peg$savedPos = s0;
                    s0 = peg$f20(s3, s7, s11);
                  } else {
                    peg$currPos = s0;
                    s0 = peg$FAILED;
                  }
                } else {
                  peg$currPos = s0;
                  s0 = peg$FAILED;
                }
              } else {
                peg$currPos = s0;
                s0 = peg$FAILED;
              }
            } else {
              peg$currPos = s0;
              s0 = peg$FAILED;
            }
          } else {
            peg$currPos = s0;
            s0 = peg$FAILED;
          }
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
      } else {
        peg$currPos = s0;
        s0 = peg$FAILED;
      }
      if (s0 === peg$FAILED) {
        s0 = peg$currPos;
        if (input.substr(peg$currPos, 5) === peg$c21) {
          s1 = peg$c21;
          peg$currPos += 5;
        } else {
          s1 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e45);
          }
        }
        if (s1 !== peg$FAILED) {
          s2 = peg$parse_();
          s3 = peg$parseNUM();
          if (s3 !== peg$FAILED) {
            s4 = peg$parse_();
            if (input.charCodeAt(peg$currPos) === 44) {
              s5 = peg$c20;
              peg$currPos++;
            } else {
              s5 = peg$FAILED;
              if (peg$silentFails === 0) {
                peg$fail(peg$e44);
              }
            }
            if (s5 !== peg$FAILED) {
              s6 = peg$parse_();
              s7 = peg$parseNUM();
              if (s7 !== peg$FAILED) {
                s8 = peg$parse_();
                if (input.charCodeAt(peg$currPos) === 44) {
                  s9 = peg$c20;
                  peg$currPos++;
                } else {
                  s9 = peg$FAILED;
                  if (peg$silentFails === 0) {
                    peg$fail(peg$e44);
                  }
                }
                if (s9 !== peg$FAILED) {
                  s10 = peg$parse_();
                  s11 = peg$parseNUM();
                  if (s11 !== peg$FAILED) {
                    s12 = peg$parse_();
                    if (input.charCodeAt(peg$currPos) === 44) {
                      s13 = peg$c20;
                      peg$currPos++;
                    } else {
                      s13 = peg$FAILED;
                      if (peg$silentFails === 0) {
                        peg$fail(peg$e44);
                      }
                    }
                    if (s13 !== peg$FAILED) {
                      s14 = peg$parse_();
                      s15 = peg$parseNUM();
                      if (s15 !== peg$FAILED) {
                        s16 = peg$parse_();
                        if (input.charCodeAt(peg$currPos) === 41) {
                          s17 = peg$c2;
                          peg$currPos++;
                        } else {
                          s17 = peg$FAILED;
                          if (peg$silentFails === 0) {
                            peg$fail(peg$e7);
                          }
                        }
                        if (s17 !== peg$FAILED) {
                          peg$savedPos = s0;
                          s0 = peg$f21(s3, s7, s11, s15);
                        } else {
                          peg$currPos = s0;
                          s0 = peg$FAILED;
                        }
                      } else {
                        peg$currPos = s0;
                        s0 = peg$FAILED;
                      }
                    } else {
                      peg$currPos = s0;
                      s0 = peg$FAILED;
                    }
                  } else {
                    peg$currPos = s0;
                    s0 = peg$FAILED;
                  }
                } else {
                  peg$currPos = s0;
                  s0 = peg$FAILED;
                }
              } else {
                peg$currPos = s0;
                s0 = peg$FAILED;
              }
            } else {
              peg$currPos = s0;
              s0 = peg$FAILED;
            }
          } else {
            peg$currPos = s0;
            s0 = peg$FAILED;
          }
        } else {
          peg$currPos = s0;
          s0 = peg$FAILED;
        }
        if (s0 === peg$FAILED) {
          s0 = peg$currPos;
          s1 = peg$parseIDENTIFIER();
          if (s1 !== peg$FAILED) {
            peg$savedPos = s0;
            s1 = peg$f22();
          }
          s0 = s1;
        }
      }
    }
    peg$silentFails--;
    if (s0 === peg$FAILED) {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e40);
      }
    }
    return s0;
  }
  function peg$parseNUM() {
    var s0, s1, s2, s3, s4, s5, s6, s7;
    s0 = peg$currPos;
    s1 = input.charAt(peg$currPos);
    if (peg$r3.test(s1)) {
      peg$currPos++;
    } else {
      s1 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e46);
      }
    }
    if (s1 === peg$FAILED) {
      s1 = null;
    }
    s2 = peg$currPos;
    s3 = [];
    s4 = input.charAt(peg$currPos);
    if (peg$r4.test(s4)) {
      peg$currPos++;
    } else {
      s4 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e47);
      }
    }
    while (s4 !== peg$FAILED) {
      s3.push(s4);
      s4 = input.charAt(peg$currPos);
      if (peg$r4.test(s4)) {
        peg$currPos++;
      } else {
        s4 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e47);
        }
      }
    }
    if (input.charCodeAt(peg$currPos) === 46) {
      s4 = peg$c22;
      peg$currPos++;
    } else {
      s4 = peg$FAILED;
      if (peg$silentFails === 0) {
        peg$fail(peg$e48);
      }
    }
    if (s4 !== peg$FAILED) {
      s5 = [];
      s6 = input.charAt(peg$currPos);
      if (peg$r4.test(s6)) {
        peg$currPos++;
      } else {
        s6 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e47);
        }
      }
      if (s6 !== peg$FAILED) {
        while (s6 !== peg$FAILED) {
          s5.push(s6);
          s6 = input.charAt(peg$currPos);
          if (peg$r4.test(s6)) {
            peg$currPos++;
          } else {
            s6 = peg$FAILED;
            if (peg$silentFails === 0) {
              peg$fail(peg$e47);
            }
          }
        }
      } else {
        s5 = peg$FAILED;
      }
      if (s5 !== peg$FAILED) {
        s3 = [s3, s4, s5];
        s2 = s3;
      } else {
        peg$currPos = s2;
        s2 = peg$FAILED;
      }
    } else {
      peg$currPos = s2;
      s2 = peg$FAILED;
    }
    if (s2 === peg$FAILED) {
      s2 = [];
      s3 = input.charAt(peg$currPos);
      if (peg$r4.test(s3)) {
        peg$currPos++;
      } else {
        s3 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e47);
        }
      }
      if (s3 !== peg$FAILED) {
        while (s3 !== peg$FAILED) {
          s2.push(s3);
          s3 = input.charAt(peg$currPos);
          if (peg$r4.test(s3)) {
            peg$currPos++;
          } else {
            s3 = peg$FAILED;
            if (peg$silentFails === 0) {
              peg$fail(peg$e47);
            }
          }
        }
      } else {
        s2 = peg$FAILED;
      }
    }
    if (s2 !== peg$FAILED) {
      s3 = peg$currPos;
      if (input.charCodeAt(peg$currPos) === 101) {
        s4 = peg$c23;
        peg$currPos++;
      } else {
        s4 = peg$FAILED;
        if (peg$silentFails === 0) {
          peg$fail(peg$e49);
        }
      }
      if (s4 !== peg$FAILED) {
        s5 = input.charAt(peg$currPos);
        if (peg$r3.test(s5)) {
          peg$currPos++;
        } else {
          s5 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e46);
          }
        }
        if (s5 === peg$FAILED) {
          s5 = null;
        }
        s6 = [];
        s7 = input.charAt(peg$currPos);
        if (peg$r4.test(s7)) {
          peg$currPos++;
        } else {
          s7 = peg$FAILED;
          if (peg$silentFails === 0) {
            peg$fail(peg$e47);
          }
        }
        if (s7 !== peg$FAILED) {
          while (s7 !== peg$FAILED) {
            s6.push(s7);
            s7 = input.charAt(peg$currPos);
            if (peg$r4.test(s7)) {
              peg$currPos++;
            } else {
              s7 = peg$FAILED;
              if (peg$silentFails === 0) {
                peg$fail(peg$e47);
              }
            }
          }
        } else {
          s6 = peg$FAILED;
        }
        if (s6 !== peg$FAILED) {
          s4 = [s4, s5, s6];
          s3 = s4;
        } else {
          peg$currPos = s3;
          s3 = peg$FAILED;
        }
      } else {
        peg$currPos = s3;
        s3 = peg$FAILED;
      }
      if (s3 === peg$FAILED) {
        s3 = null;
      }
      peg$savedPos = s0;
      s0 = peg$f23();
    } else {
      peg$currPos = s0;
      s0 = peg$FAILED;
    }
    return s0;
  }
  peg$result = peg$startRuleFunction();
  if (options.peg$library) {
    return /** @type {any} */{
      peg$result,
      peg$currPos,
      peg$FAILED,
      peg$maxFailExpected,
      peg$maxFailPos
    };
  }
  if (peg$result !== peg$FAILED && peg$currPos === input.length) {
    return peg$result;
  } else {
    if (peg$result !== peg$FAILED && peg$currPos < input.length) {
      peg$fail(peg$endExpectation());
    }
    throw peg$buildStructuredError(peg$maxFailExpected, peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null, peg$maxFailPos < input.length ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1) : peg$computeLocation(peg$maxFailPos, peg$maxFailPos));
  }
}
module.exports = {
  StartRules: ["start"],
  SyntaxError: peg$SyntaxError,
  parse: peg$parse
};
//# sourceMappingURL=extractFiltersString.js.map