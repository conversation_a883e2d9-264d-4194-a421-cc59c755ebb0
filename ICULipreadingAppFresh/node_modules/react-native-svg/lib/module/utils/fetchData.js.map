{"version": 3, "names": ["Platform", "<PERSON><PERSON><PERSON>", "fetchText", "uri", "startsWith", "OS", "dataUriToXml", "decodeBase64Image", "fetchUriData", "decoded", "decodeURIComponent", "splitContent", "split", "dataType", "content", "slice", "join", "from", "toString", "error", "Error", "response", "fetch", "ok", "status", "text"], "sourceRoot": "../../../src", "sources": ["utils/fetchData.ts"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,MAAM,QAAQ,QAAQ;AAE/B,OAAO,eAAeC,SAASA,CAACC,GAAY,EAA0B;EACpE,IAAI,CAACA,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAIA,GAAG,CAACC,UAAU,CAAC,yBAAyB,CAAC,IAAIJ,QAAQ,CAACK,EAAE,KAAK,SAAS,EAAE;IAC1E,OAAOC,YAAY,CAACH,GAAG,CAAC;EAC1B,CAAC,MAAM,IAAIA,GAAG,CAACC,UAAU,CAAC,2BAA2B,CAAC,EAAE;IACtD,OAAOG,iBAAiB,CAACJ,GAAG,CAAC;EAC/B,CAAC,MAAM;IACL,OAAOK,YAAY,CAACL,GAAG,CAAC;EAC1B;AACF;AAEA,MAAMI,iBAAiB,GAAIJ,GAAW,IAAK;EACzC,MAAMM,OAAO,GAAGC,kBAAkB,CAACP,GAAG,CAAC;EACvC,MAAMQ,YAAY,GAAGF,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC;EACrD,MAAMC,QAAQ,GAAGF,YAAY,CAAC,CAAC,CAAmB;EAClD,MAAMG,OAAO,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAE/C,OAAOf,MAAM,CAACgB,IAAI,CAACH,OAAO,EAAED,QAAQ,CAAC,CAACK,QAAQ,CAAC,OAAO,CAAC;AACzD,CAAC;AAED,SAASZ,YAAYA,CAACH,GAAW,EAAiB;EAChD,IAAI;IACF;IACA,OAAOO,kBAAkB,CAACP,GAAG,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC9D,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,MAAM,IAAIC,KAAK,CAAC,YAAYjB,GAAG,uBAAuBgB,KAAK,EAAE,CAAC;EAChE;AACF;AAEA,eAAeX,YAAYA,CAACL,GAAW,EAAE;EACvC,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAACnB,GAAG,CAAC;EACjC,IAAIkB,QAAQ,CAACE,EAAE,IAAKF,QAAQ,CAACG,MAAM,KAAK,CAAC,IAAIrB,GAAG,CAACC,UAAU,CAAC,SAAS,CAAE,EAAE;IACvE,OAAO,MAAMiB,QAAQ,CAACI,IAAI,CAAC,CAAC;EAC9B;EACA,MAAM,IAAIL,KAAK,CAAC,YAAYjB,GAAG,uBAAuBkB,QAAQ,CAACG,MAAM,EAAE,CAAC;AAC1E", "ignoreList": []}