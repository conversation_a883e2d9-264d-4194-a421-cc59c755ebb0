{"version": 3, "file": "legacy.d.ts", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["legacy.ts"], "names": [], "mappings": "AAAA,OAAO,EAAiB,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC7D,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAKlD;;;;;;GAMG;AACH,MAAM,WAAW,eAAe;IAC5B,QAAQ,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;IAChD,QAAQ,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;IAChD,YAAY,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;IACrD,CAAC,aAAa,EAAE,MAAM,GAChB,SAAS,GACT,MAAM,GACN,CAAC,CAAC,cAAc,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC;CAC/C;AAkFD;;;;;;;GAOG;AACH,wBAAgB,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAG5E;AAED;;;;;;;;;GASG;AACH,wBAAgB,WAAW,CACvB,OAAO,EAAE,eAAe,EACxB,KAAK,EAAE,OAAO,GAAG,OAAO,EAAE,EAC1B,OAAO,EAAE,OAAO,EAChB,KAAK,GAAE,MAAiB,GACzB,OAAO,EAAE,CAGX;AAED;;;;;;;;GAQG;AACH,wBAAgB,cAAc,CAC1B,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,MAAM,KAAK,OAAO,CAAC,EACtC,KAAK,EAAE,OAAO,GAAG,OAAO,EAAE,EAC1B,OAAO,UAAO,GACf,OAAO,GAAG,IAAI,CAGhB;AAED;;;;;;;;;GASG;AACH,wBAAgB,oBAAoB,CAChC,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,EAC7C,KAAK,EAAE,OAAO,GAAG,OAAO,EAAE,EAC1B,OAAO,UAAO,EACd,KAAK,GAAE,MAAiB,GACzB,OAAO,EAAE,CAOX;AAED;;;;;;;;;GASG;AACH,wBAAgB,sBAAsB,CAClC,SAAS,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,EAC/C,KAAK,EAAE,OAAO,GAAG,OAAO,EAAE,EAC1B,OAAO,UAAO,EACd,KAAK,GAAE,MAAiB,GACzB,OAAO,EAAE,CAOX;AAED;;;;;;;;;GASG;AACH,wBAAgB,oBAAoB,CAChC,IAAI,EAAE,WAAW,GAAG,CAAC,CAAC,IAAI,EAAE,WAAW,KAAK,OAAO,CAAC,EACpD,KAAK,EAAE,OAAO,GAAG,OAAO,EAAE,EAC1B,OAAO,UAAO,EACd,KAAK,GAAE,MAAiB,GACzB,OAAO,EAAE,CAEX"}