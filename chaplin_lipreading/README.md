# 🧠 Chaplin Lipreading - Local ICU Communication

A complete local setup for webcam-based lipreading using the Chaplin model, specifically designed for ICU patient communication.

## 🎯 Features

- **Local webcam recording** with adjustable duration (1-5 seconds)
- **Real-time lipreading** using Chaplin transformer model
- **ICU phrase mapping** with intelligent similarity matching
- **Web interface** via Streamlit (no internet required)
- **CLI mode** for quick testing
- **Cross-platform** support (macOS/Linux)

## 🏥 Supported ICU Phrases

1. "I'm in pain"
2. "can i have some water?"
3. "my feet are cold"
4. "I feel cold"
5. "I feel hot"
6. "can you call my wife please?"
7. "can you put the back of the bed down a little bit?"

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Clone or download this directory
cd chaplin_lipreading

# Run setup (installs everything)
make setup
```

### 2. Download Model

```bash
# Download pretrained Chaplin model
make model
```

### 3. Start Web Interface

```bash
# Launch Streamlit app
make live
```

Then open **http://localhost:8501** in your browser.

### 4. CLI Testing (Optional)

```bash
# Quick CLI test
make test
```

## 📁 Project Structure

```
chaplin_lipreading/
├── setup.sh              # Environment setup script
├── Makefile              # Build commands
├── streamlit_app.py      # Web interface
├── chaplin_live.py       # CLI interface
├── chaplin_inference.py  # Model inference engine
├── icu_phrases.py        # ICU phrase mapping
├── download_model.py     # Model downloader
├── requirements.txt      # Python dependencies
├── chaplin_repo/         # Cloned Chaplin repository
├── chaplin_models/       # Downloaded model files
├── outputs/              # Saved results
└── temp/                 # Temporary files
```

## 🔧 Commands

| Command | Description |
|---------|-------------|
| `make setup` | Set up environment and dependencies |
| `make model` | Download pretrained model |
| `make live` | Start Streamlit web app |
| `make test` | Run CLI webcam test |
| `make clean` | Clean temporary files |
| `make help` | Show available commands |

## 🌐 Web Interface Usage

1. **Open** http://localhost:8501
2. **Adjust** recording duration (1-5 seconds)
3. **Click** "Start Recording"
4. **Wait** for 3-second countdown
5. **Mouth** an ICU phrase clearly
6. **Click** "Run Lipreading"
7. **View** results and similarity scores

## 💻 CLI Usage

```bash
# Record 3 seconds and predict
python chaplin_live.py

# Custom duration
python chaplin_live.py --duration 4.0

# Use existing video file
python chaplin_live.py --no-record --output my_video.mp4
```

## 🔍 How It Works

### 1. Video Preprocessing
- Captures webcam video at 25 FPS
- Converts to grayscale
- Resizes to 224x224 pixels
- Normalizes pixel values
- Pads/truncates to 75 frames

### 2. Model Inference
- Uses Chaplin transformer architecture
- Processes video through 3D CNN + transformer
- Outputs character-level predictions
- Decodes to readable text

### 3. ICU Phrase Mapping
- **String similarity** using SequenceMatcher
- **TF-IDF cosine similarity** for semantic matching
- **Keyword matching** for medical terms
- **Combined scoring** with weighted average

## 🛠️ Troubleshooting

### Webcam Issues
```bash
# Check camera permissions
ls /dev/video*

# Test camera access
python -c "import cv2; cap = cv2.VideoCapture(0); print('Camera OK' if cap.isOpened() else 'Camera Error')"
```

### Model Loading Issues
```bash
# Check model files
ls -la chaplin_models/

# Re-download model
make model
```

### Dependencies Issues
```bash
# Reinstall dependencies
source .venv/bin/activate
pip install -r requirements.txt
```

## 📊 Performance

- **CPU inference**: ~2-5 seconds per video
- **GPU inference**: ~0.5-1 second per video (if available)
- **Memory usage**: ~1-2GB RAM
- **Video quality**: 640x480 @ 25 FPS

## 🔬 Technical Details

### Model Architecture
- **Base**: Chaplin transformer model
- **Input**: 75 frames × 224×224 grayscale
- **Output**: Character-level predictions
- **Vocabulary**: 41 characters (a-z, space, apostrophe, special tokens)

### Similarity Scoring
- **String**: Direct character matching
- **TF-IDF**: Semantic word similarity
- **Keyword**: Medical term matching
- **Combined**: Weighted average (40% + 30% + 30%)

## 📝 License

This project uses the Chaplin model from https://github.com/amanvirparhar/chaplin

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review the logs in the terminal
3. Ensure all dependencies are installed
4. Verify webcam permissions

---

**🎯 Ready to start lipreading? Run `make setup && make model && make live`**
