# 🧠 Chaplin Lipreading Makefile

.PHONY: setup model live test clean help

# Default target
help:
	@echo "🧠 Chaplin Lipreading Commands:"
	@echo ""
	@echo "  make setup    - Set up environment and dependencies"
	@echo "  make model    - Download pretrained model"
	@echo "  make live     - Start Streamlit web app"
	@echo "  make test     - Run CLI webcam test"
	@echo "  make clean    - Clean temporary files"
	@echo "  make help     - Show this help"
	@echo ""

# Set up environment
setup:
	@echo "🚀 Setting up Chaplin environment..."
	chmod +x setup.sh
	./setup.sh

# Download pretrained model
model:
	@echo "📥 Downloading pretrained model..."
	@if [ ! -f "chaplin_models/LRS3_V_WER19.1.pth" ]; then \
		echo "Downloading LRS3_V_WER19.1 model..."; \
		source .venv/bin/activate && python download_model.py; \
	else \
		echo "✅ Model already exists"; \
	fi

# Start Streamlit app
live:
	@echo "🌐 Starting Streamlit app..."
	@echo "Opening http://localhost:8501"
	source .venv/bin/activate && streamlit run streamlit_app.py --server.port 8501

# Run CLI test
test:
	@echo "🎥 Running CLI webcam test..."
	source .venv/bin/activate && python chaplin_live.py

# Clean temporary files
clean:
	@echo "🧹 Cleaning temporary files..."
	rm -rf temp/*
	rm -rf outputs/*.mp4
	rm -f temp_record.mp4

# Install dependencies only
deps:
	@echo "📚 Installing dependencies..."
	source .venv/bin/activate && pip install -r requirements.txt
