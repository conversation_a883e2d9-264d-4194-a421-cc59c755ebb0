#!/usr/bin/env python3

"""
🌐 Chaplin Streamlit Web App
Local web interface for webcam lipreading using Chaplin model
"""

import streamlit as st
import cv2
import time
import tempfile
import os
from datetime import datetime
from pathlib import Path
import numpy as np
from PIL import Image

# Import our modules
from chaplin_inference import ChaplinInference
from icu_phrases import ICUPhraseMapper

# Page configuration
st.set_page_config(
    page_title="🧠 Chaplin Lipreading",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-box {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .status-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .status-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
    .status-error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .phrase-box {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

def add_lip_guide(frame):
    """Add lip positioning guide overlay to frame"""
    height, width = frame.shape[:2]

    # Calculate guide box dimensions (centered on face area)
    guide_width = int(width * 0.3)  # 30% of frame width
    guide_height = int(height * 0.2)  # 20% of frame height

    # Center the guide box
    center_x = width // 2
    center_y = int(height * 0.6)  # Slightly below center for mouth area

    # Calculate box corners
    x1 = center_x - guide_width // 2
    y1 = center_y - guide_height // 2
    x2 = center_x + guide_width // 2
    y2 = center_y + guide_height // 2

    # Draw main guide rectangle
    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)  # Green box

    # Draw corner markers
    corner_size = 20
    # Top-left corner
    cv2.line(frame, (x1, y1), (x1 + corner_size, y1), (0, 255, 0), 3)
    cv2.line(frame, (x1, y1), (x1, y1 + corner_size), (0, 255, 0), 3)

    # Top-right corner
    cv2.line(frame, (x2, y1), (x2 - corner_size, y1), (0, 255, 0), 3)
    cv2.line(frame, (x2, y1), (x2, y1 + corner_size), (0, 255, 0), 3)

    # Bottom-left corner
    cv2.line(frame, (x1, y2), (x1 + corner_size, y2), (0, 255, 0), 3)
    cv2.line(frame, (x1, y2), (x1, y2 - corner_size), (0, 255, 0), 3)

    # Bottom-right corner
    cv2.line(frame, (x2, y2), (x2 - corner_size, y2), (0, 255, 0), 3)
    cv2.line(frame, (x2, y2), (x2, y2 - corner_size), (0, 255, 0), 3)

    # Add center crosshair for mouth positioning
    cv2.line(frame, (center_x - 15, center_y), (center_x + 15, center_y), (255, 0, 0), 2)  # Blue horizontal line
    cv2.line(frame, (center_x, center_y - 10), (center_x, center_y + 10), (255, 0, 0), 2)  # Blue vertical line

    # Add instruction text
    cv2.putText(frame, "Position your lips in the green box",
                (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    cv2.putText(frame, "Center mouth on blue crosshair",
                (x1, y2 + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

    return frame

@st.cache_resource
def load_models():
    """Load models with caching"""
    try:
        chaplin = ChaplinInference()
        icu_mapper = ICUPhraseMapper()
        return chaplin, icu_mapper, True
    except Exception as e:
        st.error(f"❌ Error loading models: {e}")
        return None, None, False

def record_webcam_video(duration=3):
    """Record video from webcam"""
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp4')
        temp_path = temp_file.name
        temp_file.close()

        # Initialize webcam with better error handling
        st.info("🔍 Initializing camera...")
        cap = cv2.VideoCapture(0)

        # Try different camera backends if first fails
        if not cap.isOpened():
            st.warning("⚠️ Trying alternative camera access...")
            cap.release()
            cap = cv2.VideoCapture(0, cv2.CAP_AVFOUNDATION)  # macOS specific

        if not cap.isOpened():
            st.error("❌ Could not access webcam. Please check:")
            st.error("1. Camera permissions in System Preferences")
            st.error("2. No other apps are using the camera")
            st.error("3. Camera is properly connected")
            return None

        st.success("✅ Camera initialized successfully")
        
        # Set camera properties
        fps = 25
        cap.set(cv2.CAP_PROP_FPS, fps)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        # Get actual properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        actual_fps = cap.get(cv2.CAP_PROP_FPS)
        if actual_fps <= 0:
            actual_fps = fps
        
        # Set up video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_path, fourcc, actual_fps, (width, height))
        
        # Calculate total frames
        total_frames = int(duration * actual_fps)
        
        # Create placeholders for real-time updates
        status_placeholder = st.empty()
        video_placeholder = st.empty()
        progress_placeholder = st.empty()
        
        # Countdown
        for i in range(3, 0, -1):
            status_placeholder.markdown(f"<div class='status-box status-warning'>⏰ Starting in {i}...</div>", unsafe_allow_html=True)
            time.sleep(1)
        
        status_placeholder.markdown("<div class='status-box status-success'>🔴 RECORDING!</div>", unsafe_allow_html=True)
        
        frames_recorded = 0
        start_time = time.time()
        
        # Recording loop
        while frames_recorded < total_frames:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip frame horizontally for mirror effect
            frame = cv2.flip(frame, 1)
            
            # Add recording indicator
            cv2.circle(frame, (30, 30), 10, (0, 0, 255), -1)
            cv2.putText(frame, "REC", (50, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # Write frame
            out.write(frame)
            
            # Update progress
            progress = frames_recorded / total_frames
            progress_placeholder.progress(progress)
            
            # Add lip positioning guide overlay
            frame_with_guide = add_lip_guide(frame)

            # Show frame in Streamlit (convert BGR to RGB)
            frame_rgb = cv2.cvtColor(frame_with_guide, cv2.COLOR_BGR2RGB)
            video_placeholder.image(frame_rgb, channels="RGB", use_column_width=True)
            
            frames_recorded += 1
        
        # Cleanup
        cap.release()
        out.release()
        
        elapsed = time.time() - start_time
        status_placeholder.markdown(f"<div class='status-box status-success'>✅ Recording complete! {frames_recorded} frames in {elapsed:.1f}s</div>", unsafe_allow_html=True)
        
        return temp_path
        
    except Exception as e:
        st.error(f"❌ Recording error: {e}")
        return None

def main():
    """Main Streamlit app"""
    
    # Header
    st.markdown("<h1 class='main-header'>🧠 Chaplin Lipreading</h1>", unsafe_allow_html=True)
    st.markdown("<p style='text-align: center; color: #666;'>Local webcam lipreading for ICU communication</p>", unsafe_allow_html=True)
    
    # Sidebar
    st.sidebar.markdown("## ⚙️ Settings")
    
    # Recording duration slider
    duration = st.sidebar.slider(
        "📹 Recording Duration (seconds)",
        min_value=1.0,
        max_value=5.0,
        value=3.0,
        step=0.5
    )
    
    # Model status
    st.sidebar.markdown("## 🔧 Model Status")
    
    # Load models
    chaplin, icu_mapper, models_loaded = load_models()
    
    if models_loaded:
        st.sidebar.markdown("✅ Chaplin model loaded")
        st.sidebar.markdown("✅ ICU phrase mapper ready")
    else:
        st.sidebar.markdown("❌ Models not loaded")
        st.error("Please run 'make setup' and 'make model' first")
        return
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("## 🎥 Webcam Recorder")
        
        # Recording controls
        col_record, col_retry = st.columns(2)
        
        with col_record:
            if st.button("🎬 Start Recording", type="primary", use_container_width=True):
                st.session_state.recording = True
        
        with col_retry:
            if st.button("🔄 Retry Recording", use_container_width=True):
                if 'last_video_path' in st.session_state:
                    try:
                        os.unlink(st.session_state.last_video_path)
                    except:
                        pass
                st.session_state.recording = True
        
        # Recording area
        if st.session_state.get('recording', False):
            st.markdown("### 📹 Recording in progress...")
            
            video_path = record_webcam_video(duration)
            
            if video_path:
                st.session_state.last_video_path = video_path
                st.session_state.recording = False
                st.session_state.video_ready = True
                st.rerun()
        
        # Inference section
        if st.session_state.get('video_ready', False) and 'last_video_path' in st.session_state:
            st.markdown("## 🧠 Inference Results")
            
            if st.button("🚀 Run Lipreading", type="primary", use_container_width=True):
                with st.spinner("🧠 Processing video..."):
                    start_time = time.time()
                    
                    # Run inference
                    raw_prediction = chaplin.predict(st.session_state.last_video_path)
                    
                    # Map to ICU phrases
                    icu_result = icu_mapper.find_best_match(raw_prediction)
                    
                    inference_time = time.time() - start_time
                    
                    # Store results
                    st.session_state.results = {
                        'raw_prediction': raw_prediction,
                        'icu_result': icu_result,
                        'inference_time': inference_time
                    }
                    
                    st.rerun()
        
        # Display results
        if 'results' in st.session_state:
            results = st.session_state.results
            
            st.markdown("### 🎯 Results")
            
            # Raw prediction
            st.markdown(f"**📝 Raw output:** `{results['raw_prediction']}`")
            
            # ICU phrase match
            icu_phrase = results['icu_result']['phrase']
            similarity = results['icu_result']['similarity']
            
            if similarity > 0.7:
                status_class = "status-success"
                icon = "✅"
            elif similarity > 0.4:
                status_class = "status-warning"
                icon = "⚠️"
            else:
                status_class = "status-error"
                icon = "❌"
            
            st.markdown(f"""
            <div class='status-box {status_class}'>
                {icon} <strong>ICU Phrase:</strong> "{icu_phrase}"<br>
                <strong>Similarity:</strong> {similarity:.3f}<br>
                <strong>Processing time:</strong> {results['inference_time']:.2f}s
            </div>
            """, unsafe_allow_html=True)
            
            # Top matches
            if st.expander("🔍 View all matches"):
                top_matches = icu_mapper.get_top_matches(results['raw_prediction'])
                for i, match in enumerate(top_matches, 1):
                    st.write(f"{i}. \"{match['phrase']}\" (similarity: {match['similarity']:.3f})")
    
    with col2:
        st.markdown("## 🏥 Supported ICU Phrases")
        
        phrases = [
            "I'm in pain",
            "can i have some water?",
            "my feet are cold",
            "I feel cold",
            "I feel hot",
            "can you call my wife please?",
            "can you put the back of the bed down a little bit?"
        ]
        
        for i, phrase in enumerate(phrases, 1):
            st.markdown(f"""
            <div class='phrase-box'>
                <strong>{i}.</strong> "{phrase}"
            </div>
            """, unsafe_allow_html=True)
        
        # Instructions
        st.markdown("## 📋 Instructions")
        st.markdown("""
        1. **Position yourself** in front of the webcam
        2. **Adjust duration** using the slider (1-5 seconds)
        3. **Click "Start Recording"** and wait for countdown
        4. **Mouth one of the ICU phrases** clearly
        5. **Click "Run Lipreading"** to get results
        6. **Use "Retry"** if you want to record again
        """)
        
        # Tips
        st.markdown("## 💡 Tips")
        st.markdown("""
        - **Good lighting** improves accuracy
        - **Face the camera** directly
        - **Mouth words clearly** without sound
        - **Try different phrases** to test accuracy
        - **Use 3-4 seconds** for best results
        """)

if __name__ == "__main__":
    main()
