#!/usr/bin/env python3

"""
🧪 Test Chaplin Setup
Quick test to verify the installation and basic functionality
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV: {e}")
        return False
    
    try:
        import streamlit
        print(f"✅ Streamlit {streamlit.__version__}")
    except ImportError as e:
        print(f"❌ Streamlit: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy: {e}")
        return False
    
    try:
        from sklearn import __version__ as sklearn_version
        print(f"✅ Scikit-learn {sklearn_version}")
    except ImportError as e:
        print(f"❌ Scikit-learn: {e}")
        return False
    
    return True

def test_webcam():
    """Test webcam access"""
    print("\n🎥 Testing webcam access...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✅ Webcam accessible and working")
                print(f"📹 Frame shape: {frame.shape}")
            else:
                print("⚠️ Webcam accessible but no frame captured")
            cap.release()
            return True
        else:
            print("❌ Cannot access webcam")
            return False
    except Exception as e:
        print(f"❌ Webcam test error: {e}")
        return False

def test_model_files():
    """Test if model files exist"""
    print("\n📁 Testing model files...")
    
    model_dir = Path("chaplin_models")
    
    if not model_dir.exists():
        print("⚠️ Model directory doesn't exist - run 'make model'")
        return False
    
    expected_files = ["config.json"]
    optional_files = ["LRS3_V_WER19.1.pth", "model.pth"]
    
    all_good = True
    
    for file in expected_files:
        file_path = model_dir / file
        if file_path.exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} missing")
            all_good = False
    
    for file in optional_files:
        file_path = model_dir / file
        if file_path.exists():
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} not found (optional)")
    
    return all_good

def test_modules():
    """Test our custom modules"""
    print("\n🔧 Testing custom modules...")
    
    try:
        from chaplin_inference import ChaplinInference
        print("✅ chaplin_inference module")
    except ImportError as e:
        print(f"❌ chaplin_inference: {e}")
        return False
    
    try:
        from icu_phrases import ICUPhraseMapper
        print("✅ icu_phrases module")
    except ImportError as e:
        print(f"❌ icu_phrases: {e}")
        return False
    
    # Test ICU phrase mapper
    try:
        mapper = ICUPhraseMapper()
        result = mapper.find_best_match("i feel hot")
        print(f"✅ ICU phrase mapping: '{result['phrase']}' (similarity: {result['similarity']:.3f})")
    except Exception as e:
        print(f"❌ ICU phrase mapping error: {e}")
        return False
    
    return True

def test_directories():
    """Test if required directories exist"""
    print("\n📁 Testing directory structure...")
    
    required_dirs = ["outputs", "temp"]
    optional_dirs = ["chaplin_repo", "chaplin_models"]
    
    all_good = True
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ missing")
            all_good = False
    
    for dir_name in optional_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"✅ {dir_name}/")
        else:
            print(f"⚠️ {dir_name}/ not found (will be created)")
    
    return all_good

def main():
    """Run all tests"""
    print("🧠 Chaplin Setup Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Webcam", test_webcam),
        ("Model Files", test_model_files),
        ("Custom Modules", test_modules),
        ("Directories", test_directories)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Ready to use Chaplin lipreading.")
        print("\n🚀 Next steps:")
        print("1. Run 'make live' to start the web interface")
        print("2. Open http://localhost:8501 in your browser")
        print("3. Start recording and testing!")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please check the setup.")
        print("\n🔧 Troubleshooting:")
        if not results.get("Imports", True):
            print("- Run 'make setup' to install dependencies")
        if not results.get("Model Files", True):
            print("- Run 'make model' to download model files")
        if not results.get("Webcam", True):
            print("- Check webcam permissions and connections")

if __name__ == "__main__":
    main()
