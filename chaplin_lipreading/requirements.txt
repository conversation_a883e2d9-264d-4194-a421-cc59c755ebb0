# 🧠 Chaplin Lipreading Requirements

# Core ML frameworks
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# ESPnet for lipreading
espnet>=202301

# Computer vision
opencv-python>=4.8.0

# Web interface
streamlit>=1.28.0

# Video processing
ffmpeg-python>=0.2.0
moviepy>=1.0.3

# Scientific computing
numpy>=1.24.0
scikit-learn>=1.3.0

# Audio processing
soundfile>=0.12.0

# Utilities
tqdm>=4.65.0
Pillow>=10.0.0
matplotlib>=3.7.0

# Optional: GPU support (uncomment if you have CUDA)
# torch>=2.0.0+cu118 --index-url https://download.pytorch.org/whl/cu118
# torchvision>=0.15.0+cu118 --index-url https://download.pytorch.org/whl/cu118
# torchaudio>=2.0.0+cu118 --index-url https://download.pytorch.org/whl/cu118
