#!/usr/bin/env python3

"""
🏥 ICU Phrase Mapper
Maps lipreading predictions to specific ICU phrases using similarity matching
"""

import re
from difflib import SequenceMatcher
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

class ICUPhraseMapper:
    """Maps predictions to ICU phrases using multiple similarity methods"""
    
    def __init__(self):
        self.icu_phrases = [
            "I'm in pain",
            "can i have some water?",
            "my feet are cold",
            "I feel cold",
            "I feel hot",
            "can you call my wife please?",
            "can you put the back of the bed down a little bit?"
        ]
        
        # Preprocess phrases for better matching
        self.processed_phrases = [self.preprocess_text(phrase) for phrase in self.icu_phrases]
        
        # Initialize TF-IDF vectorizer
        self.vectorizer = TfidfVectorizer(
            lowercase=True,
            stop_words=None,  # Keep all words for medical context
            ngram_range=(1, 2),  # Use unigrams and bigrams
            analyzer='word'
        )
        
        # Fit vectorizer on ICU phrases
        self.phrase_vectors = self.vectorizer.fit_transform(self.processed_phrases)
        
        print(f"🏥 Loaded {len(self.icu_phrases)} ICU phrases")
    
    def preprocess_text(self, text):
        """Preprocess text for better matching"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove punctuation except apostrophes
        text = re.sub(r"[^\w\s']", "", text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def calculate_string_similarity(self, text1, text2):
        """Calculate string similarity using SequenceMatcher"""
        return SequenceMatcher(None, text1, text2).ratio()
    
    def calculate_tfidf_similarity(self, prediction):
        """Calculate TF-IDF cosine similarity"""
        try:
            # Transform prediction
            pred_vector = self.vectorizer.transform([prediction])
            
            # Calculate cosine similarity
            similarities = cosine_similarity(pred_vector, self.phrase_vectors)[0]
            
            return similarities
        except Exception as e:
            print(f"⚠️ TF-IDF similarity error: {e}")
            return np.zeros(len(self.icu_phrases))
    
    def calculate_keyword_similarity(self, prediction):
        """Calculate similarity based on key medical words"""
        # Define key medical terms and their weights
        medical_keywords = {
            'pain': ['pain', 'hurt', 'ache', 'sore'],
            'water': ['water', 'drink', 'thirsty', 'liquid'],
            'cold': ['cold', 'freeze', 'freezing', 'chilly'],
            'hot': ['hot', 'warm', 'heat', 'burning'],
            'feet': ['feet', 'foot', 'toes'],
            'wife': ['wife', 'spouse', 'partner'],
            'bed': ['bed', 'mattress', 'pillow'],
            'call': ['call', 'phone', 'contact'],
            'back': ['back', 'spine'],
            'down': ['down', 'lower', 'decrease']
        }
        
        prediction_words = set(prediction.lower().split())
        similarities = []
        
        for phrase in self.processed_phrases:
            phrase_words = set(phrase.split())
            
            # Calculate keyword overlap
            overlap_score = 0
            total_keywords = 0
            
            for category, keywords in medical_keywords.items():
                phrase_has_keyword = any(kw in phrase_words for kw in keywords)
                pred_has_keyword = any(kw in prediction_words for kw in keywords)
                
                if phrase_has_keyword:
                    total_keywords += 1
                    if pred_has_keyword:
                        overlap_score += 1
            
            # Calculate similarity as ratio of matched keywords
            if total_keywords > 0:
                similarity = overlap_score / total_keywords
            else:
                similarity = 0
            
            similarities.append(similarity)
        
        return np.array(similarities)
    
    def find_best_match(self, prediction, method='combined'):
        """Find the best matching ICU phrase"""
        if not prediction or not prediction.strip():
            # Return default phrase for empty predictions
            return {
                'phrase': self.icu_phrases[0],
                'similarity': 0.0,
                'method': 'default'
            }
        
        processed_prediction = self.preprocess_text(prediction)
        
        if method == 'string':
            # String similarity only
            similarities = [
                self.calculate_string_similarity(processed_prediction, phrase)
                for phrase in self.processed_phrases
            ]
        
        elif method == 'tfidf':
            # TF-IDF similarity only
            similarities = self.calculate_tfidf_similarity(processed_prediction)
        
        elif method == 'keyword':
            # Keyword similarity only
            similarities = self.calculate_keyword_similarity(processed_prediction)
        
        else:  # combined
            # Combine multiple similarity methods
            string_sims = np.array([
                self.calculate_string_similarity(processed_prediction, phrase)
                for phrase in self.processed_phrases
            ])
            
            tfidf_sims = self.calculate_tfidf_similarity(processed_prediction)
            keyword_sims = self.calculate_keyword_similarity(processed_prediction)
            
            # Weighted combination
            similarities = (
                0.4 * string_sims +
                0.3 * tfidf_sims +
                0.3 * keyword_sims
            )
        
        # Find best match
        best_idx = np.argmax(similarities)
        best_similarity = similarities[best_idx]
        best_phrase = self.icu_phrases[best_idx]
        
        return {
            'phrase': best_phrase,
            'similarity': float(best_similarity),
            'method': method,
            'all_similarities': similarities.tolist() if hasattr(similarities, 'tolist') else similarities
        }
    
    def get_top_matches(self, prediction, top_k=3):
        """Get top K matching ICU phrases"""
        result = self.find_best_match(prediction, method='combined')
        similarities = result['all_similarities']
        
        # Get top K indices
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        top_matches = []
        for idx in top_indices:
            top_matches.append({
                'phrase': self.icu_phrases[idx],
                'similarity': similarities[idx]
            })
        
        return top_matches
    
    def print_all_phrases(self):
        """Print all available ICU phrases"""
        print("🏥 Available ICU Phrases:")
        print("-" * 40)
        for i, phrase in enumerate(self.icu_phrases, 1):
            print(f"{i}. \"{phrase}\"")
        print("-" * 40)

# Test function
def test_mapper():
    """Test the ICU phrase mapper"""
    mapper = ICUPhraseMapper()
    
    test_predictions = [
        "i feel hot",
        "my feet cold",
        "water please",
        "call wife",
        "pain",
        "bed down",
        "i am cold"
    ]
    
    print("\n🧪 Testing ICU Phrase Mapper")
    print("=" * 50)
    
    for prediction in test_predictions:
        result = mapper.find_best_match(prediction)
        print(f"Input: \"{prediction}\"")
        print(f"Match: \"{result['phrase']}\" (similarity: {result['similarity']:.3f})")
        print("-" * 30)

if __name__ == "__main__":
    test_mapper()
