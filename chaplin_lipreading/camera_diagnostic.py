#!/usr/bin/env python3

"""
🔍 Camera Diagnostic Tool
Comprehensive camera testing for macOS
"""

import cv2
import subprocess
import sys
import platform

def check_system_info():
    """Check system information"""
    print("🖥️  SYSTEM INFORMATION")
    print("=" * 50)
    print(f"OS: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    print(f"OpenCV: {cv2.__version__}")
    print()

def check_camera_permissions():
    """Check camera permissions on macOS"""
    print("🔐 CAMERA PERMISSIONS")
    print("=" * 50)
    
    try:
        # Check if we can list video devices
        result = subprocess.run(['system_profiler', 'SPCameraDataType'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ System can access camera hardware")
            if "Built-in" in result.stdout or "Camera" in result.stdout:
                print("✅ Built-in camera detected")
            else:
                print("⚠️ No built-in camera found in system profiler")
        else:
            print("❌ Cannot access camera hardware info")
            
    except Exception as e:
        print(f"⚠️ Could not check system camera info: {e}")
    
    print()

def test_opencv_backends():
    """Test different OpenCV backends"""
    print("🔧 OPENCV BACKEND TESTING")
    print("=" * 50)
    
    backends = [
        (cv2.CAP_ANY, "CAP_ANY (default)"),
        (cv2.CAP_AVFOUNDATION, "CAP_AVFOUNDATION (macOS)"),
        (cv2.CAP_QT, "CAP_QT"),
        (cv2.CAP_GSTREAMER, "CAP_GSTREAMER"),
    ]
    
    working_backends = []
    
    for backend_id, backend_name in backends:
        try:
            print(f"Testing {backend_name}...")
            cap = cv2.VideoCapture(0, backend_id)
            
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    print(f"✅ {backend_name}: Working (frame: {frame.shape})")
                    working_backends.append((backend_id, backend_name))
                else:
                    print(f"⚠️ {backend_name}: Opens but no frame")
                cap.release()
            else:
                print(f"❌ {backend_name}: Cannot open")
                
        except Exception as e:
            print(f"❌ {backend_name}: Error - {e}")
    
    print(f"\n✅ Working backends: {len(working_backends)}")
    return working_backends

def test_camera_indices():
    """Test different camera indices"""
    print("\n📹 CAMERA INDEX TESTING")
    print("=" * 50)
    
    working_cameras = []
    
    for i in range(5):  # Test indices 0-4
        try:
            print(f"Testing camera index {i}...")
            cap = cv2.VideoCapture(i)
            
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    print(f"✅ Camera {i}: Working (frame: {frame.shape})")
                    working_cameras.append(i)
                else:
                    print(f"⚠️ Camera {i}: Opens but no frame")
                cap.release()
            else:
                print(f"❌ Camera {i}: Cannot open")
                
        except Exception as e:
            print(f"❌ Camera {i}: Error - {e}")
    
    print(f"\n✅ Working cameras: {working_cameras}")
    return working_cameras

def test_camera_properties():
    """Test camera properties"""
    print("\n⚙️ CAMERA PROPERTIES")
    print("=" * 50)
    
    try:
        cap = cv2.VideoCapture(0, cv2.CAP_AVFOUNDATION)
        
        if not cap.isOpened():
            print("❌ Cannot open camera for property testing")
            return
        
        properties = [
            (cv2.CAP_PROP_FRAME_WIDTH, "Width"),
            (cv2.CAP_PROP_FRAME_HEIGHT, "Height"),
            (cv2.CAP_PROP_FPS, "FPS"),
            (cv2.CAP_PROP_FOURCC, "FOURCC"),
            (cv2.CAP_PROP_BACKEND, "Backend"),
        ]
        
        for prop_id, prop_name in properties:
            value = cap.get(prop_id)
            print(f"{prop_name}: {value}")
        
        cap.release()
        
    except Exception as e:
        print(f"❌ Property test failed: {e}")

def test_video_recording():
    """Test video recording"""
    print("\n🎬 VIDEO RECORDING TEST")
    print("=" * 50)
    
    try:
        cap = cv2.VideoCapture(0, cv2.CAP_AVFOUNDATION)
        
        if not cap.isOpened():
            print("❌ Cannot open camera for recording test")
            return False
        
        # Set properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        # Get actual properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"Recording at: {width}x{height} @ {fps}fps")
        
        # Test recording 1 second
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter('test_recording.mp4', fourcc, fps, (width, height))
        
        frames_recorded = 0
        for i in range(int(fps)):  # 1 second
            ret, frame = cap.read()
            if ret:
                out.write(frame)
                frames_recorded += 1
            else:
                print(f"❌ Frame {i} failed")
                break
        
        cap.release()
        out.release()
        
        print(f"✅ Recorded {frames_recorded} frames")
        return True
        
    except Exception as e:
        print(f"❌ Recording test failed: {e}")
        return False

def main():
    """Run all diagnostics"""
    print("🔍 CAMERA DIAGNOSTIC TOOL")
    print("=" * 50)
    print()
    
    # System info
    check_system_info()
    
    # Permissions
    check_camera_permissions()
    
    # Backend testing
    working_backends = test_opencv_backends()
    
    # Camera indices
    working_cameras = test_camera_indices()
    
    # Properties
    test_camera_properties()
    
    # Recording test
    recording_works = test_video_recording()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    print(f"Working backends: {len(working_backends)}")
    print(f"Working cameras: {len(working_cameras)}")
    print(f"Recording test: {'✅ PASS' if recording_works else '❌ FAIL'}")
    
    if working_backends and working_cameras and recording_works:
        print("\n🎉 Camera system is working correctly!")
        print("The issue is likely with Streamlit browser permissions.")
        print("\nNext steps:")
        print("1. Open http://localhost:8501 in your browser")
        print("2. Click 'Start Recording'")
        print("3. Allow camera access when prompted")
        print("4. If still not working, try a different browser")
    else:
        print("\n❌ Camera system has issues.")
        print("Please check macOS camera permissions.")

if __name__ == "__main__":
    main()
