#!/usr/bin/env python3

"""
🎥 Chaplin Live CLI
Command-line interface for webcam lipreading using Chaplin model
"""

import cv2
import time
import argparse
import numpy as np
from pathlib import Path
from datetime import datetime
import sys
import os

# Add chaplin repo to path
sys.path.append('chaplin_repo')

from chaplin_inference import ChaplinInference
from icu_phrases import ICUPhraseMapper

class WebcamRecorder:
    """Handles webcam recording for lipreading"""
    
    def __init__(self):
        self.cap = None
        self.fps = 25  # Standard fps for lipreading
        
    def initialize_camera(self):
        """Initialize the webcam"""
        print("🎥 Initializing webcam...")
        self.cap = cv2.VideoCapture(0)
        
        if not self.cap.isOpened():
            raise RuntimeError("❌ Could not open webcam")
        
        # Set camera properties
        self.cap.set(cv2.CAP_PROP_FPS, self.fps)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        print("✅ Webcam initialized")
    
    def record_video(self, duration=3, output_path="temp_record.mp4"):
        """Record video from webcam"""
        if self.cap is None:
            self.initialize_camera()
        
        # Get actual fps
        actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
        if actual_fps <= 0:
            actual_fps = self.fps
        
        # Calculate total frames
        total_frames = int(duration * actual_fps)
        
        # Set up video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        out = cv2.VideoWriter(output_path, fourcc, actual_fps, (width, height))
        
        print(f"🎬 Recording {duration}s video...")
        print("📍 Position yourself in front of the camera and start mouthing the phrase")
        
        # Countdown
        for i in range(3, 0, -1):
            print(f"⏰ Starting in {i}...")
            time.sleep(1)
        
        print("🔴 RECORDING!")
        
        frames_recorded = 0
        start_time = time.time()
        
        while frames_recorded < total_frames:
            ret, frame = self.cap.read()
            if not ret:
                print("❌ Failed to capture frame")
                break
            
            # Flip frame horizontally for mirror effect
            frame = cv2.flip(frame, 1)
            
            # Add recording indicator
            cv2.circle(frame, (30, 30), 10, (0, 0, 255), -1)
            cv2.putText(frame, "REC", (50, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # Show progress
            progress = frames_recorded / total_frames
            cv2.rectangle(frame, (10, height-30), (int((width-20) * progress), height-10), (0, 255, 0), -1)
            
            # Write frame
            out.write(frame)
            
            # Display frame
            cv2.imshow('Chaplin Recorder', frame)
            
            frames_recorded += 1
            
            # Break on 'q' key
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        # Cleanup
        out.release()
        cv2.destroyAllWindows()
        
        elapsed = time.time() - start_time
        print(f"✅ Recording complete! {frames_recorded} frames in {elapsed:.1f}s")
        print(f"📁 Saved to: {output_path}")
        
        return output_path
    
    def cleanup(self):
        """Release camera resources"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()

def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(description="🧠 Chaplin Live Lipreading")
    parser.add_argument("--duration", "-d", type=float, default=3.0, 
                       help="Recording duration in seconds (default: 3.0)")
    parser.add_argument("--output", "-o", type=str, default="temp_record.mp4",
                       help="Output video file (default: temp_record.mp4)")
    parser.add_argument("--no-record", action="store_true",
                       help="Skip recording, use existing video file")
    
    args = parser.parse_args()
    
    print("🧠 Chaplin Live Lipreading CLI")
    print("=" * 40)
    
    try:
        # Initialize components
        print("🔧 Initializing Chaplin inference...")
        chaplin = ChaplinInference()
        
        print("🏥 Loading ICU phrase mapper...")
        icu_mapper = ICUPhraseMapper()
        
        # Record video (unless skipped)
        if not args.no_record:
            recorder = WebcamRecorder()
            try:
                video_path = recorder.record_video(args.duration, args.output)
            finally:
                recorder.cleanup()
        else:
            video_path = args.output
            if not Path(video_path).exists():
                print(f"❌ Video file not found: {video_path}")
                return
        
        # Run inference
        print("🧠 Running Chaplin inference...")
        start_time = time.time()
        
        raw_prediction = chaplin.predict(video_path)
        
        inference_time = time.time() - start_time
        
        # Map to ICU phrases
        icu_result = icu_mapper.find_best_match(raw_prediction)
        
        # Display results
        print("\n" + "="*50)
        print("🎯 RESULTS")
        print("="*50)
        print(f"📝 Raw output: \"{raw_prediction}\"")
        print(f"🏥 Closest ICU phrase: \"{icu_result['phrase']}\" (similarity {icu_result['similarity']:.3f})")
        print(f"⏱️ Processing time: {inference_time:.2f}s")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"outputs/result_{timestamp}.txt"
        
        os.makedirs("outputs", exist_ok=True)
        with open(results_file, 'w') as f:
            f.write(f"Chaplin Lipreading Result - {timestamp}\n")
            f.write(f"Video: {video_path}\n")
            f.write(f"Raw prediction: {raw_prediction}\n")
            f.write(f"ICU phrase: {icu_result['phrase']}\n")
            f.write(f"Similarity: {icu_result['similarity']:.3f}\n")
            f.write(f"Processing time: {inference_time:.2f}s\n")
        
        print(f"💾 Results saved to: {results_file}")
        
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
