#!/usr/bin/env python3

"""
🎥 Simple Camera Test for Streamlit
Direct camera access test without Streamlit components
"""

import cv2
import streamlit as st
import numpy as np
import time
from pathlib import Path

def test_direct_camera():
    """Test direct camera access"""
    st.title("🎥 Direct Camera Test")
    
    if st.button("Test Camera Access"):
        with st.spinner("Testing camera..."):
            try:
                # Test camera access
                cap = cv2.VideoCapture(0)
                
                if not cap.isOpened():
                    st.error("❌ Camera could not be opened")
                    return
                
                st.success("✅ Camera opened successfully")
                
                # Capture a frame
                ret, frame = cap.read()
                if ret:
                    st.success(f"✅ Frame captured: {frame.shape}")
                    
                    # Convert BGR to RGB for display
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    
                    # Display the frame
                    st.image(frame_rgb, caption="Camera Test Frame", use_container_width=True)
                    
                    # Save test frame
                    test_path = Path("temp/camera_test.jpg")
                    test_path.parent.mkdir(exist_ok=True)
                    cv2.imwrite(str(test_path), frame)
                    st.success(f"✅ Frame saved to: {test_path}")
                    
                else:
                    st.error("❌ Could not capture frame")
                
                cap.release()
                
            except Exception as e:
                st.error(f"❌ Camera test failed: {e}")

def test_video_recording():
    """Test video recording"""
    st.title("🎬 Video Recording Test")
    
    duration = st.slider("Recording Duration (seconds)", 1, 5, 3)
    
    if st.button("Test Video Recording"):
        with st.spinner(f"Recording {duration}s video..."):
            try:
                cap = cv2.VideoCapture(0)
                
                if not cap.isOpened():
                    st.error("❌ Camera not available")
                    return
                
                # Set up video writer
                fps = 30
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                output_path = Path("temp/test_recording.mp4")
                output_path.parent.mkdir(exist_ok=True)
                
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
                
                st.info(f"📹 Recording {duration}s at {width}x{height} @ {fps}fps")
                
                # Record video
                start_time = time.time()
                frame_count = 0
                
                progress_bar = st.progress(0)
                
                while time.time() - start_time < duration:
                    ret, frame = cap.read()
                    if ret:
                        out.write(frame)
                        frame_count += 1
                        
                        # Update progress
                        progress = (time.time() - start_time) / duration
                        progress_bar.progress(min(progress, 1.0))
                    else:
                        st.error("❌ Frame capture failed")
                        break
                
                cap.release()
                out.release()
                
                st.success(f"✅ Recording complete! {frame_count} frames saved to: {output_path}")
                
                # Show file info
                if output_path.exists():
                    file_size = output_path.stat().st_size / 1024 / 1024  # MB
                    st.info(f"📁 File size: {file_size:.2f} MB")
                
            except Exception as e:
                st.error(f"❌ Recording failed: {e}")

def main():
    """Main app"""
    st.set_page_config(
        page_title="Camera Test",
        page_icon="🎥",
        layout="wide"
    )
    
    st.sidebar.title("🧪 Camera Tests")
    
    test_mode = st.sidebar.selectbox(
        "Select Test",
        ["Direct Camera Test", "Video Recording Test"]
    )
    
    if test_mode == "Direct Camera Test":
        test_direct_camera()
    elif test_mode == "Video Recording Test":
        test_video_recording()
    
    # System info
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 🔧 System Info")
    st.sidebar.text(f"OpenCV: {cv2.__version__}")
    
    # Instructions
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📋 Instructions")
    st.sidebar.markdown("""
    1. **Direct Test**: Tests basic camera access
    2. **Recording Test**: Tests video recording
    3. Make sure camera permissions are enabled
    4. Close other apps using the camera
    """)

if __name__ == "__main__":
    main()
