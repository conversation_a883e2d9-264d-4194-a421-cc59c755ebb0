#!/usr/bin/env python3

"""
🎥 Camera Test for macOS
Test camera access and permissions
"""

import cv2
import sys
import time

def test_camera_detailed():
    """Detailed camera test"""
    print("🎥 Testing MacBook Camera Access")
    print("=" * 40)
    
    # Test different camera indices
    for camera_id in range(3):
        print(f"\n📹 Testing camera index {camera_id}...")
        
        try:
            cap = cv2.VideoCapture(camera_id)
            
            if cap.isOpened():
                print(f"✅ Camera {camera_id} opened successfully")
                
                # Try to read a frame
                ret, frame = cap.read()
                if ret:
                    print(f"✅ Frame captured: {frame.shape}")
                    
                    # Show frame for 2 seconds
                    cv2.imshow(f'Camera {camera_id} Test', frame)
                    print("📺 Displaying frame for 2 seconds...")
                    cv2.waitKey(2000)  # Wait 2 seconds
                    cv2.destroyAllWindows()
                    
                    cap.release()
                    print(f"🎯 Camera {camera_id} working perfectly!")
                    return camera_id
                else:
                    print(f"❌ Camera {camera_id} opened but no frame captured")
            else:
                print(f"❌ Camera {camera_id} could not be opened")
            
            cap.release()
            
        except Exception as e:
            print(f"❌ Error with camera {camera_id}: {e}")
    
    print("\n❌ No working cameras found")
    return None

def test_camera_permissions():
    """Test camera permissions"""
    print("\n🔐 Testing Camera Permissions")
    print("-" * 30)
    
    try:
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Camera permission denied or camera not available")
            print("\n🔧 Troubleshooting steps:")
            print("1. Go to System Preferences > Security & Privacy > Camera")
            print("2. Make sure Terminal (or your IDE) has camera access")
            print("3. Try running: sudo python test_camera.py")
            return False
        
        # Set properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        # Get actual properties
        width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
        height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"📊 Camera properties:")
        print(f"   Resolution: {int(width)}x{int(height)}")
        print(f"   FPS: {fps}")
        
        # Test frame capture
        for i in range(5):
            ret, frame = cap.read()
            if ret:
                print(f"✅ Frame {i+1}/5 captured successfully")
            else:
                print(f"❌ Frame {i+1}/5 failed")
                break
        
        cap.release()
        return True
        
    except Exception as e:
        print(f"❌ Permission test error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 MacBook Camera Diagnostic")
    print("=" * 50)
    
    # Test 1: Find working camera
    working_camera = test_camera_detailed()
    
    # Test 2: Check permissions
    permissions_ok = test_camera_permissions()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    if working_camera is not None:
        print(f"✅ Working camera found at index: {working_camera}")
    else:
        print("❌ No working camera found")
    
    if permissions_ok:
        print("✅ Camera permissions OK")
    else:
        print("❌ Camera permission issues")
    
    if working_camera is not None and permissions_ok:
        print("\n🎉 Camera is ready for lipreading!")
        print("You can now use the Streamlit app or CLI mode.")
    else:
        print("\n🔧 Camera issues detected. Please check:")
        print("1. Camera permissions in System Preferences")
        print("2. No other apps using the camera")
        print("3. Camera hardware is working")

if __name__ == "__main__":
    main()
