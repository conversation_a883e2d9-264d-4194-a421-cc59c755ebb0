#!/usr/bin/env python3

"""
🧠 Chaplin Model Downloader
Downloads the pretrained LRS3_V_WER19.1 model for lipreading
"""

import os
import urllib.request
import sys
from pathlib import Path

def download_file(url, filename, description="file"):
    """Download a file with progress bar"""
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, (downloaded * 100) // total_size)
            bar_length = 40
            filled_length = (percent * bar_length) // 100
            bar = '█' * filled_length + '-' * (bar_length - filled_length)
            print(f'\r📥 {description}: |{bar}| {percent}% ({downloaded//1024//1024}MB)', end='')
        else:
            print(f'\r📥 {description}: {downloaded//1024//1024}MB downloaded', end='')
    
    try:
        urllib.request.urlretrieve(url, filename, progress_hook)
        print()  # New line after progress bar
        return True
    except Exception as e:
        print(f"\n❌ Error downloading {description}: {e}")
        return False

def main():
    """Download the Chaplin model"""
    print("🧠 Chaplin Model Downloader")
    print("=" * 50)
    
    # Create model directory
    model_dir = Path("chaplin_models")
    model_dir.mkdir(exist_ok=True)
    
    # Model URLs (these are example URLs - you'll need to update with actual Chaplin model URLs)
    models = {
        "LRS3_V_WER19.1.pth": {
            "url": "https://example.com/models/LRS3_V_WER19.1.pth",  # Update with actual URL
            "description": "LRS3 Visual Model"
        },
        "config.json": {
            "url": "https://example.com/models/config.json",  # Update with actual URL
            "description": "Model Configuration"
        }
    }
    
    # Check if we have the model files from the existing setup
    existing_model_dir = Path("../LRS3_V_WER19.1_files")
    if existing_model_dir.exists():
        print("✅ Found existing model files, copying...")
        
        # Copy existing model files
        import shutil
        for file in existing_model_dir.glob("*"):
            dest = model_dir / file.name
            if not dest.exists():
                shutil.copy2(file, dest)
                print(f"✅ Copied {file.name}")
        
        print("✅ Model setup complete!")
        return
    
    # Download models
    success_count = 0
    for filename, info in models.items():
        filepath = model_dir / filename
        
        if filepath.exists():
            print(f"✅ {filename} already exists")
            success_count += 1
            continue
        
        print(f"📥 Downloading {filename}...")
        if download_file(info["url"], filepath, info["description"]):
            print(f"✅ {filename} downloaded successfully")
            success_count += 1
        else:
            print(f"❌ Failed to download {filename}")
    
    if success_count == len(models):
        print("\n🎉 All models downloaded successfully!")
    else:
        print(f"\n⚠️ Downloaded {success_count}/{len(models)} models")
        print("Some models may need to be downloaded manually")
    
    # Create a simple config if it doesn't exist
    config_path = model_dir / "config.json"
    if not config_path.exists():
        import json
        config = {
            "model_name": "LRS3_V_WER19.1",
            "input_size": [224, 224],
            "sequence_length": 75,
            "vocab_size": 41,
            "architecture": "transformer"
        }
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        print("✅ Created default config.json")

if __name__ == "__main__":
    main()
