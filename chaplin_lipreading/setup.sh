#!/bin/bash

# 🧠 Chaplin Lipreading Setup Script
# Sets up the complete environment for local webcam lipreading

set -e

echo "🚀 Setting up Chaplin Lipreading Environment..."

# Check if we're on macOS or Linux
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Detected macOS"
    PLATFORM="macos"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🐧 Detected Linux"
    PLATFORM="linux"
else
    echo "❌ Unsupported platform: $OSTYPE"
    exit 1
fi

# Install system dependencies
echo "📦 Installing system dependencies..."
if [[ "$PLATFORM" == "macos" ]]; then
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    # Install dependencies
    brew install python@3.10 git ffmpeg || true
    
    # Set Python path
    export PATH="/opt/homebrew/bin:$PATH"
    PYTHON_CMD="python3.10"
    
elif [[ "$PLATFORM" == "linux" ]]; then
    sudo apt update
    sudo apt install -y python3.10 python3.10-venv python3.10-dev git ffmpeg
    PYTHON_CMD="python3.10"
fi

# Verify Python installation
if ! command -v $PYTHON_CMD &> /dev/null; then
    echo "❌ Python 3.10 not found. Please install Python 3.10 manually."
    exit 1
fi

echo "✅ Python version: $($PYTHON_CMD --version)"

# Create virtual environment
echo "🐍 Creating virtual environment..."
if [ ! -d ".venv" ]; then
    $PYTHON_CMD -m venv .venv
fi

# Activate virtual environment
source .venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install PyTorch (CPU version for compatibility)
echo "🔥 Installing PyTorch..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# Install other dependencies
echo "📚 Installing Python dependencies..."
pip install \
    espnet \
    opencv-python \
    streamlit \
    ffmpeg-python \
    numpy \
    moviepy \
    soundfile \
    tqdm \
    scikit-learn \
    Pillow \
    matplotlib

# Clone Chaplin repository
echo "📥 Cloning Chaplin repository..."
if [ ! -d "chaplin_repo" ]; then
    git clone https://github.com/amanvirparhar/chaplin.git chaplin_repo
    echo "✅ Chaplin repository cloned"
else
    echo "ℹ️ Chaplin repository already exists"
fi

# Create model directory
echo "📁 Creating model directory..."
mkdir -p chaplin_models

# Create outputs directory
mkdir -p outputs

# Create temp directory
mkdir -p temp

echo "✅ Setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Run 'make model' to download the pretrained model"
echo "2. Run 'make live' to start the Streamlit app"
echo "3. Open http://localhost:8501 in your browser"
echo ""
echo "💡 Available commands:"
echo "  make setup  - Run this setup script"
echo "  make model  - Download pretrained model"
echo "  make live   - Start Streamlit app"
echo "  make test   - CLI webcam test"
