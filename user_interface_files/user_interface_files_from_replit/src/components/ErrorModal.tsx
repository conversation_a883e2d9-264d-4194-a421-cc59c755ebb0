import { FC } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface ErrorModalProps {
  message: string;
  onClose: () => void;
}

const ErrorModal: FC<ErrorModalProps> = ({ message, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg w-full max-w-md mx-auto overflow-hidden">
        <div className="bg-accent text-white p-4">
          <h2 className="text-xl font-bold">Error</h2>
        </div>
        
        <div className="p-6">
          <div className="flex items-center justify-center mb-4 text-accent">
            <AlertTriangle className="h-12 w-12" />
          </div>
          
          <p className="text-center mb-6">
            {message || "Unable to process your request. Please check your connection and try again."}
          </p>
          
          <Button
            onClick={onClose}
            className="w-full bg-primary text-white font-medium py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ErrorModal;
